const { createLogger } = require('../logger');
const {
  formatDate,
  formatUserInfo,
  formatMetadata,
  withErrorHandling,
} = require('./commonFormatters');

const logger = createLogger('familyDataFormatter');

/**
 * Formats family member data into a readable string
 * @param {Object} member - Family member data
 * @returns {string} Formatted family member info
 */
function formatFamilyMember(member) {
  if (!member) return 'No member data';

  return (
    `- **${member.name || 'Unnamed Member'}**
` +
    (member.relationship
      ? `  - Relationship: ${member.relationship}
`
      : '') +
    (member.dateOfBirth
      ? `  - Date of Birth: ${formatDate(member.dateOfBirth)}
`
      : '') +
    (member.gender
      ? `  - Gender: ${member.gender}
`
      : '') +
    (member.phone
      ? `  - Phone: ${member.phone}
`
      : '') +
    (member.email
      ? `  - Email: ${member.email}
`
      : '')
  );
}

/**
 * Formats family event data into a readable string
 * @param {Object} event - Family event data
 * @returns {string} Formatted event info
 */
function formatFamilyEvent(event) {
  if (!event) return 'No event data';

  return (
    `- **${event.name || 'Unnamed Event'}**
` +
    (event.startDateTime
      ? `  - When: ${formatDate(event.startDateTime)}` +
        (event.endDateTime ? ` to ${formatDate(event.endDateTime)}\n` : '\n')
      : '') +
    (event.description ? `  - ${event.description}\n` : '') +
    (event.location ? `  - Location: ${event.location}\n` : '')
  );
}

/**
 * Converts family query data into a meaningful summary
 * @param {Object} data - Raw family data from the query
 * @returns {string} Formatted summary of the family data
 */
function formatFamilyData(data) {
  if (!data) {
    logger.warn('No family data provided to formatter');
    return 'No family data available';
  }

  return withErrorHandling(
    (data) => {
      // If data is already in the new format (from fetchFamilyData), use it directly
      if (data._metadata?.dataSource === 'family') {
        return formatStructuredFamilyData(data);
      }

      // Legacy format handling (kept for backward compatibility)
      return formatLegacyFamilyData(data);
    },
    data,
    'Error processing family data. Please try again later.',
    'familyDataFormatter'
  );
}

/**
 * Formats family data in the new structured format
 * @param {Object} data - Structured family data
 * @returns {string} Formatted family data
 */
function formatStructuredFamilyData(data) {
  let summary = '';

  // User Information
  if (data.user) {
    summary += formatUserInfo(
      {
        givenName: data.user.givenName,
        familyName: data.user.familyName,
        id: data.user.id,
      },
      'Family Information'
    );
  }

  // Family Members
  if (data.members?.length > 0) {
    summary += `## Family Members (${data.members.length})\n`;
    data.members.forEach((member) => {
      summary += formatFamilyMember(member);
    });
    summary += '\n';
  }

  // Upcoming Events
  if (data.upcomingEvents?.length > 0) {
    summary += `## Upcoming Family Events (${data.upcomingEvents.length})\n`;
    data.upcomingEvents.forEach((event) => {
      summary += formatFamilyEvent(event);
    });
    summary += '\n';
  }

  // Family Groups
  if (data.groups?.length > 0) {
    summary += `## Family Groups (${data.groups.length})\n`;
    data.groups.forEach((group) => {
      summary += `- **${group.name || 'Unnamed Group'}**\n`;
      if (group.description) {
        summary += `  - ${group.description}\n`;
      }
      if (group.memberCount > 0) {
        summary += `  - Members: ${group.memberCount}\n`;
      }
    });
    summary += '\n';
  }

  // Pending Tasks
  if (data.pendingTasks?.length > 0) {
    summary += `## Pending Family Tasks (${data.pendingTasks.length})\n`;
    data.pendingTasks.forEach((task) => {
      summary += `- **${task.title || 'Untitled Task'}**\n`;
      if (task.dueDate) {
        summary += `  - Due: ${formatDate(task.dueDate)}\n`;
      }
      if (task.assignedTo) {
        summary += `  - Assigned to: ${task.assignedTo.name || 'Unassigned'}\n`;
      }
    });
    summary += '\n';
  }

  // Add metadata
  summary += formatMetadata(data._metadata, data._metadata?.timestamp);

  return summary;
}

/**
 * Formats legacy family data structure
 * @param {Object} data - Legacy family data
 * @returns {string} Formatted family data
 */
function formatLegacyFamilyData(data) {
  let summary = '';

  // User Information
  summary += formatUserInfo(
    {
      givenName: data.givenName,
      familyName: data.familyName,
      id: data.id,
    },
    'Family Information'
  );

  // Process family members
  if (data.familyMembers?.items?.length > 0) {
    summary += `## Family Members (${data.familyMembers.items.length})\n`;
    data.familyMembers.items.forEach((member) => {
      summary += formatFamilyMember(member);
    });
    summary += '\n';
  }

  // Process family events
  if (data.familyEvents?.items?.length > 0) {
    const upcomingEvents = data.familyEvents.items.filter(
      (event) => new Date(event.startDateTime) > new Date()
    );

    if (upcomingEvents.length > 0) {
      summary += `## Upcoming Family Events (${upcomingEvents.length})\n`;
      upcomingEvents.forEach((event) => {
        summary += formatFamilyEvent(event);
      });
      summary += '\n';
    }
  }

  // Process family groups
  if (data.familyGroups?.items?.length > 0) {
    summary += `## Family Groups (${data.familyGroups.items.length})\n`;
    data.familyGroups.items.forEach((group) => {
      summary += `- **${group.name || 'Unnamed Group'}**\n`;
      if (group.description) {
        summary += `  - ${group.description}\n`;
      }
      if (group.members?.items?.length > 0) {
        summary += `  - Members: ${group.members.items.length}\n`;
      }
    });
    summary += '\n';
  }

  // Process family tasks
  if (data.familyTasks?.items?.length > 0) {
    const pendingTasks = data.familyTasks.items.filter((task) => !task.completed);

    if (pendingTasks.length > 0) {
      summary += `## Pending Family Tasks (${pendingTasks.length})\n`;
      pendingTasks.forEach((task) => {
        summary += `- **${task.title || 'Untitled Task'}**\n`;
        if (task.dueDate) {
          summary += `  - Due: ${formatDate(task.dueDate)}\n`;
        }
        if (task.assignedTo) {
          summary += `  - Assigned to: ${task.assignedTo.name || 'Unassigned'}\n`;
        }
      });
      summary += '\n';
    }
  }

  // Add metadata
  summary += formatMetadata(data._metadata);

  return summary;
}

module.exports = {
  formatFamilyData,
  formatFamilyMember,
  formatFamilyEvent,
  formatStructuredFamilyData,
  formatLegacyFamilyData,
};
