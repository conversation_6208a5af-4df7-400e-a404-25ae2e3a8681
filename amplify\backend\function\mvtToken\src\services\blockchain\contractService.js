var ethers = require("ethers");
var dotenv = require("dotenv");
var contractABI = require("./contractABI.json");
var withdrawContractABI = require("./withdrawContractABI.json");
var axios = require("axios");

dotenv.config();

console.log("process.env.RPC_URL: ", process.env.RPC_URL);
console.log("process.env.PRIVATE_KEY: ", process.env.PRIVATE_KEY);
console.log("process.env.CONTRACT_ADDRESS: ", process.env.MVT_CONTRACT_ADDRESS);
console.log("process.env.MVT_WITHDRAW_CONTRACT_ADDRESS: ", process.env.MVT_WITHDRAW_CONTRACT_ADDRESS);
console.log("process.env.ETHERSCAN_API_KEY: ", process.env.ETHERSCAN_API_KEY);

// Validate required environment variables are present
if (!process.env.RPC_URL) {
  console.error("ERROR: RPC_URL environment variable is not set");
}
if (!process.env.PRIVATE_KEY) {
  console.error("ERROR: PRIVATE_KEY environment variable is not set");
}
if (!process.env.MVT_CONTRACT_ADDRESS) {
  console.error("ERROR: MVT_CONTRACT_ADDRESS environment variable is not set");
}

if (!process.env.MVT_WITHDRAW_CONTRACT_ADDRESS) {
  console.error(
    "ERROR: MVT_WITHDRAW_CONTRACT_ADDRESS environment variable is not set"
  );
}

if (!process.env.ETHERSCAN_API_KEY) {
  console.error("ERROR: ETHERSCAN_API_KEY environment variable is not set");
}

var swapRequestCache = {
  data: null,
  timestamp: 0,
  expiryTime: 60000, // 1 minute cache
};

// Add a transaction tracking cache to prevent duplicates
var pendingTransactions = {
  mint: {},
  transfer: {},
  depositUSDC: {},
  withdrawUSDC: {},
  swapRequest: {},
  lastCleanup: Date.now()
};

// Helper function to clean up old pending transactions
function cleanupPendingTransactions() {
  var now = Date.now();
  // Only clean up every 5 minutes
  if (now - pendingTransactions.lastCleanup < 300000) {
    return;
  }
  
  // Clean up transactions older than 10 minutes
  var expiryTime = 600000; // 10 minutes
  
  Object.keys(pendingTransactions).forEach(type => {
    if (type === 'lastCleanup') return;
    
    Object.keys(pendingTransactions[type]).forEach(key => {
      const transaction = pendingTransactions[type][key];
      if (now - transaction.timestamp > expiryTime) {
        delete pendingTransactions[type][key];
      }
    });
  });
  
  pendingTransactions.lastCleanup = now;
}

// Connect to Ethereum network
var provider;
var wallet;
var contract;
var withdrawContract;
var etherscanProvider;

try {
  // Initialize provider
  provider = new ethers.JsonRpcProvider(process.env.RPC_URL);

  // Etherscan provider for fetching transactions
  etherscanProvider = new ethers.EtherscanProvider(
    "homestead",
    process.env.ETHERSCAN_API_KEY
  );

  // Create a wallet (signer) from private key
  if (process.env.PRIVATE_KEY) {
    wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);
  }

  // Connect to deployed contract
  if (wallet && process.env.MVT_CONTRACT_ADDRESS) {
    contract = new ethers.Contract(
      process.env.MVT_CONTRACT_ADDRESS,
      contractABI,
      wallet
    );
  }
  if (wallet && process.env.MVT_WITHDRAW_CONTRACT_ADDRESS) {
    withdrawContract = new ethers.Contract(
      process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
      withdrawContractABI,
      wallet
    );
  }
} catch (error) {
  console.error("Failed to initialize blockchain connection:", error);
}

// Function to mint MVT tokens
exports.mintMVT = function (amount) {
  return new Promise(function (resolve, reject) {
    console.log("Minting " + amount + " MVT");
    try {
      if (!contract) {
        throw new Error(
          "Contract is not initialized. Check environment variables."
        );
      }
      
      // Check for duplicate transactions
      var now = Date.now();
      var transactionId = "mint-" + amount + "-" + now;
      var isDuplicate = false;
      
      // Clean up old entries (older than 10 minutes)
      for (var key in pendingTransactions.mint) {
        if (now - pendingTransactions.mint[key].timestamp > 600000) {
          delete pendingTransactions.mint[key];
        }
      }
      
      // Check if a similar transaction is already in progress (within 30 seconds)
      for (var key in pendingTransactions.mint) {
        var tx = pendingTransactions.mint[key];
        if (tx.amount === amount && now - tx.timestamp < 30000) {
          console.warn("Duplicate mint detected for amount: " + amount + " MVT");
          isDuplicate = true;
          
          // If already completed, return success
          if (tx.status === "completed") {
            console.log("Using cached result for transaction");
            resolve();
            return;
          }
        }
      }
      
      // Record this transaction
      pendingTransactions.mint[transactionId] = {
        amount: amount,
        timestamp: now,
        status: "pending"
      };
      
      console.log("Starting mint transaction with ID: " + transactionId);
      contract
        .mint(ethers.parseUnits(amount, 18))
        .then(function (tx) {
          console.log("Mint transaction hash:", tx.hash);
            pendingTransactions.mint[transactionId].hash = tx.hash;
          return tx.wait();
        })
        .then(function (receipt) {
          console.log("Mint transaction receipt:", receipt);
          console.log("Minted " + amount + " MVT");
          pendingTransactions.mint[transactionId].status = "completed";
          resolve();
        })
        .catch(function (error) {
          console.error("Minting failed:", error);
          pendingTransactions.mint[transactionId].status = "failed";
            pendingTransactions.mint[transactionId].error = error.message;
          resolve(); // Resolving instead of rejecting to maintain backward compatibility
        });
    } catch (error) {
      console.error("Minting failed:", error);
      resolve(); // Resolving instead of rejecting to maintain backward compatibility
    }
  });
};

// Function to transfer MVT tokens INTERNALLY between users
exports.transferMVT = function (to, amount) {
  return new Promise(function (resolve, reject) {
    console.log("Initiating transfer of MVT tokens");
    console.log("Recipient address:", to);
    console.log("Transfer amount:", amount);
    
    try {
      if (!contract) {
        throw new Error(
          "Contract is not initialized. Check environment variables."
        );
      }
      console.log("Contract initialized successfully");
      
      // Create a unique transaction ID
      const transactionId = `transfer-${to}-${amount}-${Date.now()}`;
      
      // Check for recent similar transactions
      const now = Date.now();
      const recentTransactionWindow = 30000; // 30 seconds
      
      // Clean up old pending transactions
      cleanupPendingTransactions();
      
      // Check for recent similar transactions
      for (const key in pendingTransactions.transfer) {
        const pendingTx = pendingTransactions.transfer[key];
        
        // If same recipient, same amount, and within window, likely a duplicate
        if (pendingTx.to === to && 
            pendingTx.amount === amount && 
            (now - pendingTx.timestamp < recentTransactionWindow)) {
          console.warn(`Possible duplicate transfer transaction detected! To: ${to}, Amount: ${amount}`);
          
          // If transaction already completed successfully, return success
          if (pendingTx.status === 'completed') {
            console.log(`Using cached success result for duplicate transfer transaction`);
            resolve();
            return;
          }
          
          // Similar logic as in mintMVT - waiting for pending transaction
          if (pendingTx.status === 'pending') {
            console.log(`Similar transaction already pending. Waiting for it to complete.`);
            // Implementation similar to mintMVT - omitted for brevity
          }
        }
      }
      
      // Track this transaction
      pendingTransactions.transfer[transactionId] = {
        to: to,
        amount: amount,
        timestamp: now,
        status: 'pending'
      };
      
      console.log(`Starting transfer transaction with ID: ${transactionId}`);

      // Directly transfer tokens from sender to recipient using the built-in transfer method
      contract
        .transferMVT(to, ethers.parseUnits(amount, 18))
        .then(function (tx) {
          console.log("Transaction hash:", tx.hash);
          
          // Update transaction with hash
          if (pendingTransactions.transfer[transactionId]) {
            pendingTransactions.transfer[transactionId].hash = tx.hash;
          }
          
          return tx.wait();
        })
        .then(function () {
          console.log("Successfully transferred " + amount + " MVT to " + to);
          
          // Mark transaction as completed
          if (pendingTransactions.transfer[transactionId]) {
            pendingTransactions.transfer[transactionId].status = 'completed';
          }
          
          resolve();
        })
        .catch(function (error) {
          console.error("Transfer failed during transaction execution:", error);
          
          // Mark transaction as failed
          if (pendingTransactions.transfer[transactionId]) {
            pendingTransactions.transfer[transactionId].status = 'failed';
            pendingTransactions.transfer[transactionId].error = error.message;
          }
          
          resolve(); // Resolving instead of rejecting to maintain backward compatibility
        });
    } catch (error) {
      console.error("Transfer failed during setup:", error);
      resolve(); // Resolving instead of rejecting to maintain backward compatibility
    }
  });
};

// Function to check MVT balance
exports.getMVTBalance = function (address) {
  return new Promise(function (resolve, reject) {
    console.log("Checking balance for address:", address);
    try {
      if (!contract) {
        throw new Error(
          "Contract is not initialized. Check environment variables."
        );
      }

      // Validate the address
      if (
        !address ||
        typeof address !== "string" ||
        !address.startsWith("0x")
      ) {
        console.error("Invalid address format:", address);
        throw new Error(
          "Invalid address format. Expected a valid Ethereum address starting with 0x"
        );
      }

      // Log available functions for debugging
      console.log(
        "Contract functions available:",
        JSON.stringify(
          contract.interface.fragments.map(function (f) {
            return f.name;
          })
        )
      );

      // Try with the getMVTBalance function first if it exists
      var hasMVTBalanceFunction = contract.interface.fragments.some(
        function(f) { return f.name === "getMVTBalance"; }
      );

      if (hasMVTBalanceFunction) {
        console.log("Attempting to use getMVTBalance function");
        contract
          .getMVTBalance(address)
          .then(function(balance) {
            console.log(
              "Balance fetched with getMVTBalance:",
              balance.toString()
            );
            resolve(ethers.formatUnits(balance, 18));
          })
          .catch(function(getMVTBalanceError) {
            console.error(
              "Error using getMVTBalance, falling back to balanceOf:",
              getMVTBalanceError
            );
            // Fall back to balanceOf
            tryBalanceOf();
          });
      } else {
        tryBalanceOf();
      }

      function tryBalanceOf() {
        console.log("Attempting to use balanceOf function");
        contract
          .balanceOf(address)
          .then(function(balance) {
            console.log("Balance fetched with balanceOf:", balance.toString());
            resolve(ethers.formatUnits(balance, 18));
          })
          .catch(function(balanceOfError) {
            // Handle specific ethers.js error
            if (
              balanceOfError.code === "BAD_DATA" &&
              balanceOfError.value === "0x"
            ) {
              console.error("Contract returned empty data (0x)");
              reject(
                new Error(
                  "Contract returned empty data. This usually happens when the contract does not implement the function correctly."
                )
              );
            } else {
              console.error(
                "Error fetching balance with balanceOf:",
                balanceOfError
              );
              reject(
                new Error(
                  "Failed to fetch MVT balance: " + balanceOfError.message
                )
              );
            }
          });
      }
    } catch (error) {
      console.error("Error in getMVTBalance:", error);
      reject(error);
    }
  });
};

// Function to transfer MVT tokens between users
exports.transferMVTBetweenUsers = function (from, to, amount) {
  return new Promise(function (resolve, reject) {
    console.log("From:", from);
    console.log("To:", to);
    console.log("Amount:", amount);
    try {
      if (!contract) {
        throw new Error(
          "Contract is not initialized. Check environment variables."
        );
      }

      // Check allowance
      contract.allowance(from, wallet.address).then(function(allowance) {
      console.log("Allowance:", ethers.formatUnits(allowance, 18));

      if (allowance < ethers.parseUnits(amount, 18)) {
        throw new Error(
          "Insufficient allowance. User must approve tokens first."
        );
      }

      // Transfer tokens
      console.log("Transferring tokens...");
      contract
        .transferFrom(from, to, ethers.parseUnits(amount, 18))
          .then(function(tx) {
          console.log("Transaction hash:", tx.hash);
          return tx.wait();
        })
          .then(function() {
          console.log(
            "Transferred " + amount + " MVT from " + from + " to " + to
          );
          resolve();
        })
          .catch(function(error) {
          console.error("Transfer failed:", error);
            resolve();
          });
      }).catch(function(error) {
        console.error("Failed to check allowance:", error);
          resolve();
        });
    } catch (error) {
      console.error("Transfer failed:", error);
      resolve();
    }
  });
};

// Let's add a cache for transaction data
var transactionCache = {
  data: {},
  timestamp: null,
  expiryTime: 5 * 60 * 1000, // 5 minutes cache expiry
};

// Function to get transaction list
exports.getMVTTransactionList = async function (address, limit = null) {
  console.log("Fetching transaction list for address:", address);
  console.log("Limit:", limit);

  try {
    // Check if we have cached data and it's still valid
    const now = Date.now();
    if (
      transactionCache.timestamp &&
      now - transactionCache.timestamp < transactionCache.expiryTime &&
      transactionCache.data[address]
    ) {
      console.log("Using cached transaction data");
      const cachedData = transactionCache.data[address];

      // If limit is specified, return only that many items
      const returnData = limit ? cachedData.slice(0, limit) : cachedData;
      console.log("Returning cached data:", returnData);
      return returnData;
    }

    if (!address || typeof address !== "string" || !address.startsWith("0x")) {
      address = process.env.MVT_CONTRACT_ADDRESS;
    }

    // Etherscan API endpoint for fetching transaction history
    const etherscanUrl = `https://api-sepolia.etherscan.io/api?module=account&action=tokentx&address=${address}&startblock=0&endblock=********&sort=desc&apikey=${process.env.ETHERSCAN_API_KEY}`;

    console.log(
      "Fetching transaction history from Etherscan API:",
      etherscanUrl
    );
    // Fetch transaction history
    const response = await axios.get(etherscanUrl);
    console.log("Etherscan API response:", response.data);

    if (response.data.status !== "1") {
      console.error("Etherscan API Error:", response.data.message);
      return [];
    }

    const transactions = response.data.result;
    const contractAddressNormalized =
      process.env.MVT_CONTRACT_ADDRESS.toLowerCase();

    console.log("Processing transaction history...");
    // Process and filter transactions more efficiently
    var processedTransactions = transactions
      .filter(
        function(tx) {
          return tx.contractAddress &&
          tx.contractAddress.toLowerCase() === contractAddressNormalized;
        }
      )
      .map(function(tx) {
        // Check transaction status - if confirmations < 12, consider it pending
        var confirmations = parseInt(tx.confirmations || "0");
        var status = confirmations < 12 ? "PENDING" : "COMPLETED";
        
        return {
          id: tx.hash,
          amount: ethers.formatUnits(tx.value, 18), // Convert wei to MVT
          to: tx.to,
          from: tx.from,
          type: determineTransactionType(tx, address),
          date: new Date(tx.timeStamp * 1000).toISOString(),
          status: status
        };
      });
    console.log("Processed transactions:", processedTransactions);

    // Get swap request transactions if the address matches the contract or is admin
    var swapRequestTransactions = [];
    if (
      address.toLowerCase() === process.env.MVT_CONTRACT_ADDRESS.toLowerCase() ||
      address.toLowerCase() === wallet.address.toLowerCase()
    ) {
      try {
        const swapRequests = await exports.getSwapRequests();
        swapRequestTransactions = swapRequests.map((req) => ({
          id: req.txHash || `req-${req.id}`,
          amount: req.mvtAmount,
          to: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
          from: req.user,
          type: "Swap",
          date: req.date,
          status: req.status,
          currency: "MVT"
        }));
      } catch (error) {
        console.error("Error fetching swap requests:", error);
      }
    }

    // Combine regular transactions with swap requests
    const allTransactions = [...processedTransactions, ...swapRequestTransactions]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // Cache the results
    transactionCache.data[address] = allTransactions;
    transactionCache.timestamp = now;
    console.log("Cached transaction data:", transactionCache.data[address]);

    // Return full or limited results
    const returnData = limit
      ? allTransactions.slice(0, limit)
      : allTransactions;
    console.log("Returning processed data:", returnData);
    return returnData;
  } catch (error) {
    console.error("Fetching transaction list failed:", error);
    return [];
  }
};

// Helper function to determine transaction type
function determineTransactionType(tx, userAddress) {
  console.log("Determining transaction type:", tx, userAddress);
  if (!userAddress || userAddress === process.env.MVT_CONTRACT_ADDRESS) {
    // If viewing as admin, use more descriptive types
    if (
      tx.from.toLowerCase() === process.env.MVT_CONTRACT_ADDRESS.toLowerCase()
    ) {
      console.log("Transaction is Sent");
      return "SENT";
    } else if (
      tx.to.toLowerCase() === process.env.MVT_CONTRACT_ADDRESS.toLowerCase()
    ) {
      console.log("Transaction is Received");
      return "RECEIVED";
    }
  } else {
    // Personalized view for user
    if (tx.from.toLowerCase() === userAddress.toLowerCase()) {
      console.log("Transaction is Sent");
      return "SENT";
    } else if (tx.to.toLowerCase() === userAddress.toLowerCase()) {
      console.log("Transaction is Received");
      return "RECEIVED";
    }
  }

  // Default
  console.log("Transaction type is ADDED");
  return "ADDED";
}

// WithdrawContract Functions
try {
  if (wallet && process.env.MVT_WITHDRAW_CONTRACT_ADDRESS) {
    withdrawContract = new ethers.Contract(
      process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
      withdrawContractABI,
      wallet
    );
    console.log("Withdraw contract initialized successfully with address:", process.env.MVT_WITHDRAW_CONTRACT_ADDRESS);
  } else {
    console.error(
      "Failed to initialize withdraw contract. Check environment variables. MVT_WITHDRAW_CONTRACT_ADDRESS=",
      process.env.MVT_WITHDRAW_CONTRACT_ADDRESS
    );
  }
} catch (error) {
  console.error("Error initializing withdraw contract:", error);
}

// Function to deposit USDC into the contract
exports.depositUSDC = async function (amount) {
  console.log("Depositing " + amount + " USDC");

  try {
    if (!withdrawContract || !process.env.MVT_WITHDRAW_CONTRACT_ADDRESS) {
      throw new Error(
        "Withdraw Contract is not initialized. Check environment variables."
      );
    }

    // Convert USDC amount to correct decimal format (6 decimals)
    const amountInWei = ethers.parseUnits(amount.toString(), 6);
    console.log("amountInWei: ", amountInWei);

    // Get the USDC token contract instance
    const usdcToken = new ethers.Contract(
      process.env.MVT_USDC_CONTRACT_ADDRESS,
      [
        "function approve(address spender, uint256 amount) public returns (bool)",
      ],
      wallet
    );

    // Step 1: Approve the contract to spend USDC
    const approveTx = await usdcToken.approve(
      process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
      amountInWei
    );
    await approveTx.wait();
    console.log("USDC approval successful");

    // Step 2: Deposit USDC into the contract
    const tx2 = await withdrawContract.depositUSDC(amountInWei);
    await tx2.wait();
    console.log("Deposit successful");

    return { success: true, message: "Deposit successful" };
  } catch (error) {
    console.error("Deposit failed:", error);
    return { success: false, message: "Deposit failed", error: error.message };
  }
};

// Function to request a swap of MVT for USDC
exports.requestMVTSwap = async function (
  userWalletAddress,
  mvtAmount,
  signature
) {
  try {
    console.log(
      "Processing swap request for:",
      mvtAmount,
      "MVT from",
      userWalletAddress
    );

    // Verify signature
    const message = "I am requesting a swap of " + mvtAmount + " MVT from my wallet: " + userWalletAddress.toLowerCase();
    console.log("message: ", message);
    console.log("signature: ", signature);
    const recoveredAddress = ethers.verifyMessage(message, signature);
    console.log("userWalletAddress: ", userWalletAddress);
    console.log("recoveredAddress: ", recoveredAddress);

    if (recoveredAddress.toLowerCase() !== userWalletAddress.toLowerCase()) {
      throw new Error("Signature verification failed. Unauthorized request.");
    }

    console.log("Signature verified. Proceeding with swap...");

    // Convert amount to Wei
    const amountInWei = ethers.parseUnits(mvtAmount.toString(), 18);
    const signer = new ethers.Wallet(
      "ba37f55275bbd23f32f9b12fd40bd26c934c49fe188bb9947babce98bd3b5952",
      provider
    );
    // Set up contract instances
    const userWithdrawContract = new ethers.Contract(
      process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
      withdrawContractABI,
      signer // Ensure you have a provider set up
    );

    // Check contract USDC balance
    const contractUsdcBalance =
      await userWithdrawContract.getContractUSDCBalance();
    console.log(
      "Contract USDC balance:",
      ethers.formatUnits(contractUsdcBalance, 6)
    );

    // Get exchange rate and required USDC
    const exchangeRate = await userWithdrawContract.getExchangeRate();
    console.log("Exchange rate:", ethers.formatUnits(exchangeRate, 18));

    const usdcNeeded =
      (BigInt(amountInWei) * BigInt(exchangeRate)) / BigInt(10n ** 18n);
    console.log("USDC needed:", ethers.formatUnits(usdcNeeded, 6));

    if (BigInt(contractUsdcBalance) < usdcNeeded) {
      throw new Error(
        "Insufficient USDC in contract for swap. Required: " + ethers.formatUnits(
          usdcNeeded,
          6
        ) + " USDC"
      );
    }

    // Request Swap
    const tx = await userWithdrawContract.requestSwap(amountInWei, {
      gasLimit: 500000,
    });
    console.log("Swap request transaction hash:", tx.hash);

    const receipt = await tx.wait();
    console.log("Swap request transaction status:", receipt.status);

    if (receipt.status === 0) {
      throw new Error("Transaction failed during execution.");
    }

    const swapRequestId = await userWithdrawContract.swapRequestId();
    const requestId = (parseInt(swapRequestId.toString()) - 1).toString();
    console.log("Swap request ID:", requestId);

    return { success: true, requestId };
  } catch (error) {
    console.error("Swap request failed:", error);
    throw new Error(
      "Swap request failed: " + (error.message || "Unknown error")
    );
  }
};

// Function to approve a swap request (admin only)
exports.approveSwap = async function (requestId) {
  console.log("Approving swap request ID:", requestId);
  try {
    if (!withdrawContract) {
      throw new Error(
        "Withdraw Contract is not initialized. Check environment variables."
      );
    }

    // Check if the request exists and isn't already approved
    const request = await withdrawContract.swapRequests(requestId);
    console.log("Swap request details:", request);

    if (request.approved) {
      throw new Error("This swap request has already been approved");
    }

    // Proceed with approval
    const tx = await withdrawContract.approveSwap(requestId);
    console.log("Approve transaction hash:", tx.hash);
    const receipt = await tx.wait();
    console.log("Approve transaction receipt:", receipt);
    console.log("Approved swap request ID:", requestId);

    return { success: true, txHash: tx.hash };
  } catch (error) {
    console.error("Approval failed:", error);
    throw error;
  }
};

// Function to reject a swap request (on-chain)
exports.rejectSwap = async function (requestId) {
  console.log("Rejecting swap request ID:", requestId);
  try {
    if (!withdrawContract) {
      throw new Error(
        "Withdraw Contract is not initialized. Check environment variables."
      );
    }

    // Check if the request exists and isn't already rejected
    const request = await withdrawContract.swapRequests(requestId);
    console.log("Swap request details:", request);

    if (request.rejected) {
      throw new Error("This swap request has already been rejected");
    }

    // Proceed with rejection
    const tx = await withdrawContract.rejectSwap(requestId);
    console.log("Reject transaction hash:", tx.hash);
    const receipt = await tx.wait();
    console.log("Reject transaction receipt:", receipt);
    console.log("Rejected swap request ID:", requestId);

    return { success: true, txHash: tx.hash };
  } catch (error) {
    console.error("Rejection failed:", error);
    throw error;
  }
};

// Function to get all swap requests
exports.getSwapRequests = async function (limit = null) {
  console.log("Fetching swap requests...");

  try {
    // Always clear the cache to ensure we have fresh data with correct expiration status
    swapRequestCache.data = null;
    swapRequestCache.timestamp = 0;
    console.log("Cache cleared to ensure fresh expiration status data");

    // Initialize contract
    if (!withdrawContract) {
      throw new Error(
        "Withdraw Contract is not initialized. Check environment variables."
      );
    }

    // Get total number of swap requests
    const currentId = await getTotalSwapRequestCount();
    
    // Fetch all relevant events
    const events = await fetchSwapEvents();
    
    // Process each request
    const requests = await processAllRequests(currentId, events);
    
    // Sort by date (newest first)
    requests.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // Update cache
    swapRequestCache.data = requests;
    swapRequestCache.timestamp = Date.now();

    // Return requested number of items
    return limit ? requests.slice(0, limit) : requests;
  } catch (error) {
    console.error("Failed to fetch swap requests:", error);
    return [];
  }
};

// Helper function to get total swap request count
async function getTotalSwapRequestCount() {
  const currentIdBigInt = await withdrawContract.swapRequestId();
  const currentId = parseInt(currentIdBigInt.toString());
  console.log(`Total swap requests: ${currentId}`);
  return currentId;
}

// Helper function to fetch all relevant events
async function fetchSwapEvents() {
  // Pre-fetch all events once
  const [allRequestedEvents, allApprovalEvents, allRejectionEvents, allCancelEvents] =
    await Promise.all([
      withdrawContract.queryFilter(withdrawContract.filters.SwapRequested()),
      withdrawContract.queryFilter(withdrawContract.filters.SwapApproved()),
      withdrawContract.queryFilter(withdrawContract.filters.SwapRejected()),
      withdrawContract.queryFilter(withdrawContract.filters.SwapCancelled()),
    ]);

  // Log event counts
  console.log(`Events fetched - Requested: ${allRequestedEvents.length}, Approved: ${allApprovalEvents.length}, Rejected: ${allRejectionEvents.length}, Cancelled: ${allCancelEvents.length}`);
  
  // Process cancel events for debugging
  await processCancelEvents(allCancelEvents);

  return {
    allRequestedEvents,
    allApprovalEvents,
    allRejectionEvents,
    allCancelEvents
  };
}

// Helper function to process cancel events (mainly for debugging)
async function processCancelEvents(allCancelEvents) {
  // Log cancel events for debugging
  if (allCancelEvents.length > 0) {
    console.log("Cancel events found:", allCancelEvents.map(e => ({ 
      requestId: e.args.requestId.toString(),
      transactionHash: e.transactionHash
    })));
  } else {
    console.log("No cancel events found in the contract");
    
    // Debug: Try to log all event names in the contract
    try {
      console.log("Available events in contract:", 
        Object.keys(withdrawContract.filters)
          .filter(key => typeof withdrawContract.filters[key] === 'function')
      );
      
      // Try to get all events (not filtered) to see if we can find cancel events
      console.log("Attempting to get all events from the contract...");
      const allEvents = await withdrawContract.queryFilter(null);
      console.log(`Found ${allEvents.length} total events`);
      
      // Look for cancel events by examining event names
      const possibleCancelEvents = allEvents.filter(e => 
        e.eventName && (
          e.eventName.toLowerCase().includes('cancel') || 
          e.eventName.toLowerCase().includes('expired')
        )
      );
      
      if (possibleCancelEvents.length > 0) {
        console.log("Found possible cancel events with different names:", 
          possibleCancelEvents.map(e => e.eventName)
        );
      }
    } catch (error) {
      console.error("Error trying to debug contract events:", error);
    }
  }
}

// Helper function to process all requests
async function processAllRequests(currentId, events) {
  const requests = [];

  for (let i = 0; i < currentId; i++) {
    try {
      // Get raw request data
      const requestRaw = await withdrawContract.swapRequests(i);
      
      // Skip empty requests
      if (requestRaw.user === "******************************************") {
        continue;
      }

      // Process this individual request
      const request = await processIndividualRequest(i, requestRaw, events);
      requests.push(request);
    } catch (error) {
      console.error(`Error fetching swap request ${i}:`, error);
    }
  }

  // Log summary of requests
  console.log(`Retrieved ${requests.length} swap requests`);
  console.log(`Expired requests: ${requests.filter(r => r.status === "EXPIRED").length}`);
  console.log(`Pending requests: ${requests.filter(r => r.status === "PENDING").length}`);
  console.log(`Approved requests: ${requests.filter(r => r.status === "APPROVED").length}`);
  console.log(`Rejected requests: ${requests.filter(r => r.status === "REJECTED").length}`);

  return requests;
}

// Helper function to process an individual request
async function processIndividualRequest(i, requestRaw, events) {
  // Filter events for this request
  const { allRequestedEvents, allApprovalEvents, allRejectionEvents, allCancelEvents } = events;
  
  const approvalEvents = allApprovalEvents.filter(
    (e) => e.args.requestId.toString() === i.toString()
  );
  const rejectionEvents = allRejectionEvents.filter(
    (e) => e.args.requestId.toString() === i.toString()
  );
  const cancelEvents = allCancelEvents.filter(
    (e) => e.args.requestId.toString() === i.toString()
  );
  const requestEvents = allRequestedEvents.filter(
    (e) => e.args.requestId.toString() === i.toString()
  );

  console.log(
    `Swap ID ${i} - Approvals: ${approvalEvents.length}, Rejections: ${rejectionEvents.length}, Cancellations: ${cancelEvents.length}, Requests: ${requestEvents.length}`
  );

  // Determine status and transaction hash
  const { status, transactionHash } = await determineRequestStatus(i, requestRaw, {
    approvalEvents,
    rejectionEvents,
    cancelEvents,
    requestEvents
  });

  // Get the date for this request
  const { requestDate, requestDateSource } = await getRequestDate(i, requestEvents, transactionHash);

  // Check if old pending requests need to be expired
  const { updatedStatus, updatedHash } = await checkAndExpireOldRequest(
    i, status, requestDate, transactionHash
  );

  // Try to get user name from blockchain
  var userName = null;
  try {
    var userAddress = requestRaw.user;
    if (userAddress !== "******************************************") {
      var userDetails = await withdrawContract.userDetails(userAddress);
      if (userDetails && userDetails.firstName && userDetails.lastName) {
        userName = userDetails.firstName + " " + userDetails.lastName;
      }
    }
  } catch (error) {
    console.log("Error getting user details: " + error.message);
  }

  // Create the final request object
  return {
    id: i.toString(),
    user: requestRaw.user,
    userName: userName,
    walletAddress: requestRaw.user,
    mvtAmount: ethers.formatUnits(requestRaw.mvtAmount, 18),
    usdcAmount: ethers.formatUnits(requestRaw.usdcAmount, 6),
    status: updatedStatus,
    approved: requestRaw.approved,
    date: requestDate.toISOString(),
    dateSource: requestDateSource, // Add source for debugging
    txHash: updatedHash,
  };
}

// Helper function to determine request status
async function determineRequestStatus(i, requestRaw, events) {
  const { approvalEvents, rejectionEvents, cancelEvents, requestEvents } = events;
  
  let status = "PENDING";
  let transactionHash = null;

  // Check if the swap is expired (cancelled)
  if (cancelEvents.length > 0) {
    console.log(`Swap ID ${i} is EXPIRED due to cancellation event`);
    status = "EXPIRED";
    transactionHash = cancelEvents[0].transactionHash;
  } 
  // Check if rejected
  else if (rejectionEvents.length > 0) {
    console.log(`Swap ID ${i} is REJECTED`);
    status = "REJECTED";
    transactionHash = rejectionEvents[0].transactionHash;
  } 
  // Check if approved
  else if (approvalEvents.length > 0 || requestRaw.approved) {
    console.log(`Swap ID ${i} is APPROVED`);
    status = "APPROVED";
    transactionHash =
      approvalEvents.length > 0
        ? approvalEvents[0].transactionHash
        : null;
  } 
  // Otherwise it's pending, but check if it's expired based on timestamp
  else if (requestEvents.length > 0) {
    // Get current timestamp and expiration time
    try {
      const now = Math.floor(Date.now() / 1000); // Current time in seconds
      const requestTimestamp = parseInt(requestRaw.timestamp.toString());
      const expirationTimeBigInt = await withdrawContract.expirationTime();
      const expirationTime = parseInt(expirationTimeBigInt.toString());
      
      // If current time > request timestamp + expiration time, then it's expired
      if (now > requestTimestamp + expirationTime) {
        console.log(`Swap ID ${i} is EXPIRED due to time (${now} > ${requestTimestamp} + ${expirationTime})`);
        status = "EXPIRED";
      } else {
        console.log(`Swap ID ${i} is PENDING`);
      }
    } catch (error) {
      console.error(`Error checking expiration time for request ${i}:`, error);
      console.log(`Fallback: Swap ID ${i} is PENDING`);
    }
    
    transactionHash = requestEvents[0].transactionHash;
  }

  return { status, transactionHash };
}

// Helper function to get request date
async function getRequestDate(i, requestEvents, transactionHash) {
  let requestDate = new Date(); // Default to current date
  let requestDateSource = "fallback";

  try {
    if (requestEvents.length > 0) {
      console.log(`Fetching block data for request ${i} with block number ${requestEvents[0].blockNumber}`);
      // Get the block of the first request event to get the timestamp
      const block = await provider.getBlock(requestEvents[0].blockNumber);
      if (block && block.timestamp) {
        requestDate = new Date(block.timestamp * 1000);
        requestDateSource = "blockchain";
        console.log(`Using blockchain timestamp for request ${i}: ${requestDate.toISOString()}`);
      } else {
        console.warn(`Block data missing timestamp for request ${i}, using current date as fallback`);
      }
    } else {
      console.warn(`No request events found for request ${i}, using current date as fallback`);
    }
  } catch (error) {
    console.error(`Error getting block timestamp for request ${i}:`, error.message);
  }

  // Second option - use Etherscan API to get transaction timestamp if available
  if (requestDateSource === "fallback" && transactionHash && transactionHash.startsWith("0x")) {
    try {
      console.log(`Trying to get transaction timestamp from Etherscan for request ${i}`);
      const txData = await etherscanProvider.getTransaction(transactionHash);
      if (txData && txData.timestamp) {
        requestDate = new Date(txData.timestamp * 1000);
        requestDateSource = "etherscan";
        console.log(`Using Etherscan timestamp for request ${i}: ${requestDate.toISOString()}`);
      }
    } catch (etherscanError) {
      console.error(`Error getting Etherscan data for request ${i}:`, etherscanError.message);
    }
  }

  return { requestDate, requestDateSource };
}

// Helper function to check if old pending requests need to be expired
async function checkAndExpireOldRequest(i, status, requestDate, transactionHash) {
  const now = new Date();
  
  let updatedStatus = status;
  let updatedHash = transactionHash;

  // Only process pending requests
  if (status === "PENDING") {
    try {
      // Get the request data directly from the contract
      const requestRaw = await withdrawContract.swapRequests(i);
      
      // Get the contract's expiration time (in seconds)
      const expirationTimeBigInt = await withdrawContract.expirationTime();
      const expirationTimeInSeconds = parseInt(expirationTimeBigInt.toString());
      console.log(`Contract expiration time for request ${i}: ${expirationTimeInSeconds} seconds`);
      
      // Convert to milliseconds for JavaScript Date comparison
      const expirationTimeMs = expirationTimeInSeconds * 1000;
      
      // Get the request timestamp from the contract (in seconds)
      const requestTimestampBigInt = requestRaw.timestamp;
      const requestTimestampSeconds = parseInt(requestTimestampBigInt.toString());
      const requestTimestampDate = new Date(requestTimestampSeconds * 1000);
      
      console.log(`Request ${i} timestamp from contract: ${requestTimestampDate.toISOString()}`);
      console.log(`Current time: ${now.toISOString()}`);
      
      // Calculate the expiration time
      const expirationDate = new Date(requestTimestampSeconds * 1000 + expirationTimeMs);
      console.log(`Request ${i} expiration date: ${expirationDate.toISOString()}`);
      
      // Check if the request has expired based on contract's timestamp and expiration time
      if (now.getTime() > expirationDate.getTime()) {
        console.log(`Swap ID ${i} is EXPIRED. Contract timestamp: ${requestTimestampDate.toISOString()}, Expiration: ${expirationDate.toISOString()}`);
        
        // Try to actually cancel on-chain if it's expired
        try {
          console.log(`Attempting to cancel swap request ${i} on-chain due to expiration`);
          const cancelResult = await exports.cancelSwap(i);
          console.log(`Successfully cancelled swap request ${i} on-chain:`, cancelResult);
          updatedStatus = "EXPIRED";
          updatedHash = cancelResult.txHash;
        } catch (cancelError) {
          console.error(`Failed to cancel swap request ${i} on-chain:`, cancelError.message);
          
          // Even if the cancellation fails (e.g., if it was already cancelled or someone else cancelled it),
          // we should still mark it as expired in our response
          updatedStatus = "EXPIRED";
          updatedHash = "auto-expired-" + Date.now().toString();
        }
      }
    } catch (error) {
      console.error(`Error checking expiration for request ${i}:`, error);
    }
  }

  return { updatedStatus, updatedHash };
}

exports.getExchangeRate = async function () {
  console.log("Fetching exchange rate");

  try {
    if (!withdrawContract) {
      throw new Error(
        "Withdraw Contract is not initialized. Check environment variables."
      );
    }

    const rawRate = await withdrawContract.getExchangeRate();
    const humanRate = ethers.formatUnits(rawRate, 6); 

    console.log("Exchange rate (USDC per MVT):", humanRate);
    return humanRate;
  } catch (error) {
    console.error("Failed to fetch exchange rate:", error);
    return "1.0"; 
  }
};

// Function to get USDC balance
exports.getUSDCBalance = async function (address) {
  console.log("Checking USDC balance for address:", address);

  try {
    const balance = await withdrawContract.getContractUSDCBalance();

    // USDC has 6 decimals, so format the balance for readability
    const formattedBalance = ethers.formatUnits(balance, 6);
    console.log(`USDC balance of the contract is: ${formattedBalance} USDC`);

    return formattedBalance;
  } catch (error) {
    console.error("Failed to fetch USDC balance:", error);
    return "0"; // Return a default value in case of failure
  }
};

// Function to get USDC transaction list
exports.getUSDCTransactionList = async function (address, limit = null) {
  console.log("Fetching USDC transaction list for address:", address);
  console.log("Limit:", limit);

  try {
    // Use a separate cache key for USDC transactions
    var cacheKey = `usdc_${address}`;
    var now = Date.now();

    // Check if we have cached data and it's still valid
    if (
      transactionCache.timestamp &&
      now - transactionCache.timestamp < transactionCache.expiryTime &&
      transactionCache.data[cacheKey]
    ) {
      console.log("Using cached USDC transaction data");
      var cachedData = transactionCache.data[cacheKey];

      // If limit is specified, return only that many items
      var returnData = limit ? cachedData.slice(0, limit) : cachedData;
      console.log("Returning cached USDC data:", returnData);
      return returnData;
    }

    if (!address || typeof address !== "string" || !address.startsWith("0x")) {
      address = process.env.MVT_WITHDRAW_CONTRACT_ADDRESS;
    }

    // Etherscan API endpoint for fetching USDC transaction history
    var etherscanUrl = `https://api-sepolia.etherscan.io/api?module=account&action=tokentx&address=${address}&startblock=0&endblock=********&sort=desc&apikey=${process.env.ETHERSCAN_API_KEY}`;

    console.log(
      "Fetching USDC transaction history from Etherscan API:",
      etherscanUrl
    );
    // Fetch transaction history
    var response = await axios.get(etherscanUrl);
    console.log("Etherscan API response for USDC:", response.data);

    if (response.data.status !== "1") {
      console.error("Etherscan API Error (USDC):", response.data.message);
      return [];
    }

    var transactions = response.data.result;
    var usdcContractAddressNormalized =
      process.env.MVT_USDC_CONTRACT_ADDRESS.toLowerCase();

    console.log("Processing USDC transaction history...");
    // Process and filter transactions for USDC
    var processedTransactions = transactions
      .filter(
        function(tx) {
          return tx.contractAddress &&
          tx.contractAddress.toLowerCase() === usdcContractAddressNormalized;
        }
      )
      .map(function(tx) {
        // Check transaction status - if confirmations < 12, consider it pending
        var confirmations = parseInt(tx.confirmations || "0");
        var status = confirmations < 12 ? "PENDING" : "COMPLETED";
        
        return {
          id: tx.hash,
          amount: ethers.formatUnits(tx.value, 6), // Convert wei to USDC (6 decimals)
          to: tx.to,
          from: tx.from,
          type: determineTransactionType(tx, address),
          date: new Date(tx.timeStamp * 1000).toISOString(),
          status: status
        };
      });
    console.log("Processed USDC transactions:", processedTransactions);

    // Get swap request transactions if the address matches the contract or is admin
    var swapRequestTransactions = [];
    if (
      address.toLowerCase() === process.env.MVT_WITHDRAW_CONTRACT_ADDRESS.toLowerCase() ||
      address.toLowerCase() === wallet.address.toLowerCase()
    ) {
      try {
        var swapRequests = await exports.getSwapRequests();
        swapRequestTransactions = swapRequests.map((req) => ({
          id: req.txHash || `req-${req.id}`,
          amount: req.usdcAmount,
          to: req.user,
          from: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
          type: "Swap",
          date: req.date,
          status: req.status,
          currency: "USDC"
        }));
      } catch (error) {
        console.error("Error fetching swap requests for USDC:", error);
      }
    }

    // Combine regular transactions with swap requests
    var allTransactions = [...processedTransactions, ...swapRequestTransactions]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // Cache the results with the USDC-specific key
    transactionCache.data[cacheKey] = allTransactions;
    transactionCache.timestamp = now;
    console.log(
      "Cached USDC transaction data:",
      transactionCache.data[cacheKey]
    );

    // Return full or limited results
    var returnData = limit
      ? allTransactions.slice(0, limit)
      : allTransactions;
    console.log("Returning processed USDC data:", returnData);
    return returnData;
  } catch (error) {
    console.error("Fetching USDC transaction list failed:", error);
    return [];
  }
};

// Function to set up the contract for testing (admin only)
exports.setupContract = async function (usdcAmount) {
  try {
    console.log("Setting up contract for testing");

    // Check if contract is initialized
    if (!withdrawContract) {
      throw new Error(
        "Withdraw Contract is not initialized. Check environment variables."
      );
    }

    // Check current balances
    const usdcBalance = await withdrawContract.getContractUSDCBalance();
    console.log(
      "Current USDC Balance:",
      ethers.formatUnits(usdcBalance, 6),
      "USDC"
    );

    // Check MVT balance in the withdraw contract
    try {
      const mvtBalance = await withdrawContract.getContractMVTBalance();
      console.log(
        "Withdraw Contract MVT Balance:",
        ethers.formatUnits(mvtBalance, 18),
        "MVT"
      );
    } catch (error) {
      console.log("Error fetching withdraw contract MVT balance:", error);
    }

    // Check MVT balance in the MVT token contract
    try {
      const mvtTokenBalance = await withdrawContract.getMVTTokenBalance();
      console.log(
        "MVT Token Contract Balance:",
        ethers.formatUnits(mvtTokenBalance, 18),
        "MVT"
      );

      if (mvtTokenBalance <= 0) {
        console.log(
          "WARNING: MVT Token contract has no MVT tokens. Exchange rate calculation will fail!"
        );
      }
    } catch (error) {
      console.log("Error fetching MVT token balance:", error);
    }

    // Check exchange rate (might fail if no MVT in token contract)
    try {
      const exchangeRate = await withdrawContract.getExchangeRate();
      console.log(
        "Current exchange rate:",
        ethers.formatUnits(exchangeRate, 18),
        "USDC per MVT"
      );
    } catch (error) {
      console.log("Error getting exchange rate:", error.message);
      console.log("To fix this, ensure the MVT token contract has MVT tokens.");
    }

    // Check if we need to deposit USDC
    if (usdcAmount && usdcAmount > 0) {
      console.log(`Depositing ${usdcAmount} USDC into the contract...`);
      const depositResult = await exports.depositUSDC(usdcAmount);
      console.log("Deposit result:", depositResult);
    }

    return {
      success: true,
      message: "Contract setup completed",
      usdcBalance: ethers.formatUnits(usdcBalance, 6),
    };
  } catch (error) {
    console.error("Contract setup failed:", error);
    return {
      success: false,
      message: `Contract setup failed: ${error.message}`,
      error: error.message,
    };
  }
};

/**
 * Withdraws USDC from the contract back to the admin wallet using the same private key
 * @param {number} amount - Amount of USDC to withdraw
 * @returns {Promise<Object>} Transaction data
 */
exports.withdrawUSDC = async function (amount) {
  console.log("Withdrawing " + amount + " USDC");

  try {
    if (!withdrawContract || !process.env.MVT_WITHDRAW_CONTRACT_ADDRESS) {
      throw new Error(
        "Withdraw Contract is not initialized. Check environment variables."
      );
    }

    // Convert USDC amount to correct decimal format (6 decimals)
    const amountInWei = ethers.parseUnits(amount.toString(), 6);
    console.log("amountInWei: ", amountInWei);

    // Withdraw USDC from the contract
    const tx = await withdrawContract.withdrawUSDC(amountInWei);
    await tx.wait();
    console.log("Withdrawal successful");

    return { success: true, message: "Withdrawal successful" };
  } catch (error) {
    console.error("Withdrawal failed:", error.message);
    return {
      success: false,
      message: "Withdrawal failed",
      error: error.message,
    };
  }
};

// Function to cancel a swap request (on-chain)
exports.cancelSwap = async function (requestId) {
  console.log("Cancelling swap request ID:", requestId);
  try {
    if (!withdrawContract) {
      throw new Error(
        "Withdraw Contract is not initialized. Check environment variables."
      );
    }

    // Check if the request exists and isn't already cancelled
    const request = await withdrawContract.swapRequests(requestId);
    console.log("Swap request details:", request);

    if (request.user === "******************************************") {
      throw new Error("This swap request does not exist");
    }

    if (request.approved) {
      throw new Error("This swap request has already been approved and cannot be cancelled");
    }

    if (request.rejected) {
      throw new Error("This swap request has already been rejected and cannot be cancelled");
    }

    // Check if the request is expired according to the contract's rules
    const now = Math.floor(Date.now() / 1000); // Current time in seconds
    const requestTimestamp = parseInt(request.timestamp.toString());
    const expirationTimeBigInt = await withdrawContract.expirationTime();
    const expirationTime = parseInt(expirationTimeBigInt.toString());
    
    console.log(`Request ${requestId} timestamp: ${requestTimestamp}, expiration: ${expirationTime}, now: ${now}`);
    
    // If not yet expired, we can't cancel it
    if (now <= requestTimestamp + expirationTime) {
      console.log(`Request ${requestId} is not yet expired (${now} <= ${requestTimestamp + expirationTime})`);
      throw new Error("Swap request not yet expired");
    }

    // Proceed with cancellation
    const tx = await withdrawContract.cancelSwap(requestId);
    console.log("Cancel transaction hash:", tx.hash);
    const receipt = await tx.wait();
    console.log("Cancel transaction receipt:", receipt);
    console.log("Cancelled swap request ID:", requestId);

    return { success: true, txHash: tx.hash };
  } catch (error) {
    console.error("Cancellation failed:", error);

    // Special handling for expiration-related errors
    if (error.message && (
        error.message.includes("Swap request not yet expired") ||
        error.message.includes("execution reverted") ||
        error.message.includes("Request already processed")
    )) {
      // Even if we can't cancel it on-chain, mark it as expired in our system
      if ((Math.floor(Date.now() / 1000)) > (parseInt((await withdrawContract.swapRequests(requestId)).timestamp.toString()) + parseInt((await withdrawContract.expirationTime()).toString()))) {
        console.log(`Request ${requestId} is expired by timestamp but couldn't be cancelled on-chain: ${error.message}`);
        return { success: true, txHash: "auto-expired-" + Date.now().toString(), status: "EXPIRED" };
      }
    }
    
    throw error;
  }
};

// Function to get combined MVT and USDC transaction list
exports.getTokenTransactionList = async function (address, limit = null) {
  console.log("Fetching combined MVT and USDC transaction list for address:", address);
  console.log("Limit:", limit);

  try {
    // Define a cache key for combined transactions
    var cacheKey = `combined_${address}`;
    var now = Date.now();

    // Check if we have cached data and it's still valid
    if (
      transactionCache.timestamp &&
      now - transactionCache.timestamp < transactionCache.expiryTime &&
      transactionCache.data[cacheKey]
    ) {
      console.log("Using cached combined transaction data");
      var cachedData = transactionCache.data[cacheKey];

      // If limit is specified, return only that many items
      var returnData = limit ? cachedData.slice(0, limit) : cachedData;
      console.log("Returning cached combined data:", returnData);
      return returnData;
    }

    if (!address || typeof address !== "string" || !address.startsWith("0x")) {
      address = wallet ? wallet.address : process.env.MVT_CONTRACT_ADDRESS;
    }

    // Etherscan API endpoint for fetching all token transaction history
    var etherscanUrl = `https://api-sepolia.etherscan.io/api?module=account&action=tokentx&address=${address}&startblock=0&endblock=********&sort=desc&apikey=${process.env.ETHERSCAN_API_KEY}`;

    console.log(
      "Fetching combined transaction history from Etherscan API:",
      etherscanUrl
    );
    
    // Fetch transaction history
    var response = await axios.get(etherscanUrl);
    console.log("Etherscan API response:", response.data);

    if (response.data.status !== "1") {
      console.error("Etherscan API Error:", response.data.message);
      return [];
    }

    var transactions = response.data.result;
    var mvtContractAddressNormalized = process.env.MVT_CONTRACT_ADDRESS.toLowerCase();
    var usdcContractAddressNormalized = process.env.MVT_USDC_CONTRACT_ADDRESS.toLowerCase();

    console.log("Processing combined transaction history...");
    console.log("MVT Contract Address:", mvtContractAddressNormalized);
    console.log("USDC Contract Address:", usdcContractAddressNormalized);
    
    // Process and filter transactions for both MVT and USDC
    var processedTransactions = transactions
      .filter(
        function(tx) {
          const isValidTx = tx.contractAddress && 
                          (tx.contractAddress.toLowerCase() === mvtContractAddressNormalized ||
                           tx.contractAddress.toLowerCase() === usdcContractAddressNormalized);
          
          if (isValidTx) {
            console.log("Found valid transaction for token:", 
              tx.contractAddress.toLowerCase() === mvtContractAddressNormalized ? "MVT" : "USDC");
          }
          
          return isValidTx;
        }
      )
      .map(function(tx) {
        // Check which token this transaction is for
        var isMVT = tx.contractAddress.toLowerCase() === mvtContractAddressNormalized;
        
        // Check transaction status - if confirmations < 12, consider it pending
        var confirmations = parseInt(tx.confirmations || "0");
        var status = confirmations < 12 ? "PENDING" : "COMPLETED";
        
        const txObject = {
          id: tx.hash,
          // Format the amount according to the token's decimals
          amount: ethers.formatUnits(tx.value, isMVT ? 18 : 6), 
          to: tx.to,
          from: tx.from,
          type: determineTransactionType(tx, address),
          date: new Date(tx.timeStamp * 1000).toISOString(),
          status: status,
          // Add token type property to differentiate between MVT and USDC
          tokenType: isMVT ? "MVT" : "USDC"
        };
        
        console.log("Processed transaction object:", txObject);
        return txObject;
      });
    console.log("Processed combined transactions:", processedTransactions);
    console.log("Number of processed transactions:", processedTransactions.length);

    // Get swap request transactions if the address matches the contract or is admin
    var swapRequestTransactions = [];
    if (
      address.toLowerCase() === process.env.MVT_CONTRACT_ADDRESS.toLowerCase() ||
      address.toLowerCase() === process.env.MVT_WITHDRAW_CONTRACT_ADDRESS.toLowerCase() ||
      (wallet && address.toLowerCase() === wallet.address.toLowerCase())
    ) {
      try {
        var swapRequests = await exports.getSwapRequests();
        console.log("Swap requests fetched:", swapRequests.length);
        
        // Add MVT swap transactions
        var mvtSwapTransactions = swapRequests.map((req) => ({
          id: req.txHash || `req-${req.id}`,
          amount: req.mvtAmount,
          to: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
          from: req.user,
          type: "RECEIVED",
          date: req.date,
          status: req.status,
          tokenType: "MVT"
        }));
        
        // Add USDC swap transactions
        var usdcSwapTransactions = swapRequests.map((req) => ({
          id: req.txHash || `req-${req.id}`,
          amount: req.usdcAmount,
          to: req.user,
          from: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
          type: "SENT",
          date: req.date,
          status: req.status,
          tokenType: "USDC"
        }));
        
        swapRequestTransactions = [...mvtSwapTransactions, ...usdcSwapTransactions];
        console.log("Swap request transactions created:", swapRequestTransactions.length);
      } catch (error) {
        console.error("Error fetching swap requests for combined list:", error);
      }
    }

    // Combine regular transactions with swap requests
    var allTransactions = [...processedTransactions, ...swapRequestTransactions]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    
    console.log("All transactions combined:", allTransactions.length);

    // Cache the results
    transactionCache.data[cacheKey] = allTransactions;
    transactionCache.timestamp = now;
    console.log("Cached combined transaction data with", allTransactions.length, "transactions");

    // Return full or limited results
    var returnData = limit ? allTransactions.slice(0, limit) : allTransactions;
    console.log("Returning processed combined data:", returnData.length, "transactions");
    return returnData;
  } catch (error) {
    console.error("Fetching combined transaction list failed:", error);
    return [];
  }
};

// Function to get blockchain configuration
exports.getBlockchainConfig = async function () {
  try {
    // Get the network information from the provider
    const network = await provider.getNetwork();
    const networkName = network.name === 'homestead' ? 'mainnet' : network.name;
    
    // Define Etherscan base URL based on network
    const etherscanBaseUrl = (networkName === 'sepolia') 
      ? 'https://sepolia.etherscan.io/tx/'
      : 'https://etherscan.io/tx/';
    
    return {
      success: true,
      message: "Blockchain configuration retrieved successfully",
      data: {
        network: {
          name: networkName,
          chainId: network.chainId.toString()
        },
        contracts: {
          mvtToken: {
            address: process.env.MVT_CONTRACT_ADDRESS,
            name: "MVT Token"
          },
          withdrawContract: {
            address: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
            name: "MVT Withdraw Contract"
          },
          usdcToken: {
            address: process.env.MVT_USDC_CONTRACT_ADDRESS,
            name: "USDC Token"
          }
        },
        explorer: {
          baseUrl: etherscanBaseUrl
        }
      }
    };
  } catch (error) {
    console.error("Failed to get blockchain configuration:", error);
    return {
      success: false,
      message: "Failed to retrieve blockchain configuration",
      error: error.message,
      data: null
    };
  }
};

// Function to batch update user details on the blockchain
exports.batchUpdateUserDetails = async function (users, firstNames, lastNames, emails) {
  console.log("Batch updating user details on blockchain");

  try {
    // Validate contract initialization
    if (!withdrawContract) {
      throw new Error(
        "Withdraw Contract is not initialized. Check environment variables."
      );
    }

    // Validate input arrays have matching lengths
    if (users.length !== firstNames.length || 
        users.length !== lastNames.length || 
        users.length !== emails.length) {
      throw new Error("All input arrays must have matching lengths");
    }

    // Validate each entry has non-empty values
    for (var i = 0; i < users.length; i++) {
      if (!users[i] || !firstNames[i] || !lastNames[i] || !emails[i]) {
        throw new Error(`Entry at index ${i} has empty values`);
      }
      if (!users[i].startsWith('0x')) {
        throw new Error(`User address at index ${i} is not a valid Ethereum address`);
      }
    }

    // Call the contract's batchUpdateUserDetails function
    var tx = await withdrawContract.batchUpdateUserDetails(
      users,
      firstNames,
      lastNames,
      emails,
      { gasLimit: 3000000 } // Set an appropriate gas limit for batch operations
    );

    console.log("Transaction hash:", tx.hash);
    var receipt = await tx.wait();
    console.log("Transaction receipt:", receipt);

    return { 
      success: true, 
      message: `Updated details for ${users.length} users`,
      txHash: tx.hash
    };
    
  } catch (error) {
    console.error("Batch user details update failed:", error);
    throw error;
  }
};

// Function to extract unique users who have swap requests
exports.getUniqueSwapRequestUsers = async function () {
  console.log("Getting unique users from swap requests");
  
  try {
    // Get all swap requests
    const swapRequests = await exports.getSwapRequests();
    console.log(`Found ${swapRequests.length} swap requests`);
    
    // Extract unique wallet addresses
    const uniqueWalletAddresses = [...new Set(
      swapRequests
        .filter(req => req.user && req.user.startsWith('0x'))
        .map(req => req.user.toLowerCase())
    )];
    
    console.log(`Found ${uniqueWalletAddresses.length} unique wallet addresses`);
    return uniqueWalletAddresses;
    
  } catch (error) {
    console.error("Failed to get unique swap request users:", error);
    throw error;
  }
};
