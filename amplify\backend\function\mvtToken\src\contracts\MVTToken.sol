// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";

contract MVTToken is
    Initializable,
    ERC20Upgradeable,
    OwnableUpgradeable,
    UUPSUpgradeable
{
    bool public mintingFinished;
    mapping(address => bool) public approvedSpenders;

    event Minted(address indexed to, uint256 amount);
    event MintingFinished();
    event SpenderApproved(address indexed spender, uint256 amount);
    event SpenderAuthorized(address indexed spender);
    event SpenderRevoked(address indexed spender);
    event TokensTransferred(address indexed to, uint256 amount);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /// @dev Initializer - called once during proxy deployment
    function initialize(
        string memory name,
        string memory symbol
    ) public virtual initializer {
        __ERC20_init(name, symbol);
        __Ownable_init(msg.sender);
        __UUPSUpgradeable_init();

        mintingFinished = false;
    }

    /// @dev Required for UUPS proxy upgrade auth
    function _authorizeUpgrade(
        address newImplementation
    ) internal virtual override onlyOwner {}

    // ============ MINTING ============

    modifier canMint() {
        require(!_isMintingFinished(), _mintingDisabledMessage());
        _;
    }

    function _isMintingFinished() internal view virtual returns (bool) {
        return mintingFinished;
    }

    function _mintingDisabledMessage()
        internal
        view
        virtual
        returns (string memory)
    {
        return "Minting is permanently disabled";
    }

    function mint(uint256 amount) external virtual onlyOwner canMint {
        require(_isValidMintAmount(amount), _invalidMintAmountMessage());
        _mint(address(this), amount);
        emit Minted(address(this), amount);
    }

    function _isValidMintAmount(
        uint256 amount
    ) internal view virtual returns (bool) {
        return amount > 0;
    }

    function _invalidMintAmountMessage()
        internal
        view
        virtual
        returns (string memory)
    {
        return "Amount must be greater than zero";
    }

    function finishMinting() external virtual onlyOwner {
        mintingFinished = true;
        emit MintingFinished();
    }

    // ============ SPENDER MANAGEMENT ============

    function approveMVT(
        address spender,
        uint256 amount
    ) external virtual onlyOwner {
        require(spender != address(0), "Invalid spender address");
        _approve(address(this), spender, amount);
        emit SpenderApproved(spender, amount);
    }

    function authorizeSpender(address spender) external virtual onlyOwner {
        require(spender != address(0), "Spender cannot be zero address");
        approvedSpenders[spender] = true;
        emit SpenderAuthorized(spender);
    }

    function revokeSpender(address spender) external virtual onlyOwner {
        approvedSpenders[spender] = false;
        emit SpenderRevoked(spender);
    }

    // ============ TRANSFERS ============

    function transferMVT(
        address recipient,
        uint256 amount
    ) external virtual onlyOwner {
        require(recipient != address(0), "Invalid recipient");
        require(balanceOf(address(this)) >= amount, "Insufficient balance");
        _transfer(address(this), recipient, amount);
        emit TokensTransferred(recipient, amount);
    }

    function getMVTBalance(
        address user
    ) external view virtual returns (uint256) {
        return balanceOf(user);
    }

    // ============ STORAGE GAP ============

    /// @dev Reserve storage space for future variable upgrades
    uint256[50] private __gap;
}
