const {
  BedrockRuntimeClient,
  InvokeModelCommand,
} = require("@aws-sdk/client-bedrock-runtime");

const client = new BedrockRuntimeClient({ region: "us-east-1" });

/**
 * @deprecated
 * Converts a JSON response from Bedrock into a human-readable form based on a given question.
 * @param {string} question - The question asked by the user.
 * @param {string} responseOutput - The JSON response from Bedrock.
 * @returns {string|object} The formatted output, or an object with the error message if an error occurred.
 * @throws {Error} If an error occurred while invoking Nova Lite model.
 */

async function respondWithBedrockChatBot(prompt) {
  try {
    const params = {
      modelId: "amazon.nova-lite-v1:0",
      body: JSON.stringify({
        messages: [
          {
            role: "user",
            content: [
              // {
              //   text: prompt,
              //   toolUse:{}
              // },
              {
                toolUse: {
                  name: "chat",
                  toolUseId:"bedrock_chatbot",
                  input: {
                    prompt: prompt
                  }
                }
              }
            ],
          },
        ],
      }),
    };
    const command = new InvokeModelCommand(params);
    const response = await client.send(command);
    const responseBody = JSON.parse(new TextDecoder().decode(response.body));
    const output = responseBody?.output?.message?.content[0]?.text;
    // console.log('output: ', output);

    return output;
  } catch (error) {
    console.error("Error invoking Nova Lite model:", error);
    throw error;
  }
}

module.exports = {
  respondWithBedrockChatBot,
};
