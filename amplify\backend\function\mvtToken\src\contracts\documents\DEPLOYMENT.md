# MVT Token Smart Contracts Deployment Guide

This guide explains how to compile and deploy the MVT Token smart contracts to Ethereum blockchain networks. It provides step-by-step instructions to set up your environment, compile the contracts, and deploy them to a blockchain network.

## Table of Contents
- [Prerequisites](#prerequisites)
- [Contract Overview](#contract-overview)
- [Environment Setup](#environment-setup)
- [Configuration](#configuration)
- [Compiling Contracts](#compiling-contracts)
- [Deployment](#deployment)
- [Verification](#verification)
- [Troubleshooting](#troubleshooting)
- [Upgradable Contracts](#upgradable-contracts)

## Prerequisites

Before starting, ensure you have the following installed:

- [Node.js](https://nodejs.org/) (v16.x or higher recommended)
- [npm](https://www.npmjs.com/) (v8.x or higher)
- [Git](https://git-scm.com/)
- A code editor (like [Visual Studio Code](https://code.visualstudio.com/))
- [MetaMask](https://metamask.io/) or another Ethereum wallet
- Test ETH on your chosen testnet (e.g., Sepolia from a [faucet](https://sepoliafaucet.com/))

## Contract Overview

This project contains two main smart contracts:

1. **MVTToken.sol** - An ERC-20 token contract based on OpenZeppelin's ERC20 implementation with:
   - Minting capability (owner only)
   - Transfer functions
   - Balance inquiry

2. **WithdrawMVTToken.sol** - A contract to manage token swaps between MVT and USDC with:
   - Deposit functionality
   - Swap request and approval system
   - Exchange rate calculation
   - Admin withdrawal capabilities

## Environment Setup

1. Clone the repository (if you haven't already):
   ```bash
   git clone <repository-url>
   cd myvillagefullstack
   ```

2. Install Hardhat and required dependencies:
   ```bash
   cd amplify/backend/function/graphqlCustomFunction/src/contracts
   npm install --save-dev hardhat @nomicfoundation/hardhat-toolbox @nomiclabs/hardhat-ethers ethers @openzeppelin/contracts dotenv
   ```

3. Create a `.env` file in the contracts directory:
   ```bash
   touch .env
   ```

4. Add the following environment variables to the `.env` file:
   ```
   PRIVATE_KEY=your_wallet_private_key
   ETHERSCAN_API_KEY=your_etherscan_api_key
   ALCHEMY_API_KEY=your_alchemy_api_key
   ```
   
   > ⚠️ **IMPORTANT**: Never commit your `.env` file or share your private key with anyone.

## Configuration

1. Create a `hardhat.config.js` file if not already present, or update the existing one:

```javascript
require("@nomicfoundation/hardhat-toolbox");
require("dotenv").config();

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  solidity: {
    version: "0.8.20",
    settings: {
      optimizer: {
        enabled: true,
        runs: 200
      }
    }
  },
  networks: {
    sepolia: {
      url: `https://eth-sepolia.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
      accounts: [process.env.PRIVATE_KEY]
    },
    mainnet: {
      url: `https://eth-mainnet.alchemyapi.io/v2/${process.env.ALCHEMY_API_KEY}`,
      accounts: [process.env.PRIVATE_KEY]
    }
  },
  etherscan: {
    apiKey: process.env.ETHERSCAN_API_KEY
  }
};
```

2. Ensure the USDC token address in the deployment script is correct for your target network:
   - In `scripts/deploy.js`, check the `usdcTokenAddress` variable
   - For Sepolia testnet, you can use a test USDC address or deploy a mock USDC token

## Compiling Contracts

1. Run the following command to compile the contracts:
   ```bash
   npx hardhat compile
   ```

2. This will create a new `artifacts` directory containing the compiled contract ABIs and bytecode.

3. Verify there are no compilation errors.

## Deployment

### Using Hardhat Scripts

1. Review the deployment script at `scripts/deploy.js` to ensure it's configured properly.

2. Deploy to a local Hardhat network for testing:
   ```bash
   npx hardhat run scripts/deploy.js
   ```

3. Deploy to Sepolia testnet:
   ```bash
   npx hardhat run scripts/deploy.js --network sepolia
   ```

4. Deploy to Ethereum mainnet (production):
   ```bash
   npx hardhat run scripts/deploy.js --network mainnet
   ```

5. Save the contract addresses printed in the console. You'll need these to interact with your contracts.

## Verification

After deploying to a public network, verify your contracts on Etherscan:

1. Verify MVTToken:
   ```bash
   npx hardhat verify --network sepolia <MVT_TOKEN_ADDRESS>
   ```

2. Verify WithdrawMVTToken (pass constructor arguments):
   ```bash
   npx hardhat verify --network sepolia <WITHDRAW_MVT_ADDRESS> <MVT_TOKEN_ADDRESS> <USDC_TOKEN_ADDRESS>
   ```

Once verified, your contract source code will be visible on Etherscan, allowing users to interact with it directly.

## Troubleshooting

### Common Deployment Issues

1. **Insufficient Gas**: If deployment fails due to out-of-gas errors, increase the gas limit in your configuration.

2. **Nonce Errors**: If you encounter nonce errors, reset your account's transaction history in MetaMask or use:
   ```bash
   npx hardhat clean
   ```

3. **Contract Size Limit**: If your contract exceeds the size limit (24kB), reduce complexity or split functionality.

4. **Network Connection Issues**: Ensure your RPC endpoint is correct and responsive.

### Validation Issues

If you encounter issues with contract validation on Etherscan:

1. Ensure the compiler versions match
2. Verify that all imports are available

If you encounter any issues during deployment, please review the logs for specific error messages and refer to the Hardhat documentation for troubleshooting guidance.

## Upgradable Contracts

### What You CAN Do in Future Upgrades

These changes will NOT break the proxy or storage layout:

🔧 Code Logic
- ✅ Change function bodies (logic)
- ✅ Change or improve require(...) error messages
- ✅ Add new modifiers
- ✅ Add new functions
- ✅ Override virtual functions
- ✅ Add new events
- ✅ Add internal/private helper functions
- ✅ Refactor logic while keeping storage untouched

🧠 State Variables
- ✅ Add new variables at the end of the storage
- ✅ Add new mappings, structs, or arrays at the end

```solidity
// Safe to add:
uint256 public version;
mapping(address => uint256) public stakes;
string public tokenTagline;
```

### What You CANNOT Change in Future Upgrades

These will break the contract or make it unsafe:

🚫 Storage Layout
- ❌ Reorder existing variables
- ❌ Rename or remove existing variables
- ❌ Change type of existing variables
- ❌ Add variables in between existing ones

🚫 Functionality
- ❌ Add a constructor with logic (should stay empty or disabled)
- ❌ Call initialize() again — it’s run once, and should never re-run
- ❌ Use selfdestruct or delegatecall in dangerous ways


You deploy MVTTokenV2, then upgrade the proxy to point to this new logic.
