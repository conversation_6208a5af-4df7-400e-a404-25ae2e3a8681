const { createLogger } = require('../logger');
const {
  formatDate,
  formatUserInfo,
  formatMetadata,
  withErrorHandling,
} = require('./commonFormatters');

const logger = createLogger('knowledgeDataFormatter');

/**
 * Formats a help article into a readable string
 * @param {Object} article - Help article data
 * @returns {string} Formatted article info
 */
function formatArticle(article) {
  if (!article) return 'No article data';

  return (
    `### ${article.title || 'Untitled Article'}
` +
    (article.category ? `**Category**: ${article.category}\n` : '') +
    (article.lastUpdated
      ? `*Last updated: ${formatDate(article.lastUpdated, { includeTime: false })}*\n`
      : '') +
    (article.content ? `\n${article.content}\n` : '') +
    (article.relatedArticles?.length > 0
      ? `\n**Related Articles**: ${article.relatedArticles.join(', ')}\n`
      : '') +
    '---\n\n'
  );
}

/**
 * Formats a tutorial into a readable string
 * @param {Object} tutorial - Tutorial data
 * @returns {string} Formatted tutorial info
 */
function formatTutorial(tutorial) {
  if (!tutorial) return 'No tutorial data';

  return (
    `### ${tutorial.title || 'Untitled Tutorial'}
` +
    (tutorial.difficulty ? `**Difficulty**: ${tutorial.difficulty}\n` : '') +
    (tutorial.duration ? `**Duration**: ${tutorial.duration} minutes\n` : '') +
    (tutorial.objectives?.length > 0
      ? `**Objectives**:\n${tutorial.objectives.map((obj) => `- ${obj}`).join('\n')}\n`
      : '') +
    (tutorial.steps?.length > 0
      ? `\n**Steps**:\n${tutorial.steps.map((step, i) => `${i + 1}. ${step}`).join('\n')}\n`
      : '') +
    '---\n\n'
  );
}

/**
 * Converts knowledge base query data into a meaningful summary
 * @param {Object} data - Raw knowledge data from the query
 * @returns {string} Formatted summary of the knowledge data
 */
function formatKnowledgeData(data) {
  if (!data) {
    logger.warn('No knowledge data provided to formatter');
    return 'No knowledge base data available';
  }

  return withErrorHandling(
    (data) => {
      // If data is already in the new format (from fetchKnowledgeData), use it directly
      if (data._metadata?.dataSource === 'knowledge') {
        return formatStructuredKnowledgeData(data);
      }

      // Legacy format handling (kept for backward compatibility)
      return formatLegacyKnowledgeData(data);
    },
    data,
    'Error processing knowledge base data. Please try again later.',
    'knowledgeDataFormatter'
  );
}

/**
 * Formats knowledge data in the new structured format
 * @param {Object} data - Structured knowledge data
 * @returns {string} Formatted knowledge data
 */
function formatStructuredKnowledgeData(data) {
  let summary = '';

  // User Information
  if (data.user) {
    summary += formatUserInfo(
      {
        givenName: data.user.givenName,
        familyName: data.user.familyName,
        id: data.user.id,
        accessLevel: data.accessLevel || 'Standard',
      },
      'Knowledge Base Access'
    );
  }

  // Featured Articles
  if (data.featuredArticles?.length > 0) {
    summary += `## Featured Articles (${data.featuredArticles.length})\n\n`;
    data.featuredArticles.forEach((article) => {
      summary += formatArticle(article);
    });
  }

  // Recent Tutorials
  if (data.recentTutorials?.length > 0) {
    summary += `## Recent Tutorials (${data.recentTutorials.length})\n\n`;
    data.recentTutorials.forEach((tutorial) => {
      summary += formatTutorial(tutorial);
    });
  }

  // Popular Topics
  if (data.popularTopics?.length > 0) {
    summary += `## Popular Topics\n\n`;
    summary += data.popularTopics.map((topic) => `- ${topic}`).join('\n');
    summary += '\n\n';
  }

  // FAQ Categories
  if (data.faqCategories?.length > 0) {
    summary += `## Frequently Asked Questions\n\n`;
    data.faqCategories.forEach((category) => {
      summary += `### ${category.name || 'General'}\n`;
      if (category.questions?.length > 0) {
        category.questions.forEach((q) => {
          summary += `**Q:** ${q.question}\n`;
          summary += `**A:** ${q.answer}\n\n`;
        });
      }
    });
  }

  // Platform Updates
  if (data.platformUpdates?.length > 0) {
    const recentUpdates = [...data.platformUpdates]
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 3); // Show only 3 most recent updates

    if (recentUpdates.length > 0) {
      summary += `## Recent Platform Updates\n\n`;
      recentUpdates.forEach((update) => {
        summary += `### ${update.title || 'Update'} (${formatDate(update.date, { includeTime: false })})\n`;
        summary += `${update.description || ''}\n`;
        if (update.impact) {
          summary += `**Impact:** ${update.impact}\n`;
        }
        summary += '\n';
      });
    }
  }

  // Add metadata
  summary += formatMetadata(data._metadata, data._metadata?.timestamp);

  return summary;
}

/**
 * Formats legacy knowledge data structure
 * @param {Object} data - Legacy knowledge data
 * @returns {string} Formatted knowledge data
 */
function formatLegacyKnowledgeData(data) {
  let summary = '';

  // User Information
  summary += formatUserInfo(
    {
      givenName: data.givenName,
      familyName: data.familyName,
      id: data.id,
      accessLevel: data.accessLevel || 'Standard',
    },
    'Knowledge Base Access'
  );

  // Featured Articles
  if (data.featuredArticles?.items?.length > 0) {
    summary += `## Featured Articles (${data.featuredArticles.items.length})\n\n`;
    data.featuredArticles.items.forEach((article) => {
      summary += formatArticle(article);
    });
  }

  // Recent Tutorials
  if (data.recentTutorials?.items?.length > 0) {
    summary += `## Recent Tutorials (${data.recentTutorials.items.length})\n\n`;
    data.recentTutorials.items.forEach((tutorial) => {
      summary += formatTutorial(tutorial);
    });
  }

  // Popular Topics
  if (data.popularTopics?.length > 0) {
    summary += `## Popular Topics\n\n`;
    summary += data.popularTopics.map((topic) => `- ${topic}`).join('\n');
    summary += '\n\n';
  }

  // FAQ Categories
  if (data.faqCategories?.length > 0) {
    summary += `## Frequently Asked Questions\n\n`;
    data.faqCategories.forEach((category) => {
      summary += `### ${category.name || 'General'}\n`;
      if (category.questions?.length > 0) {
        category.questions.forEach((q) => {
          summary += `**Q:** ${q.question}\n`;
          summary += `**A:** ${q.answer}\n\n`;
        });
      }
    });
  }

  // Platform Updates
  if (data.platformUpdates?.items?.length > 0) {
    const recentUpdates = data.platformUpdates.items
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 3); // Show only 3 most recent updates

    if (recentUpdates.length > 0) {
      summary += `## Recent Platform Updates\n\n`;
      recentUpdates.forEach((update) => {
        summary += `### ${update.title || 'Update'} (${formatDate(update.date, { includeTime: false })})\n`;
        summary += `${update.description || ''}\n`;
        if (update.impact) {
          summary += `**Impact:** ${update.impact}\n`;
        }
        summary += '\n';
      });
    }
  }

  // Add metadata
  summary += formatMetadata(data._metadata);

  return summary;
}

module.exports = {
  formatKnowledgeData,
  formatArticle,
  formatTutorial,
  formatStructuredKnowledgeData,
  formatLegacyKnowledgeData,
};
