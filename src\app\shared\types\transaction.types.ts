export enum TransactionType {
  ALL = 'all',
  MVT = 'mvt',
  USDC = 'usdc'
}

export enum TransactionStatus {
  COMPLETED = 'COMPLETED',
  PENDING = 'PENDING',
  FAILED = 'FAILED'
}

export enum SwapRequestStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  FAILED = 'FAILED',
  EXPIRED = 'EXPIRED'
}

export enum TransactionOperationType {
  SENT = 'Sent',
  RECEIVED = 'Received',
  ADDED = 'Added'
}

export interface TransactionUser {
  id: string;
  givenName?: string;
  familyName?: string;
  name?: string;
  email?: string;
}

export interface Transaction {
  id: string;
  date: Date;
  type: TransactionOperationType;
  originalType?: string; // Store original backend transaction type for context
  amount: number;
  status: TransactionStatus;
  currency: TransactionType;
  from?: string;
  to?: string;
  fromUser?: TransactionUser; // User details for sender
  toUser?: TransactionUser;   // User details for recipient
  adminUser?: TransactionUser; // User details for admin
  // Blockchain-related fields
  transactionHash?: string;  // Blockchain transaction hash for Etherscan links
  blockNumber?: number;      // Block number where transaction was mined
  gasUsed?: number;          // Gas used for the transaction
  // Display-ready fields from backend
  displayType?: string;      // SENT, RECEIVED, ADDED
  primaryLabel?: string;     // "To: John Doe", "From: Admin", etc.
  secondaryInfo?: string;    // "MVT Transaction", "USDC Transaction"
  showEtherscanLink?: boolean; // Whether to show Etherscan link
  formattedDate?: string;    // Pre-formatted date string
}

export interface SwapRequest {
  id: string;
  date: Date;
  userId: string;
  userName: string;
  walletAddress: string;
  usdcAmount: number;
  mvtAmount: number;
  status: SwapRequestStatus;
  transactionHash?: string;
}

export enum TokenActiveTab {
  MVT = 'mvt',
  USDC = 'usdc',
  WITHDRAW_USDC = 'withdraw-usdc'
}

export const ETHERSCAN_BASE_URL = 'https://sepolia.etherscan.io/tx/';
export const ZERO_ADDRESS = '******************************************'; 