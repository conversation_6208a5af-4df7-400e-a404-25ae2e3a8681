import { Component, EventEmitter, Output } from '@angular/core';

@Component({
  selector: 'app-mvt-token-faq',
  templateUrl: './mvt-token-faq.component.html',
  styleUrls: ['./mvt-token-faq.component.scss']
})
export class MvtTokenFaqComponent {
  @Output() close = new EventEmitter<void>();

  faqs = [
    {
      question: 'What is the MVT Token?',
      answer: 'MVT Token is a utility token used within the MyVillage platform for rewards and transactions. It operates through an internal wallet system managed by the platform.'
    },
    {
      question: 'How do I check my MVT balance?',
      answer: 'Your MVT balance is displayed on the dashboard, showing total balance, available balance, and any locked tokens from pending swap requests.'
    },
    {
      question: 'What is the difference between total and available balance?',
      answer: 'Total balance shows all your MVT tokens. Available balance excludes tokens locked in pending swap requests. Only available tokens can be used for new transactions.'
    },
    {
      question: 'How does the MVT to USDC swap work?',
      answer: 'Submit a swap request specifying the MVT amount. Your tokens are immediately locked until admin approval. Upon approval, MVT is transferred to the liquidity pool and USDC is sent to your wallet.'
    },
    {
      question: 'What happens to my MVT tokens during a swap request?',
      answer: 'MVT tokens are immediately locked when you submit a swap request. They remain locked until the request is approved (tokens transferred) or rejected (tokens unlocked).'
    },
    {
      question: 'How do I approve or reject swap requests?',
      answer: 'Admins can view pending requests in the swap approval section. Approving transfers locked MVT to the liquidity pool and sends USDC to the user. Rejecting unlocks the MVT tokens back to the user.'
    },
    {
      question: 'What are the liquidity pools?',
      answer: 'MVT Liquidity Pool holds MVT tokens from completed swaps. USDC Liquidity Pool holds USDC available for swap payouts. Both pools determine the exchange rate and swap availability.'
    },
    {
      question: 'How is the exchange rate calculated?',
      answer: 'Exchange rate is calculated as USDC Reserve ÷ MVT Reserve from the liquidity pools. A 2% safety buffer is applied to protect against slippage.'
    },
    {
      question: 'What does "Fallback Mode" mean?',
      answer: 'Fallback Mode activates when there are liquidity issues or system constraints. A conservative rate of 0.5 USDC per MVT is used until normal operations resume.'
    },
    {
      question: 'Can I transfer MVT tokens to other users?',
      answer: 'Yes, you can transfer MVT tokens to other platform users using the transfer feature. Only available (unlocked) tokens can be transferred.'
    },
    {
      question: 'What should I do if a transaction fails?',
      answer: 'Check your available balance and transaction details. Common issues include insufficient available balance or network errors. Contact support if the issue persists.'
    },
    {
      question: 'How do I manage USDC liquidity?',
      answer: 'Admins can deposit USDC to the liquidity pool to ensure sufficient funds for swap requests. Monitor the pool balance to maintain adequate liquidity.'
    }
  ];

  onClose() {
    this.close.emit();
  }
}
