{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"Windows\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.0.0\",\"stackType\":\"function-Lambda\",\"metadata\":{}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "authmyvillageprojectadmifeb4ea87UserPoolId": {"Type": "String", "Default": "authmyvillageprojectadmifeb4ea87UserPoolId"}, "apimyvillageprojectadmiGraphQLAPIIdOutput": {"Type": "String", "Default": "apimyvillageprojectadmiGraphQLAPIIdOutput"}, "apimyvillageprojectadmiGraphQLAPIEndpointOutput": {"Type": "String", "Default": "apimyvillageprojectadmiGraphQLAPIEndpointOutput"}, "accessKeyId": {"Type": "String"}, "secretAccessKey": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "notificationFunction", {"Fn::Join": ["", ["notificationFunction", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID": {"Ref": "authmyvillageprojectadmifeb4ea87UserPoolId"}, "API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT": {"Ref": "apimyvillageprojectadmiGraphQLAPIIdOutput"}, "API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT": {"Ref": "apimyvillageprojectadmiGraphQLAPIEndpointOutput"}, "ACCESS_KEY_ID": {"Ref": "accessKeyId"}, "SECRET_ACCESS_KEY": {"Ref": "secretAccessKey"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs16.x", "Layers": [], "Timeout": 25}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "myvillageprojectadmiLambdaRole220184e3", {"Fn::Join": ["", ["myvillageprojectadmiLambdaRole220184e3", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "AmplifyResourcesPolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["cognito-identity:Describe*", "cognito-identity:Get*", "cognito-identity:List*", "cognito-idp:Describe*", "cognito-idp:AdminGetDevice", "cognito-idp:AdminGetUser", "cognito-idp:AdminList*", "cognito-idp:List*", "cognito-sync:Describe*", "cognito-sync:Get*", "cognito-sync:List*", "iam:ListOpenIdConnectProviders", "iam:ListRoles", "sns:ListPlatformApplications"], "Resource": [{"Fn::Join": ["", ["arn:aws:cognito-idp:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":userpool/", {"Ref": "authmyvillageprojectadmifeb4ea87UserPoolId"}]]}]}, {"Effect": "Allow", "Action": ["appsync:GraphQL"], "Resource": [{"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apimyvillageprojectadmiGraphQLAPIIdOutput"}, "/types/Query/*"]]}, {"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apimyvillageprojectadmiGraphQLAPIIdOutput"}, "/types/Mutation/*"]]}, {"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apimyvillageprojectadmiGraphQLAPIIdOutput"}, "/types/Subscription/*"]]}]}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}