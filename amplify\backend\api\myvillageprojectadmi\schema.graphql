enum ActivityType {
  CREATED
  UPDATED
  DELETED
  ACTIVATED
  DEACTIVATED
  ADDED
  REMOVED
  VIEWED
  TOKENS
  GAME
}

enum HomeworkAutoGeneratedFromEnum {
  EVENT
}

enum ActivityTypeName {
  MEMBERSHIP
  PROJECT
  STORYTELLING
  FUNDING
}
enum IdeaStatusName {
  REPORTED
  ASSIGNED
  COMPLETED
}
enum UserAddedFromEnum {
  USER_FROM_MOBILE

  STAKEHOLDER_IMPORTED
  MEMBER_IMPORTED
  STUDENT_IMPORTED

  STAKEHOLDER_CREATED
  MEMBER_CREATED
  STUDENT_CREATED

  USER_IMPORTED_MEMBER
  USER_IMPORTED_ASSIGNMENT
  USER_IMPORTED_EVENT
  USER_IMPORTED_SCHOOL

  USER_FROM_MEMBER
  USER_FROM_ASSIGNMENT
  USER_FROM_EVENT
  USER_FROM_BUSINESS

  STUDENT_FROM_CHAT
}

enum associationCommunity {
  COMMUNITY_FOUNDATION_FOR_NORTHEAST_FLORIDA
  COMMUNITY_FOUNDATION_TAMPA_BAY
  CENTRAL_FLORIDA_FOUNDATION
  COMMUNITY_FOUNDATION_OF_NORTH_CENTRAL_FLORIDA
  MIAMI_COMMUNITY_FOUNDATION
}

enum UserAccessType {
  SUPER_ADMIN
  STAFF_MEMBER
  MEMBER
  SUBSCRIBER
}

enum AssignedRoleType {
  SUPER_ADMIN
  STAFF_MEMBER
  MEMBER
  SUBSCRIBER
  SCHOOL_OWNER
  SCHOOL_EMPLOYEE
  CUSTOMER
  PRESIDENT
  EXECUTIVE_DIRECTOR
  BOARD_CHAIR
  BOARD_MEMBER
  STAFF
  VOLUNTEER
}

enum PaymentStatus {
  PENDING
  APPROVED
}

enum AmountStatus {
  DEBITED
  CREDITED
}

enum requestStatusName {
  SYSTEM_APPROVED
  ADMIN_APPROVED
  ADMIN_DENIED
  PENDING_APPROVAL
}

enum MessageType {
  TEXT
  FILE
  URL
}

enum GenderType {
  MALE
  FEMALE
  OTHER
  BOTH
  NONE
}

enum SubmissionStatus {
  APPROVED
  INREVIEW
  DENIED
  DELETED
}

enum MessageBy {
  SUPERADMIN
  USER
}

enum KnowledgeFileType {
  AUDIO
  VIDEO
}

enum KnowledgeEntityType {
  STAKEHOLDER
  STUDENT
  ORGANIZATION
}

type Activity @model @auth(rules: [{ allow: private }]) {
  id: ID!
  type: ActivityType!
  moduleId: ID!
  moduleUser: User @hasOne(fields: ["moduleId"])
  moduleMember: Organizations @hasOne(fields: ["moduleId"])
  moduleName: String!
  moduleType: String!
  moduleImageUrl: String
  createdUserId: ID
  # createdUserId: String!
  createdUserName: String
  isRelationship: Boolean
  relatedTo: String
  relatedId: ID
  relatedUser: User @hasOne(fields: ["relatedId"])
  relatedMember: Organizations @hasOne(fields: ["relatedId"])
  relatedName: String
  cityId: String
  activityType: ActivityTypeName
  activityTokens: Float
  requestStatus: requestStatusName
  updatedData: FieldsArray
  gameData: GameData
  user: User @hasOne(fields: ["createdUserId"])
  isDeleted: String
    @index(
      name: "activitiesByDate"
      queryField: "activitiesByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type GameData {
  appName: String
  duration: String
  gameName: String
  gameLoginTime: AWSDateTime
}

type City @model @auth(rules: [{ allow: private }]) {
  id: ID!
  name: String
  code: String
  abbreviation: String
  isDeleted: String @default(value: "false")
}

type User @model @auth(rules: [{ allow: private }]) {
  id: ID!
  email: String
  familyName: String
  givenName: String
  name: String
  phoneNumber: String
  cityId: ID
  stackholderCities: [ID]
  cityData: City @hasOne(fields: ["cityId"])
  imageUrl: String
  streetAddressOne: String
  streetAddressTwo: String
  city: String
  state: String
  zipCode: String
  countryCode: String
  role: UserAccessType
  assignedRole: AssignedRoleType @default(value: "BOARD_MEMBER")
  birthday: String
  ethnicity: String
  gender: GenderType @default(value: "NONE")
  profileUrl: String
  type: String
  impactScore: String
  status: String
  registeredFrom: String
  isAssociated: Boolean @default(value: "false")
  userType: String
  isSignup: Boolean
  deviceId: String
  FCMToken: String
  endpointArn: String
  loginPlatform: String
  isLogin: Boolean
  otherDeviceId: String
  otherFCMToken: String
  isLoginOther: Boolean
  isStakeholder: Boolean
  defaultMessageDate: AWSDateTime
  tasks: [Task] @manyToMany(relationName: "TaskUser")
  homeworks: [HomeworkUsers] @hasMany
  associations: [Association] @hasMany #otherPersonsID from Association
  userAssociations: [Association] @hasMany #personsID from Association
  post: [Submission] @hasMany
  membershipId: ID
  membership: Membership @hasOne(fields: ["membershipId"])
  customerId: ID
  subscriptions: [Subscriptions] @hasMany
  userMessages: [Message] @hasMany
  # userChannels: [UserChannel] @hasMany
  memberCode: String
  verificationCode: String
  codeExpiration: Int
  phoneNumberVerified: Boolean
  studentEnrollmentDate: AWSDateTime
  socketConnection: String
  logoutTime: AWSDateTime
  userAddedFrom: UserAddedFromEnum @default(value: "STAKEHOLDER_CREATED")
  cognitoId: String
  knowledgeRepositoryStore: [KnowledgeRepositoryStore] @hasMany(fields: ["id"])
  knowledgeRepositoryStoreSubmitted: [KnowledgeRepositoryStore]
    @hasMany(fields: ["id"])
  knowledgeRepositoryStoreSummary: [KnowledgeRepositoryStoreSummary]
    @hasMany(fields: ["id"])
  walletAddress: String @default(value: "")
  isDeleted: String
    @index(
      name: "userByDate"
      queryField: "userByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type Subscriptions @model @auth(rules: [{ allow: private }]) {
  id: ID!
  userId: ID
  customerId: ID
  priceId: ID
  productId: ID
  stripeSubscriptionId: ID
  startDate: AWSDateTime
  renewalDate: AWSDateTime
  cancelDate: AWSDateTime
  status: String
  isDeleted: String
    @index(
      name: "subscriptionsByDate"
      queryField: "subscriptionsByDate"
      sortKeyFields: ["createdAt"]
    )
  createdAt: AWSDateTime!
}

type FieldsArray {
  id: ID
  email: String
  familyName: String
  givenName: String
  phoneNumber: String
  cityId: String
  birthday: String
  imageUrl: String
  gender: GenderType
  walletAddress: String
}

type Task @model @auth(rules: [{ allow: private }]) {
  id: ID!
  type: String
  personsID: ID
  person: User @hasOne(fields: ["personsID"])
  updatedData: FieldsArray
  requestStatus: String
  title: String
  shortDescription: String
  longDescription: String
  dueDate: AWSDateTime
  files: [String]
  cityId: String
  createdBy: ID
  user: User @hasOne(fields: ["createdBy"])
  membersType: String
  organizations: [Organizations] @manyToMany(relationName: "TaskOrganizations")
  users: [User] @manyToMany(relationName: "TaskUser")
  business: [Business] @manyToMany(relationName: "TaskBusiness")
  status: String!
  category: String
  isAllUsers: Boolean
  notifications: [Notifications] @hasMany
  isDeleted: String
    @index(
      name: "tasksByDate"
      queryField: "tasksByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type Submission @model @auth(rules: [{ allow: private }]) {
  id: ID!
  text: String
  description: String
  videos: [String]
  images: [String]
  audios: [String]
  videosThumbnail: [String]
  categoryType: String
  location: String
  projectType: String
  projectId: ID
  homework: Homework @hasOne(fields: ["projectId"])
  memberId: ID
  videoDuration: Int
  cityId: String
  isPublic: Boolean @default(value: "false")
  viewCount: Int @default(value: "0")
  likeCount: Int @default(value: "0")
  isPostLike: Boolean @default(value: "false")
  pointsAssigned: Boolean
  postDateTime: AWSDateTime
  isActive: String
  submissionStatus: SubmissionStatus @default(value: "INREVIEW")
  createdBy: ID!
  user: User @belongsTo(fields: ["createdBy"])
  business: Business @belongsTo(fields: ["memberId"])
  organization: Organizations @belongsTo(fields: ["memberId"])
  person: User @belongsTo(fields: ["memberId"])
  postFeedback: [PostFeedback] @hasMany
  grade: Float
  feedback: String
  gradedBy: String
  isDeleted: String
    @index(
      name: "SubmissionByDate"
      queryField: "SubmissionByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  transcriptDetailId: ID
  transcriptDetail: TranscriptDetail @hasOne(fields: ["transcriptDetailId"])
  createdAt: AWSDateTime!
}

type Membership @model @auth(rules: [{ allow: private }]) {
  id: ID!
  type: String
  name: String
  shortDescription: String
  imageUrl: String
  personsID: ID
  person: User @belongsTo(fields: ["personsID"])
  organizationID: ID
  organization: Organizations @belongsTo(fields: ["organizationID"])
  businessID: ID
  business: Business @belongsTo(fields: ["businessID"])
  isActive: Boolean
  lastAddedImpactScore: String
  currentImpactScore: String
  MVPTokens: Float
  fundTokens: Float
  cityId: String
  memberCode: String
  isDeleted: String
    @index(
      name: "membershipByDate"
      queryField: "membershipByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime
}

type AssociationRelationType @model @auth(rules: [{ allow: private }]) {
  id: ID!
  type: String!
  relationName: String!
  associations: [Association] @hasMany(fields: ["id"])
  isDeleted: String
    @index(
      name: "associationRelationTypeByDate"
      queryField: "associationRelationTypeByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type Association @model @auth(rules: [{ allow: private }]) {
  id: ID!
  type: String!
  relationTypeID: ID
  associationRelationType: AssociationRelationType
    @belongsTo(fields: ["relationTypeID"])
  relationType: String!
  personsID: ID
  person: User @belongsTo(fields: ["personsID"])
  otherPersonsID: ID
  otherPerson: User @belongsTo(fields: ["otherPersonsID"])
  organizationID: ID
  organization: Organizations @belongsTo(fields: ["organizationID"])
  businessID: ID
  business: Business @belongsTo(fields: ["businessID"])
  startDateTime: AWSDateTime
  endDateTime: AWSDateTime
  cityId: String
  status: Boolean!
  isFromMobile: Boolean
  createdBy: ID
  user: User @belongsTo(fields: ["createdBy"])
  isDeleted: String
    @index(
      name: "associationsByDate"
      queryField: "associationsByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type Organizations @model @auth(rules: [{ allow: private }]) {
  id: ID!
  type: String!
  teamSize: String!
  name: String!
  organizationStructure: String!
  chapterName: String
  organizationFunction: String!
  shortDescription: String!
  longDescription: String
  estimatedAnnualBudget: Float!
  organizationImpact: String
  contactFullName: String!
  contactEmail: String!
  contactPhoneNumber: String!
  contactRole: String
  isActive: Boolean!
  MOUSigned: Boolean
  boardConfirmed: Boolean
  cityId: ID
  cityData: City @hasOne(fields: ["cityId"])
  status: String
  OrganizationsEvents: [Events] @manyToMany(relationName: "EventsOrganizations")
  imageUrl: String
  organizationUserId: ID
  organizationUser: User @hasOne(fields: ["organizationUserId"])
  tasks: [Task] @manyToMany(relationName: "TaskOrganizations")
  homeworks: [HomeworkOrganizations] @hasMany
  createdBy: ID
  user: User @hasOne(fields: ["createdBy"])
  associations: [Association] @hasMany
  post: [Submission] @hasMany
  membershipId: ID
  membership: Membership @hasOne(fields: ["membershipId"])
  memberDocuments: [MemberDocumentsObj]
  memberCode: String
  bankAccountNumber: String
  bankAccountRoutingNumber: String
  accountType: String
  mailedDisbursements: Boolean @default(value: "true")
  associatedCommunityFoundation: associationCommunity
  foundationAddress: String
  address: String
  memberAddress: String
  knowledgeRepositoryStore: [KnowledgeRepositoryStore] @hasMany(fields: ["id"])
  knowledgeRepositoryStoreSummary: [KnowledgeRepositoryStoreSummary]
    @hasMany(fields: ["id"])
  isDeleted: String
    @index(
      name: "organizationsByDate"
      queryField: "organizationsByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type MemberDocumentsObj {
  name: String
  fileSize: Float
  category: String
  updateDate: AWSDateTime
}

type Events @model @auth(rules: [{ allow: private }]) {
  id: ID!
  name: String!
  shortDescription: String!
  longDescription: String
  type: String!
  categoryID: ID
  categoryData: Categories @hasOne(fields: ["categoryID"])
  startDateTime: AWSDateTime!
  endDateTime: AWSDateTime!
  contactFullName: String!
  contactEmail: String!
  contactPhoneNumber: String!
  contactRole: String!
  primaryContact: ID
  organizations: String
  cityId: String
  EventsOrganizations: [Organizations]
    @manyToMany(relationName: "EventsOrganizations")
  streetAddress1: String
  streetAddress2: String
  city: String
  state: String
  zipcode: String
  status: Boolean
  structure: String!
  infoUrl: String
  files: [String]
  projectImage: String
  favoriteData: [FavoriteEvents] @hasMany
  createdBy: ID
  user: User @hasOne(fields: ["createdBy"])
  isDeleted: String
    @index(
      name: "eventsByDate"
      queryField: "eventsByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type FavoriteEvents @model @auth(rules: [{ allow: private }]) {
  id: ID!
  userId: ID!
  userData: User @hasOne(fields: ["userId"])
  favoriteEventId: ID!
  eventData: Events @belongsTo(fields: ["favoriteEventId"])
  isDeleted: String
    @index(
      name: "favoriteEventsByDate"
      queryField: "favoriteEventsByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type Programs @model @auth(rules: [{ allow: private }]) {
  id: ID!
  name: String!
  shortDescription: String!
  longDescription: String
  type: String!
  status: Boolean!
  memberType: String
  cityId: String!
  gender: GenderType @default(value: "NONE")
  minAge: String
  maxAge: String
  perquisitesData: [Perquisites] @hasMany
  homework: [Homework] @hasMany
  homeworkOrganizations: [HomeworkOrganizations] @hasMany
  homeworkUsers: [HomeworkUsers] @hasMany
  imageUrl: String
  totalPoints: Float
  categoryID: ID
  categoryData: Categories @hasOne(fields: ["categoryID"])
  createdBy: ID
  user: User @hasOne(fields: ["createdBy"])
  receivedPoints: Float
  remainingPoints: Float
  homeworkAssignDate: AWSDateTime
  completedHomeworkCount: Int
  microCredentialCode: String
  isDeleted: String
    @index(
      name: "programsByDate"
      queryField: "programsByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type Perquisites @model @auth(rules: [{ allow: private }]) {
  id: ID!
  programId: ID!
  programData: Programs @belongsTo(fields: ["programId"])
  perquisiteId: ID!
  perquisiteData: Programs @belongsTo(fields: ["perquisiteId"])
  isDeleted: String
    @index(
      name: "perquisitesByDate"
      queryField: "perquisitesByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type Business @model @auth(rules: [{ allow: private }]) {
  id: ID!
  name: String!
  type: String!
  shortDescription: String!
  longDescription: String
  imageUrl: String
  businessUserId: ID
  businessUser: User @hasOne(fields: ["businessUserId"])
  cityId: String
  createdBy: ID
  user: User @hasOne(fields: ["createdBy"])
  tasks: [Task] @manyToMany(relationName: "TaskBusiness")
  associations: [Association] @hasMany
  post: [Submission] @hasMany
  membershipId: ID
  membership: Membership @hasOne(fields: ["membershipId"])
  minAge: String
  maxAge: String
  estTotalStudents: String
  mvpMember: Boolean
  schoolAddressOne: String
  schoolAddressTwo: String
  city: String
  state: String
  zipCode: String
  phoneNumber: String
  isDeleted: String
    @index(
      name: "businessByDate"
      queryField: "businessByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type Points @model @auth(rules: [{ allow: private }]) {
  id: ID!
  memberId: ID!
  organization: Organizations @hasOne(fields: ["memberId"])
  person: User @hasOne(fields: ["memberId"])
  impactScore: String!
  MVPTokens: Float
  pointType: String
  type: String!
  categoryID: ID
  categoryData: Categories @hasOne(fields: ["categoryID"])
  cityId: String
  category: String
  paymentStatus: PaymentStatus
  status: AmountStatus
  memo: String
  createdBy: ID
  user: User @hasOne(fields: ["createdBy"])
  isDeleted: String
    @index(
      name: "pointsByDate"
      queryField: "pointsByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type PostViews @model @auth(rules: [{ allow: private }]) {
  id: ID!
  viewUserId: ID!
  postId: ID!
}

type PostLikes @model @auth(rules: [{ allow: private }]) {
  id: ID!
  likeUserId: ID!
  postId: ID!
}

type Notifications @model @auth(rules: [{ allow: private }]) {
  id: ID!
  title: String!
  description: String
  userId: ID!
  user: User @hasOne(fields: ["userId"])
  imageUrl: String
  isRead: Boolean
  notificationType: String
  notificationIcon: String
  feedbackId: ID
  feedback: String
  feedbackDate: AWSDateTime
  homeworkId: ID
  homeworkDueDate: AWSDateTime
  homework: Homework @hasOne(fields: ["homeworkId"])
  MVPTokens: Float
  points: Float
  isAnswerable: Boolean @default(value: "true")
  task: Task @belongsTo(fields: ["feedbackId"])
  isDeleted: String
    @index(
      name: "notificationsByDate"
      queryField: "notificationsByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type Categories @model @auth(rules: [{ allow: private }]) {
  id: ID!
  name: String
  description: String
  cityId: String
  parentId: ID # For subcategories, references parent category
  parent: Categories @belongsTo(fields: ["parentId"])
  subcategories: [Categories] @hasMany(fields: ["id"])
  knowledgeRepositoryStore: [KnowledgeRepositoryStore] @hasMany(fields: ["id"])
  knowledgeRepositoryStoreSummary: [KnowledgeRepositoryStoreSummary]
    @hasMany(fields: ["id"])
  isDeleted: String @default(value: "false")
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime
}

type PostFeedback @model @auth(rules: [{ allow: private }]) {
  id: ID!
  feedbackUserId: ID!
  postId: ID!
}

type Ideas @model @auth(rules: [{ allow: private }]) {
  id: ID!
  cityId: ID
  name: String!
  shortDescription: String!
  longDescription: String
  personsID: ID
  ideaID: String
  status: IdeaStatusName
  createdBy: ID
  category: String
  submittedBy: User @hasOne(fields: ["personsID"])
  isDeleted: String
    @index(
      name: "ideasByDate"
      queryField: "ideasByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type SubmissionType {
  isImage: Boolean!
  isVideo: Boolean!
  isAudio: Boolean!
}

type Homework @model @auth(rules: [{ allow: private }]) {
  id: ID!
  name: String!
  shortDescription: String!
  longDescription: String
  dueDate: AWSDateTime
  imageUrl: String
  coCreationType: String
  categoryType: String
  assignmentType: String
  microcredentialId: ID
  microcredential: Programs @belongsTo(fields: ["microcredentialId"])
  entityType: String
  members: [HomeworkOrganizations] @hasMany
  studentsStakeholders: [HomeworkUsers] @hasMany
  assignmentPoints: Float
  primaryContact: ID
  primaryContactDetail: User @hasOne(fields: ["primaryContact"])
  isCompleted: String
  cityId: String!
  createdBy: ID
  associationId: ID
  association: Boolean
  submissionType: SubmissionType
  completeCount: Int
  selfAssignment: Boolean
  maxAssignment: Int
  assignmentInstruction: String
  homeworkAutoGeneratedFrom: HomeworkAutoGeneratedFromEnum
  user: User @hasOne(fields: ["createdBy"])
  isDeleted: String
    @index(
      name: "homeworkByDate"
      queryField: "homeworkByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type HomeworkOrganizations @model @auth(rules: [{ allow: private }]) {
  id: ID!
  homeworkId: ID!
  homeworkData: Homework @belongsTo(fields: ["homeworkId"])
  memberId: ID!
  memberData: Organizations @belongsTo(fields: ["memberId"])
  homeworkStatus: String
  homeworkAssignDate: AWSDateTime
  homeworkCompletedDate: AWSDateTime
}

type HomeworkUsers @model @auth(rules: [{ allow: private }]) {
  id: ID!
  homeworkId: ID!
  homeworkData: Homework @belongsTo(fields: ["homeworkId"])
  studentStakeholderId: ID!
  studentStakeholderData: User @belongsTo(fields: ["studentStakeholderId"])
  homeworkStatus: String
  homeworkAssignDate: AWSDateTime
  homeworkCompletedDate: AWSDateTime
}

type CityFundTransactions @model @auth(rules: [{ allow: private }]) {
  id: ID!
  invoiceId: String
  cityId: ID!
  cityData: City @hasOne(fields: ["cityId"])
  name: String
  productName: String
  type: String
  memberId: ID
  memberData: Organizations @hasOne(fields: ["memberId"])
  userData: User @hasOne(fields: ["memberId"])
  amount: Float
  amountStatus: AmountStatus
  customerId: String
  invoiceDatetime: AWSDateTime
  recurring: Boolean
  timeInterval: String
  subscriptionId: String
  previousAmount: Float
  currentAmount: Float
  description: String
  reversed: Boolean
  isDeleted: String
    @index(
      name: "cityFundTransactionsByDate"
      queryField: "cityFundTransactionsByDate"
      sortKeyFields: ["createdAt"]
    )
  createdAt: AWSDateTime!
}

type ChatGPT @model @auth(rules: [{ allow: private }]) {
  id: ID!
  userId: ID!
  userData: User @hasOne(fields: ["userId"])
  chatType: String
  message: String
  role: String
  messageType: MessageType
  fileUrl: String
  isDeleted: String
    @index(
      name: "chatGPTByDate"
      queryField: "chatGPTByDate"
      sortKeyFields: ["createdAt"]
    )
  createdAt: AWSDateTime!
}

type chatbotPrompt @model @auth(rules: [{ allow: private }]) {
  id: ID!
  chatType: String!         # e.g., "community-assistant", "event-lookup", etc.
  title: String             # Optional title or name for the prompt
  description: String       # Brief summary of what this prompt is for
  promptText: String!       # Full prompt template (with variables like ${userId}, ${question}, etc.)
  lastUpdated: AWSDateTime  # Optional: track when it was last modified
}

type ChatbotPromptsData @model @auth(rules: [{ allow: private }]) {
  id: ID!
  userId: ID!
  userData: User @hasOne(fields: ["userId"])
  promptData: String
  chatType: String
  isDeleted: String
    @index(
      name: "chatbotPromptsDataByDate"
      queryField: "chatbotPromptsDataByDate"
      sortKeyFields: ["createdAt"]
    )
  createdAt: AWSDateTime!
}

type Message @model @auth(rules: [{ allow: private }]) {
  id: ID!
  senderId: ID!
  sender: User @belongsTo(fields: ["senderId"])
  recipientId: ID
  recipient: User @belongsTo(fields: ["recipientId"])
  channelId: ID!
  channel: Channel @belongsTo(fields: ["channelId"])
  content: String!
  messageBy: MessageBy
  isRead: Boolean!
  seenBy: ID
  isDeleted: String
    @index(
      name: "messageByDate"
      queryField: "messageByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type Channel @model @auth(rules: [{ allow: private }]) {
  id: ID!
  name: String!
  # userChannels: [UserChannel] @hasMany
  messages: [Message] @hasMany
  isDeleted: String
    @index(
      name: "channelByDate"
      queryField: "channelByDate"
      sortKeyFields: ["createdAt"]
    )
    @default(value: "false")
  createdAt: AWSDateTime!
}

type KnowledgeRepositoryStore @model @auth(rules: [{ allow: private }]) {
  id: ID!
  name: String!
  description: String
  fileUrl: String!
  transcriptDetailId: ID
  transcriptDetail: TranscriptDetail @hasOne(fields: ["transcriptDetailId"])
  fileType: KnowledgeFileType!
  durationInMinutes: Float
  entityType: KnowledgeEntityType!
  cityId: String
  status: String
  userId: ID
  user: User @belongsTo(fields: ["userId"])
  organizationId: ID
  organization: Organizations @belongsTo(fields: ["organizationId"])
  categoryId: ID!
  category: Categories @belongsTo(fields: ["categoryId"])
  subCategoryIds: [String]
  submittedBy: ID!
  submittedByUser: User @belongsTo(fields: ["submittedBy"])
  isDeleted: String @default(value: "false")
  isPublic: Boolean @default(value: "false")
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime
}

type KnowledgeRepositoryStoreSummary @model @auth(rules: [{ allow: private }]) {
  id: ID!
  totalMinutes: Float!
  lastUploadDate: AWSDateTime
  cityId: String
  userId: ID
  user: User @belongsTo(fields: ["userId"])
  organizationId: ID
  organization: Organizations @belongsTo(fields: ["organizationId"])
  categoryId: ID!
  category: Categories @belongsTo(fields: ["categoryId"])
  isDeleted: String @default(value: "false")
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime
}

type TranscriptDetail @model @auth(rules: [{ allow: private }]) {
  id: ID!
  transcription: String
  summary: String
  jobName: String
  accountId: String
  status: String
  results: AWSJSON
  metadata: AWSJSON
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime
}

type MVTTokenWalletData {
  id: ID!
  balance: Float!
  lastMintedAt: AWSDateTime
}

type MVTTokenWalletResponse {
  statusCode: Int
  message: String
  data: MVTTokenWalletData
}

input MintMVTTokenInput {
  amount: Float!
}

# Bedrock Chatbot Types
input BedrockChatInput {
  message: String!
  userId: ID
  sessionId: String
}

type BedrockChatData {
  response: String!
  sessionId: String
  timestamp: AWSDateTime
  model: String
  usage: BedrockUsage
}

type BedrockUsage {
  inputTokens: Int
  outputTokens: Int
  totalTokens: Int
}

type BedrockChatResponse {
  statusCode: Int
  message: String
  data: BedrockChatData
}

# Stripe Onramp Types
type OnrampSessionData {
  sessionId: String!
  clientSecret: String!
  livemode: Boolean
}

type OnrampSessionResponse {
  statusCode: Int
  message: String
  data: OnrampSessionData
}

# Webhook types removed - using frontend-triggered transfers

type MVTTokenWallet @model @auth(rules: [{ allow: private }]) {
  id: ID!
  balance: Float!
  lastMintedAt: AWSDateTime
  totalMinted: Float @default(value: "0")
  totalTransferred: Float @default(value: "0")
  totalReceived: Float @default(value: "0")
  isDeleted: String @default(value: "false")
  createdAt: AWSDateTime!
}

# MVT Wallet Transaction Schema (New Wallet System)
type MVTWalletTransaction @model @auth(rules: [{ allow: private }]) {
  id: ID!
  transactionType: MVTWalletTransactionType!
  amount: Float!
  fromWalletId: String
  toWalletId: String
  fromUserId: ID
  toUserId: ID
  fromUser: User @hasOne(fields: ["fromUserId"])
  toUser: User @hasOne(fields: ["toUserId"])
  status: MVTWalletTransactionStatus!
  transactionHash: String
  internalTxId: String
  description: String
  adminUserId: ID
  adminUser: User @hasOne(fields: ["adminUserId"])
  gasUsed: Float
  gasPrice: Float
  blockNumber: String
  confirmations: Int
  metadata: AWSJSON
  isDeleted: String @default(value: "false")
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

# MVT Swap Request Schema (New Wallet System)
type MVTSwapRequest @model @auth(rules: [{ allow: private }]) {
  id: ID!
  userId: ID!
  user: User @hasOne(fields: ["userId"])
  userWalletAddress: String!
  mvtAmount: Float!
  usdcAmount: Float!
  exchangeRate: Float!
  status: String!
  description: String
  requestedAt: AWSDateTime!
  processedAt: AWSDateTime
  expiresAt: AWSDateTime!
  adminUserId: ID
  adminUser: User @hasOne(fields: ["adminUserId"])
  rejectionReason: String
  transactionHash: String
  isDeleted: String @default(value: "false")
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

# User Balance Tracking
type UserMVTBalance @model @auth(rules: [{ allow: private }]) {
  id: ID!
  userId: ID!
  user: User @hasOne(fields: ["userId"])
  balance: Float!
  pendingBalance: Float @default(value: "0")
  lockedBalance: Float @default(value: "0")
  lastUpdated: AWSDateTime!
  totalReceived: Float @default(value: "0")
  totalSent: Float @default(value: "0")
  isDeleted: String @default(value: "false")
  createdAt: AWSDateTime!
}





# MVT Wallet Management Input Types
input AdminMintMVTInput {
  amount: Float!
  description: String
}

input AdminTransferMVTInput {
  userId: ID!
  amount: Float!
  description: String
}

input UserBalanceQueryInput {
  userId: ID
}

input UserTransferMVTInput {
  recipientUserId: ID!
  amount: Float!
  description: String
}

input USDCDepositInput {
  amount: Float!
  description: String
}

input USDCWithdrawalInput {
  amount: Float!
  description: String
}

input AdminTransferUSDCInput {
  userId: ID!
  amount: Float!
  description: String
}

input SwapRequestInput {
  mvtAmount: Float!
  description: String
}

input SwapApprovalInput {
  swapRequestId: ID!
}

input SwapRejectionInput {
  swapRequestId: ID!
  rejectionReason: String!
}

# MVT Wallet Management Response Types
type MVTWalletData {
  id: ID!
  balance: Float!
  lastMintedAt: AWSDateTime
  totalMinted: Float!
  totalTransferred: Float!
  createdAt: AWSDateTime!
}

type MVTWalletResponse {
  statusCode: Int!
  message: String!
  data: MVTWalletData
}

type UserBalanceData {
  userId: ID!
  balance: Float!
  pendingBalance: Float!
  lockedBalance: Float!
  availableBalance: Float!
  totalReceived: Float!
  totalSent: Float!
  lastUpdated: AWSDateTime!
  recentTransactions: [MVTWalletTransactionData]
}

type UserBalanceResponse {
  statusCode: Int!
  message: String!
  data: UserBalanceData
}

type TransactionData {
  id: ID!
  transactionType: MVTWalletTransactionType!
  amount: Float!
  status: MVTWalletTransactionStatus!
  description: String
  transactionHash: String
  createdAt: AWSDateTime!
}

type TransactionResponse {
  statusCode: Int!
  message: String!
  data: TransactionData
}

# User data for transaction display
type MVTWalletTransactionUser {
  id: ID!
  givenName: String
  familyName: String
  name: String
  email: String
}

# MVT Wallet Transaction Data (New System)
type MVTWalletTransactionData {
  id: ID!
  type: MVTWalletTransactionType!  # Alias for transactionType for backward compatibility
  transactionType: MVTWalletTransactionType!
  tokenType: String!
  amount: Float!
  from: String  # Alias for fromWalletId/fromUserId
  to: String    # Alias for toWalletId/toUserId
  fromWalletId: String
  toWalletId: String
  fromUserId: ID
  toUserId: ID
  fromUser: MVTWalletTransactionUser  # User details for sender
  toUser: MVTWalletTransactionUser    # User details for recipient
  adminUser: MVTWalletTransactionUser # User details for admin
  status: MVTWalletTransactionStatus!
  transactionHash: String
  internalTxId: String
  description: String
  adminUserId: ID
  gasUsed: Float
  gasPrice: Float
  blockNumber: String
  confirmations: Int
  metadata: AWSJSON
  timestamp: AWSDateTime!  # Alias for createdAt
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
  # Display-ready fields from backend
  displayType: String!      # SENT, RECEIVED, ADDED
  primaryLabel: String!     # "To: John Doe", "From: Admin", etc.
  secondaryInfo: String!    # "MVT Transaction", "USDC Transaction"
  showEtherscanLink: Boolean! # Whether to show Etherscan link
  formattedDate: String!    # Pre-formatted date string
}

type MVTWalletTransactionResponse {
  statusCode: Int!
  message: String!
  data: MVTWalletTransactionData
}

type MVTWalletTransactionListResponse {
  statusCode: Int!
  message: String!
  data: [MVTWalletTransactionData]
}

# Onramp Transfer Response Data
type OnrampTransferData {
  mvtTransactionId: String!
  usdcTransactionId: String
  newBalance: Float!
  transferAmount: Float!
}

type OnrampTransferResponse {
  statusCode: Int!
  message: String!
  data: OnrampTransferData
}

# Onramp Transfer Status Data
type OnrampTransferStatusData {
  processed: Boolean!
  transactionId: String
  timestamp: AWSDateTime
}

type OnrampTransferStatusResponse {
  statusCode: Int!
  message: String!
  data: OnrampTransferStatusData
}

# USDC Liquidity Pool Types
type USDCLiquidityPoolData {
  totalReserves: Float!
  availableBalance: Float!
  adminWalletAddress: String
  lastUpdated: AWSDateTime!
}

type USDCLiquidityPoolResponse {
  statusCode: Int!
  message: String!
  data: USDCLiquidityPoolData
}

# Note: Exchange Rate and Swap Request types moved to MVTWallet-specific types below

type SwapProcessingResult {
  success: Boolean!
  swapRequestId: ID!
  mvtAmount: Float
  usdcAmount: Float
  userWalletAddress: String
  transactionHash: String
  newUserBalance: Float
  processedAt: AWSDateTime
  status: String
  rejectionReason: String
}

type SwapProcessingResponse {
  statusCode: Int!
  message: String!
  data: SwapProcessingResult
}

# MVT Wallet Specific Response Types (Hybrid Internal/Blockchain System)
type MVTWalletExchangeRateData {
  currentRate: Float!
  rateDisplay: String!
  liquidityStatus: MVTWalletLiquidityStatus!
  lastUpdated: AWSDateTime!
}

type MVTWalletLiquidityStatus {
  usdcReserves: Float!
  mvtSupply: Float!
  liquidityRatio: Float!
  status: String!
}

type MVTWalletExchangeRateResponse {
  statusCode: Int!
  message: String!
  data: MVTWalletExchangeRateData
}

type MVTWalletSwapRequestData {
  id: ID!
  userId: ID!
  userName: String
  userWalletAddress: String!
  mvtAmount: Float!
  usdcAmount: Float!
  exchangeRate: Float!
  status: String!
  description: String
  requestedAt: AWSDateTime!
  processedAt: AWSDateTime
  expiresAt: AWSDateTime!
  adminUserId: ID
  rejectionReason: String
  transactionHash: String
}

type MVTWalletSwapRequestResponse {
  statusCode: Int!
  message: String!
  data: MVTWalletSwapRequestData
}

type MVTWalletSwapRequestListResponse {
  statusCode: Int!
  message: String!
  data: [MVTWalletSwapRequestData]
}

# type UserChannel @model @auth(rules: [{ allow: private }]) {
#   id: ID!
#   userId: ID!
#   user: User @belongsTo(fields: ["userId"])
#   channelId: ID!
#   channel: Channel! @belongsTo(fields: ["channelId"])
#   isDeleted: String
#     @index(
#       name: "userChannelByDate"
#       queryField: "userChannelByDate"
#       sortKeyFields: ["createdAt"]
#     )
#     @default(value: "false")
#   createdAt: AWSDateTime!
# }

# Custom query for graphQl
type Response {
  recentMembers: [Membership]
  impactLeaders: [Membership]
  growingImpact: [Membership]
  members: [Membership]
}

type getRecentActivityResponse {
  data: [Activity]
}

type cardsDataResponse {
  categoryName: String
  categorySubName: String
  myVillageScore: Float
  communityScore: Float
  microcredential: [Programs]
}

type pointsByCategoriesResponse {
  STEMCreativity: Float
  AcademicsTesting: Float
  HealthWellness: Float
  HistoryTradition: Float
  BusinessFinance: Float
}

type commonResponse {
  message: String
  statusCode: Int
  data: Data
}

type Data {
  success: Int
  fail: [String]
}

type checkAssociationExistResponse {
  isExist: Boolean
  message: String
}

type checkEmailPhoneNumberExistResponse {
  isExist: Boolean
  message: String
}

type viewMicrocredentialsResponse {
  data: [Programs]
}

type ModifiedHomeworkResponse {
  homeworkStatus: String
  homeworkData: Homework
  _deleted: String
}

type latestAndPopularHomeworksResponse {
  items: [ModifiedHomeworkResponse]
}

type getAssignmentListMobileResponse {
  data: getAssignmentListMobileRes
}

type HomeworkMobileData {
  homeworkStatus: String
  homeworkData: HomeworkData
}

type ListHomeworkDataMobile {
  items: [HomeworkMobileData]
}

type getAssignmentListMobileRes {
  listHomeworkUsers: ListHomeworkUsersMobile
  listHomeworkOrganizations: ListHomeworkOrganizationsMobile
  listAvailableAssignments: ListHomeworkDataMobile
}

type ListHomeworkUsersMobile {
  items: [HomeworkUserMobile]
}

type HomeworkUserMobile {
  homeworkStatus: String
  homeworkData: HomeworkData
}

type ListHomeworkOrganizationsMobile {
  items: [HomeworkOrganizationMobile]
}

type HomeworkOrganizationMobile {
  homeworkStatus: String
  homeworkData: HomeworkData
}

type latestHomeworksResponse {
  items: [ModifiedHomeworkResponse]
}

type StudentStakeholderData {
  imageUrl: String
  id: String
  name: String
}

type Microcredential {
  id: String
  name: String
  type: String
}

type HomeworkData {
  id: String
  name: String
  imageUrl: String
  dueDate: String
  assignmentPoints: Int
  assignmentType: String
  shortDescription: String
  longDescription: String
  coCreationType: String
  cityId: String
  homeworkAutoGeneratedFrom: String
  createdBy: String
  rating: Int
  createdAt: String
  _version: Int
  _deleted: Boolean
  isDeleted: String
  microcredential: Microcredential
}

type HomeworkUser {
  homeworkStatus: String
  homeworkData: HomeworkData
  _deleted: Boolean
  studentStakeholderData: StudentStakeholderData
}

type HomeworkOrganization {
  homeworkStatus: String
  homeworkData: HomeworkData
  _deleted: Boolean
  memberData: memberData
}

type memberData {
  imageUrl: String
  id: String
  name: String
}

type ListHomeworkUsers {
  items: [HomeworkUser]
}

type ListHomeworkOrganizations {
  items: [HomeworkOrganization]
}

type getMemberAssignmentsRes {
  listHomeworkUsers: ListHomeworkUsers
  listHomeworkOrganizations: ListHomeworkOrganizations
}

type getMemberAssignmentsResponse {
  data: getMemberAssignmentsRes
}

type memberChatAssistantResponse {
  User: String
  Assistant: String
}

type userTokensChatResponse {
  User: String
  Assistant: String
}

type submissionsChatResponse {
  User: String
  Assistant: String
}

type membershipEventsChatResponse {
  User: String
  Assistant: String
}

type communityEventsChatResponse {
  User: String
  Assistant: String
}

type academicChatResponse {
  User: String
  Assistant: String
}

type TranscribeAlternative {
  confidence: Float
  content: String
}

type TranscribeItem {
  start_time: Float
  end_time: Float
  type: String
  alternatives: [TranscribeAlternative]
  speaker_label: String
}

type TranscribeTranscript {
  transcript: String
}

type TranscribeSegmentItem {
  start_time: Float
  end_time: Float
  speaker_label: String
}

type TranscribeSegment {
  start_time: Float
  end_time: Float
  speaker_label: String
  items: [TranscribeSegmentItem]
}

type TranscribeSpeakerLabels {
  segments: [TranscribeSegment]
  speakers: Int
}

type TranscribeResults {
  transcripts: [TranscribeTranscript]
  items: [TranscribeItem]
  speaker_labels: TranscribeSpeakerLabels
}

type TranscribeResult {
  jobName: String
  accountId: String
  status: String
  results: TranscribeResults
}

type TranscribeResponse {
  status: String!
  data: AWSJSON
  message: String
  jobName: String
  s3URI: String
}

type getAssignmentDetailResponse {
  id: ID!
  name: String!
  shortDescription: String!
  longDescription: String
  dueDate: AWSDateTime
  imageUrl: String
  coCreationType: String
  assignmentPoints: Float
  primaryContact: ID
  cityId: String!
  createdAt: AWSDateTime!
  isAssociated: Boolean
  isSubmitted: Boolean
  isAssigned: Boolean
  memberImage: String
  microcredential: Programs
  message: String
}

type Query {
  # Membership data custome query
  getMembershipData(
    cityId: String!
    userId: String!
    userType: String
  ): Response @function(name: "customQueryFunction-${env}")
  getRecentActivity(
    cityId: String!
    userId: String!
  ): getRecentActivityResponse @function(name: "customQueryFunction-${env}")
  getCardsData(
    memberId: String!
    type: String!
    cityId: [ID]
  ): [cardsDataResponse] @function(name: "customQueryFunction-${env}")
  pointsByCategories(memberId: ID): pointsByCategoriesResponse
    @function(name: "customQueryFunction-${env}")
  checkAssociationExist(
    userId: ID!
    memberId: ID!
  ): checkAssociationExistResponse @function(name: "customQueryFunction-${env}")
  viewMicrocredentials(
    memberId: ID!
    type: String!
    isCompleted: Boolean!
  ): viewMicrocredentialsResponse @function(name: "customQueryFunction-${env}")
  checkEmailPhoneNumberExist(
    email: String
    phoneNumber: String
  ): checkEmailPhoneNumberExistResponse
    @function(name: "customQueryFunction-${env}")
  removeCityData(cityId: ID!, transferCityId: ID): commonResponse
    @function(name: "graphqlCustomFunction-${env}")
  removeCategoryData(categoryId: ID!, transferCategoryId: ID!): commonResponse
    @function(name: "graphqlCustomFunction-${env}")

  latestAndPopularHomeworks(
    memberId: ID
    memberType: String
    cityId: [ID]
    type: String
    assignmentType: String
    isStakeholder: Boolean
  ): latestAndPopularHomeworksResponse
    @function(name: "customQueryFunction-${env}")

  getAssignmentListMobile(
    memberId: ID
    memberType: String
    cityId: [ID]
    type: String
    assignmentType: String
    isStakeholder: Boolean
    gender: GenderType
  ): getAssignmentListMobileResponse
    @function(name: "graphqlCustomFunction-${env}")

  latestHomeworks(
    memberId: ID
    memberType: String
    cityId: ID
    isStakeholder: Boolean
  ): latestHomeworksResponse @function(name: "customQueryFunction-${env}")

  getMemberAssignments(
    memberId: ID
    assignmentType: String
    cityId: String
  ): getMemberAssignmentsResponse
    @function(name: "graphqlCustomFunction-${env}")

  memberChatAssistant(
    userId: ID!
    username: String!
    question: String!
  ): memberChatAssistantResponse @function(name: "graphqlCustomFunction-${env}")
  userTokensChat(
    userId: ID!
    username: String!
    question: String!
  ): userTokensChatResponse @function(name: "graphqlCustomFunction-${env}")
  submissionsChat(
    userId: ID!
    username: String!
    question: String!
  ): submissionsChatResponse @function(name: "graphqlCustomFunction-${env}")
  communityEventsChat(
    userId: ID!
    username: String!
    question: String!
  ): communityEventsChatResponse @function(name: "graphqlCustomFunction-${env}")

  membershipEventsChat(
    userId: ID!
    username: String!
    question: String!
  ): membershipEventsChatResponse
    @function(name: "graphqlCustomFunction-${env}")

  # Bedrock chatbot AI
  bedrockChatBot(
    question: String!
    userId: ID!
    chatType: String!
  ): membershipEventsChatResponse
    @function(name: "bedrockchatbot-${env}")

  fetchChatbotData(userId: ID!, chatType: String!): commonResponse
    @function(name: "bedrockchatbot-${env}")

  removeChatbotData(userId: ID!): commonResponse
    @function(name: "bedrockchatbot-${env}")

  transcribeVideoOrAudio(
    s3URI: String!
    id: ID!
    transcriptionModule: String!
  ): TranscribeResponse @function(name: "graphqlCustomFunction-${env}")

  academicChat(
    userId: ID!
    username: String!
    question: String!
  ): academicChatResponse @function(name: "graphqlCustomFunction-${env}")
  getAssignmentDetail(
    homeworkId: ID
    userId: ID
    memberId: ID
  ): getAssignmentDetailResponse @function(name: "graphqlCustomFunction-${env}")
  getListOfUserChannel(userId: String): commonResponse
    @function(name: "customQueryFunction-${env}")



  # New MVT Wallet Operations
  getMVTTokenWalletBalance(dummy: String = ""): MVTTokenWalletResponse
    @function(name: "mvtTokenInternal-${env}")

  # Bedrock Chatbot Operations
  testBedrockChat(input: BedrockChatInput!): BedrockChatResponse
    @function(name: "bedrockchatbot-${env}")

  # MVT Wallet Management Operations
  # Admin Operations
  getAdminMVTWalletBalance: MVTWalletResponse @function(name: "mvtWallet-${env}")

  # User Operations
  # Regular users: No userId needed (uses Cognito identity)
  # Admins: Can pass userId to check other users' balances
  getUserMVTWalletBalance(input: UserBalanceQueryInput): UserBalanceResponse @function(name: "mvtWallet-${env}")

  # MVT Wallet Transaction Operations
  getMVTWalletTransactionList(
    address: String!
    isAdmin: Boolean = false
    limit: Int
  ): MVTWalletTransactionListResponse @function(name: "mvtWallet-${env}")

  getMVTWalletTransactionById(transactionId: ID!): MVTWalletTransactionResponse @function(name: "mvtWallet-${env}")

  # USDC Liquidity Pool Operations (Internal MVT Wallet)
  getUSDCLiquidityPool: USDCLiquidityPoolResponse @function(name: "mvtWallet-${env}")
  getMVTWalletExchangeRate: MVTWalletExchangeRateResponse @function(name: "mvtWallet-${env}")

  # MVT Swap Operations (Internal MVT Wallet)
  getMVTWalletSwapRequests(
    isAdmin: Boolean = false
    limit: Int = 50
    excludeFailed: Boolean = true
  ): MVTWalletSwapRequestListResponse @function(name: "mvtWallet-${env}")

  getMVTWalletSwapRequestById(swapRequestId: ID!): MVTWalletSwapRequestResponse @function(name: "mvtWallet-${env}")

  # Frontend-Triggered Transfer Status Check
  checkOnrampTransferStatus(sessionId: String!): OnrampTransferStatusResponse @function(name: "mvtWallet-${env}")

}

input UserList {
  id: String
  endpointArn: String
  isLogin: Boolean
}

input notificationInput {
  title: String
  body: String
  taskNotificationsId: ID
  notificationType: String
  notificationIcon: String
  feedbackId: ID
  feedback: String
  feedbackDate: AWSDateTime
  MVPTokens: Float
  points: Float
  isAnswerable: Boolean
  homeworkId: ID
  homeworkDueDate: AWSDateTime
  userList: [UserList]
}

input CreateEventsInput {
  id: ID
  name: String!
  shortDescription: String!
  longDescription: String
  type: String!
  startDateTime: AWSDateTime!
  endDateTime: AWSDateTime!
  contactFullName: String!
  contactEmail: String!
  contactPhoneNumber: String!
  contactRole: String!
  organizations: [String]
  cityId: String
  streetAddress1: String
  streetAddress2: String
  city: String
  state: String
  zipcode: String
  status: Boolean
  structure: String!
  primaryContact: ID
  infoUrl: String
  files: [String]
  createdBy: ID
  isDeleted: String
  createdAt: AWSDateTime
  _version: Int
  createdUserName: String
  projectImage: String
  categoryID: ID
}

input importOrgInput {
  items: [orgItems]
}

input orgItems {
  id: ID
  type: String!
  teamSize: String!
  name: String!
  organizationStructure: String!
  chapterName: String
  organizationFunction: String!
  shortDescription: String!
  longDescription: String
  estimatedAnnualBudget: Float!
  organizationImpact: String
  # contactFullName: String!
  contactFirstName: String
  contactLastName: String
  contactEmail: String
  contactPhoneNumber: String
  contactRole: String
  isActive: Boolean!
  MOUSigned: Boolean
  boardConfirmed: Boolean
  cityId: String
  status: String
  imageUrl: String
  organizationUserId: ID
  createdBy: ID
  isDeleted: String
  createdAt: AWSDateTime
  _version: Int
}

input importStudentsInput {
  items: [userInputItems]
}

input importStakeholdersInput {
  items: [userInputItems]
}

input userInputItems {
  id: ID
  email: String
  familyName: String
  givenName: String
  name: String
  phoneNumber: String
  cityId: ID
  imageUrl: String
  streetAddressOne: String
  streetAddressTwo: String
  city: String
  state: String
  zipCode: String
  countryCode: String
  role: UserAccessType
  birthday: String
  ethnicity: String
  gender: GenderType = NONE
  profileUrl: String
  type: String
  impactScore: String
  status: String
  registeredFrom: String
  isAssociated: Boolean
  userType: String
  deviceId: String
  FCMToken: String
  endpointArn: String
  loginPlatform: String
  isLogin: Boolean
  otherDeviceId: String
  otherFCMToken: String
  isLoginOther: Boolean
  isStakeholder: Boolean
  membershipId: ID
  isDeleted: String
  createdAt: AWSDateTime
  _version: Int
  createdUserId: ID
  createdUserName: String
  associationType: String
}

input ImportSchoolInput {
  items: [ImportSchoolItems]
  createdUserId: ID
  createdUserName: String
  cityId: ID
}

input ImportSchoolItems {
  cityId: String!
  type: String
  name: String!
  shortDescription: String
  longDescription: String
  minAge: String
  maxAge: String
  estTotalStudents: String
  mvpMember: Boolean
  schoolAddressOne: String
  schoolAddressTwo: String
  city: String
  state: String
  zipCode: String
  phoneNumber: String
  businessUserId: String
  contactFullName: String
  contactPhoneNumber: String
  contactEmail: String
  contactRole: String
}

input importEventsInput {
  items: [eventInputItems]
}

input eventInputItems {
  name: String!
  shortDescription: String!
  longDescription: String
  type: String!
  startDateTime: AWSDateTime!
  endDateTime: AWSDateTime!
  contactFullName: String!
  contactEmail: String!
  contactPhoneNumber: String!
  contactRole: String!
  cityId: String
  streetAddress1: String
  streetAddress2: String
  city: String
  state: String
  zipcode: String
  status: Boolean
  structure: String!
  infoUrl: String
}

input createTaskRelationshipsInput {
  taskId: ID
  memberId: ID
}

input deleteOldFeedbackInput {
  notificationIds: [ID]
}

input studentsInput {
  items: userInputItems
}

type createStudentResponse {
  isExist: Boolean
  message: String
  statusCode: Int
  userId: ID
}

input deleteAssociationsInput {
  associationIds: [ID]
}

input deleteFavoriteEventsInput {
  favoriteEventsId: [ID]
}

input deleteNotificationInput {
  userId: ID
}

input DeleteHomeworkRelationInput {
  memberRelationIds: [ID]
  userRelationIds: [ID]
}

input DeletePerquisitesInput {
  perquisitesId: [ID]
  programId: ID
  deleteType: String
}

input UpdateHomeworkStatusInput {
  homeworkId: ID
  entityId: ID
  entityType: String
}

type commonResponseWithData {
  message: String
  statusCode: Int
  data: DataObj
}



# MVT Wallet Transaction Types (New Wallet System)
enum MVTWalletTransactionType {
  WALLET_MINT
  WALLET_TRANSFER
  ADMIN_MINT
  ADMIN_TRANSFER
  USER_TO_USER_TRANSFER
  CENTRAL_TO_USER_TRANSFER
  USER_TO_CENTRAL_TRANSFER
  REWARD_DISTRIBUTION
  PENALTY_DEDUCTION
  SYSTEM_ADJUSTMENT
  USDC_DEPOSIT
  USDC_WITHDRAWAL
  SWAP_REQUEST
  SWAP_APPROVED
  SWAP_REJECTED
}

# MVT Wallet Transaction Status (New Wallet System)
enum MVTWalletTransactionStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REJECTED
  APPROVED
  CONFIRMED
  FINALIZED
}



type DataObj {
  id: ID
  fundTokens: Float
  isBalance: Boolean
}

input SendTokensToMembersInput {
  tokenAmount: Float
  projectId: ID
  membersId: [ID]
  associationId: ID
}

input DeleteUserRelationsInput {
  homeworkRelationIds: [ID]
}

input DeleteAssociationRelationsInput {
  associationRelationIds: [ID]
  status: Boolean
}

input ImportTransactionsInput {
  items: [ImportTransactionItems]
  createdUserId: ID
  createdUserName: String
  cityId: ID
}

input ImportTransactionItems {
  cityId: ID!
  name: String
  productName: String
  type: String
  memberId: ID
  amount: Float
  amountStatus: AmountStatus
  customerId: String
  invoiceDatetime: AWSDateTime
  recurring: Boolean
  timeInterval: String
  subscriptionId: String
  previousAmount: Float
  currentAmount: Float
  description: String
}

input importIdeasInput {
  items: [ideaInputItems]
}

input ideaInputItems {
  cityId: ID
  name: String!
  shortDescription: String!
  personsID: ID
  status: IdeaStatusName
  createdBy: ID
  isDeleted: String
  category: String
  longDescription: String
}

input ClearChatByUserInput {
  userId: ID
  chatType: String
}

input ImportAssignmentsInput {
  items: [ImportAssignmentItems]
  createdUserId: ID
  createdUserName: String
  cityId: ID
}

input ImportAssignmentItems {
  name: String!
  shortDescription: String!
  longDescription: String
  dueDate: AWSDateTime
  imageUrl: String
  coCreationType: String
  categoryType: String
  assignmentType: String
  microcredentialId: String
  entityType: String
  memberIds: String
  studentIds: String
  stakeholderIds: String
  assignmentPoints: Float
  primaryContact: String
  contactFullName: String
  contactPhoneNumber: String
  contactEmail: String
  contactRole: String
  cityId: String!
  createdBy: ID
}

input UpdateUserDetailInput {
  id: ID!
  familyName: String
  givenName: String
  streetAddressOne: String
  streetAddressTwo: String
  city: String
  state: String
  zipCode: String
  birthday: String
  ethnicity: String
  gender: GenderType = NONE
  status: String
}

input DeleteUserDetailInput {
  userId: ID
}

input SelfAssignAssignmentInput {
  homeworkId: ID
  memberId: ID
  memberType: String
}

input SendMailInput {
  firstName: String
  memberCode: String
  cityLocation: String
  to: String
  phoneNumber: Int
}

input WithdrawAdminUSDCInput {
  amount: Float!
}

type Mutation {
  # Set post view count
  updatePostViewsData(viewUserId: ID!, postId: ID!): commonResponse
    @function(name: "customQueryFunction-${env}")
  updatePostLikesData(
    likeUserId: ID!
    postId: ID!
    isPostLike: Boolean = true
  ): commonResponse @function(name: "customQueryFunction-${env}")
  updateFCMToken(
    userId: ID!
    deviceId: String
    FCMToken: String!
    loginPlatform: String
  ): commonResponse @function(name: "customQueryFunction-${env}")
  sendNotification(input: notificationInput): commonResponse
    @function(name: "notificationFunction-${env}")
  createCourse(input: CreateEventsInput): commonResponse
    @function(name: "customQueryFunction-${env}")
  importOrganizations(input: importOrgInput): commonResponse
    @function(name: "importDataFunction-${env}")
  importStudents(input: importStudentsInput): commonResponse
    @function(name: "importDataFunction-${env}")
  importSchool(input: ImportSchoolInput): commonResponse
    @function(name: "importDataFunction-${env}")
  importStakeholders(input: importStakeholdersInput): commonResponse
    @function(name: "importDataFunction-${env}")
  importEvents(input: importEventsInput): commonResponse
    @function(name: "importDataFunction-${env}")

  createTaskRelationships(
    input: [createTaskRelationshipsInput]
  ): commonResponse @function(name: "customQueryFunction-${env}")
  deleteOldFeedback(input: deleteOldFeedbackInput): commonResponse
    @function(name: "customQueryFunction-${env}")
  createStudent(input: studentsInput): createStudentResponse
    @function(name: "customQueryFunction-${env}")
  deleteAssociations(input: deleteAssociationsInput): commonResponse
    @function(name: "customQueryFunction-${env}")
  deleteFavoriteEventsData(input: deleteFavoriteEventsInput): commonResponse
    @function(name: "customQueryFunction-${env}")
  deleteNotification(input: deleteNotificationInput): commonResponse
    @function(name: "customQueryFunction-${env}")
  updateProject(input: CreateEventsInput): commonResponse
    @function(name: "customQueryFunction-${env}")
  deleteHomeworkRelations(input: DeleteHomeworkRelationInput): commonResponse
    @function(name: "customQueryFunction-${env}")
  deleteAssociationRelations(
    input: DeleteAssociationRelationsInput
  ): commonResponse @function(name: "customQueryFunction-${env}")
  deletePerquisitesData(input: DeletePerquisitesInput): commonResponse
    @function(name: "customQueryFunction-${env}")
  updateHomeworkStatus(
    input: UpdateHomeworkStatusInput
  ): commonResponseWithData @function(name: "customQueryFunction-${env}")
  sendTokensToMembers(input: SendTokensToMembersInput): commonResponseWithData
    @function(name: "customQueryFunction-${env}")
  deleteUserRelations(input: DeleteUserRelationsInput): commonResponse
    @function(name: "customQueryFunction-${env}")
  importTransactions(input: ImportTransactionsInput): commonResponse
    @function(name: "importDataFunction-${env}")
  clearChatByUser(input: ClearChatByUserInput): commonResponse
    @function(name: "customQueryFunction-${env}")
  importAssignments(input: ImportAssignmentsInput): commonResponse
    @function(name: "importDataFunction-${env}")
  importIdeas(input: importIdeasInput): commonResponse
    @function(name: "importDataFunction-${env}")
  updateUserDetail(input: UpdateUserDetailInput): commonResponse
    @function(name: "graphqlCustomFunction-${env}")
  deleteUserDetail(input: DeleteUserDetailInput): commonResponse
    @function(name: "graphqlCustomFunction-${env}")
  selfAssignAssignment(input: SelfAssignAssignmentInput): commonResponse
    @function(name: "graphqlCustomFunction-${env}")

  createChannelUser(userId: String): commonResponse
    @function(name: "customQueryFunction-${env}")

  welcomeMail(input: SendMailInput): commonResponse
    @function(name: "sendEmail-${env}")

  deleteUserMessages(userId: String): commonResponse
    @function(name: "customQueryFunction-${env}")

  # MVT Wallet Management Operations
  # Admin Operations
  adminMintMVT(input: AdminMintMVTInput!): MVTWalletTransactionResponse @function(name: "mvtWallet-${env}")
  adminTransferMVT(input: AdminTransferMVTInput!): MVTWalletTransactionResponse @function(name: "mvtWallet-${env}")

  # User Operations
  userTransferMVT(input: UserTransferMVTInput!): MVTWalletTransactionResponse @function(name: "mvtWallet-${env}")

  # USDC Liquidity Pool Management (Internal MVT Wallet)
  adminDepositUSDC(input: USDCDepositInput!): MVTWalletTransactionResponse @function(name: "mvtWallet-${env}")
  adminWithdrawUSDC(input: USDCWithdrawalInput!): MVTWalletTransactionResponse @function(name: "mvtWallet-${env}")
  adminTransferUSDC(input: AdminTransferUSDCInput!): MVTWalletTransactionResponse @function(name: "mvtWallet-${env}")

  # MVT Swap Operations (Internal MVT Wallet)
  requestMVTWalletSwap(input: SwapRequestInput!): MVTWalletSwapRequestResponse @function(name: "mvtWallet-${env}")
  approveMVTWalletSwap(input: SwapApprovalInput!): SwapProcessingResponse @function(name: "mvtWallet-${env}")
  rejectMVTWalletSwap(input: SwapRejectionInput!): SwapProcessingResponse @function(name: "mvtWallet-${env}")

  # USDC and MVT Swap Operations
  mintCentralMVT(input: MintMVTTokenInput!): MVTTokenWalletResponse
    @function(name: "mvtTokenInternal-${env}")

  # Stripe Onramp Operations (MVT Wallet System)
  createOnrampSession(usdcAmount: String!, userWallet: String!, mvtAmount: String, exchangeRate: String): OnrampSessionResponse
    @function(name: "mvtWallet-${env}")

  # Frontend-Triggered Transfer Operations
  processOnrampTransfer(userId: String!, mvtAmount: Float!, usdcAmount: Float!, exchangeRate: Float!, sessionId: String!, description: String): OnrampTransferResponse
    @function(name: "mvtWallet-${env}")
  verifyOnrampSession(sessionId: String!): OnrampSessionResponse
    @function(name: "mvtWallet-${env}")
}


