/**
 * Request utility functions for Lambda functions
 * Provides reusable utilities for request handling and ID generation
 */

/**
 * Generate a unique request ID for tracing and logging
 * @returns {string} A unique request ID in format: req_{timestamp}_{randomString}
 */
const generateRequestId = () => {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * Generate a request ID with a custom prefix
 * @param {string} prefix - Custom prefix for the request ID
 * @returns {string} A unique request ID with custom prefix
 */
const generateRequestIdWithPrefix = (prefix) => {
  if (!prefix || typeof prefix !== 'string') {
    throw new Error('Prefix must be a non-empty string');
  }
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * Extract request context from Lambda event
 * @param {Object} event - Lambda event object
 * @param {Object} context - Lambda context object
 * @returns {Object} Request context with common fields
 */
const extractRequestContext = (event, context) => {
  return {
    requestId: context?.awsRequestId || generateRequestId(),
    functionName: context?.functionName || 'unknown',
    fieldName: event?.fieldName || 'unknown',
    userId: event?.identity?.sub || 'anonymous',
    timestamp: new Date().toISOString(),
    hasArguments: !!event?.arguments,
    argumentKeys: event?.arguments ? Object.keys(event.arguments) : []
  };
};

module.exports = {
  generateRequestId,
  generateRequestIdWithPrefix,
  extractRequestContext
};
