const { AWS, ddb } = require('../../config/aws');
const { getTableName } = require('../../shared/database/dynamoUtils');
const contractService = require('../../shared/blockchain/contractService');
const { TRANSACTION_TYPES, TRANSACTION_STATUS, TOKEN_TYPES } = require('../../shared/constants');
const validationUtils = require('../../shared/utils/validationUtils');
const transactionService = require('../transaction/transaction.service');
const { createDatabaseLogger, createLogger, logError, logSuccess } = require('../../shared/utils/logger');

/**
 * Get USDC liquidity pool balance with hybrid architecture fallback
 * @returns {Promise<object>} - Liquidity pool data
 */
async function getUSDCLiquidityPool() {
  const logger = createLogger({}, { operation: 'getUSDCLiquidityPool' });

  try {
    // Check if blockchain connection is available
    if (!contractService.isBlockchainConnected()) {
      const error = new Error("Blockchain wallet is not connected");
      error.code = 'BLOCKCHAIN_NOT_CONNECTED';
      error.statusCode = 400;
      
      logger.error({
        operation: 'getUSDCLiquidityPool',
        error: error.message,
        code: error.code
      }, "Blockchain wallet is not connected");
      
      throw error;
    }

    // Get USDC balance directly from the contract (with built-in fallback)
    const contractUSDCBalance = await contractService.getContractUSDCBalance();
    const usdcBalance = parseFloat(contractUSDCBalance);
    const adminWalletAddress = contractService.getWalletAddress();
    const poolAddress = process.env.MVT_WITHDRAW_CONTRACT_ADDRESS || "Not configured";

    // If we get 0 balance, it might be a contract issue, but we can still operate
    const status = usdcBalance > 0 ? "AVAILABLE" : "LOW_LIQUIDITY";
    const note = usdcBalance > 0 ? "Connected to blockchain contract" : "Contract balance is 0 or unavailable";

    // Log detailed contract balance information
    logger.info({
      operation: 'getUSDCLiquidityPool',
      poolAddress: poolAddress,
      balance: usdcBalance,
      adminWalletAddress: adminWalletAddress,
      status: status
    }, `USDC Liquidity Pool Contract Balance Retrieved - Pool Address: ${poolAddress}, Balance: ${usdcBalance} USDC, Admin Wallet: ${adminWalletAddress}, Status: ${status}`);

    const liquidityData = {
      totalReserves: usdcBalance, // Contract balance represents total reserves
      availableBalance: usdcBalance, // Available balance is the same as total reserves
      adminWalletAddress: adminWalletAddress,
      lastUpdated: new Date().toISOString(),
      status: status,
      note: note,
      contractAddress: poolAddress
    };

    // Log the complete liquidity data structure for debugging
    logger.debug({
      operation: 'getUSDCLiquidityPool',
      liquidityData: liquidityData
    }, 'Complete USDC Liquidity Pool Data retrieved');

    return liquidityData;
  } catch (error) {
    logger.error({
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      operation: 'getUSDCLiquidityPool'
    }, "Error getting USDC liquidity pool from contract");

    logger.warn({
      operation: 'getUSDCLiquidityPool',
      fallbackMode: true
    }, "Using fallback USDC liquidity data for hybrid architecture");

    // Return fallback data that allows the system to continue operating
    return {
      totalReserves: 500, // Conservative fallback amount
      availableBalance: 500,
      adminWalletAddress: contractService.getWalletAddress() || "Error getting address",
      lastUpdated: new Date().toISOString(),
      status: "ERROR_FALLBACK",
      note: `Contract error: ${error.message}. Using fallback values.`
    };
  }
}

/**
 * Deposit USDC to liquidity pool via blockchain contract
 * @param {number} amount - Amount to deposit
 * @param {string} adminUserId - Admin user ID
 * @param {string} description - Transaction description
 * @returns {Promise<object>} - Transaction data
 */
async function depositUSDCToPool(amount, adminUserId, description) {
  const logger = createDatabaseLogger({}, 'depositUSDCToPool', 'USDCLiquidityPool', { amount, adminUserId });

  try {
    logger.info({ amount, adminUserId }, `Starting USDC deposit to liquidity pool`);

    // Validate USDC amount (allows floats)
    const usdcValidation = validationUtils.validateUSDCAmount(amount);
    if (!usdcValidation.isValid) {
      throw new Error(usdcValidation.error);
    }

    if (!contractService.isBlockchainConnected()) {
      throw new Error("Blockchain connection not available. Check environment variables.");
    }

    // Get current liquidity pool data (from contract)
    const currentPool = await getUSDCLiquidityPool();

    // Deposit USDC to the contract
    const depositResult = await contractService.depositUSDCToContract(amount);

    // Create transaction record and persist to database
    const transactionId = transactionService.generateTransactionId('usdc-deposit');
    const now = new Date().toISOString();

    const transactionData = {
      id: transactionId,
      transactionType: TRANSACTION_TYPES.USDC_DEPOSIT,
      tokenType: TOKEN_TYPES.USDC, // Explicitly set token type for USDC transactions
      amount: amount,
      fromWalletId: `admin-wallet-${adminUserId}`,
      toWalletId: "usdc-liquidity-pool",
      fromUserId: adminUserId,
      toUserId: null,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: depositResult.transactionHash,
      internalTxId: transactionId,
      description: description || `Admin deposited ${amount} USDC to liquidity pool`,
      adminUserId: adminUserId,
      gasUsed: parseFloat(depositResult.gasUsed || 0),
      blockNumber: depositResult.blockNumber,
      metadata: {
        operation: "usdc_deposit",
        previousReserves: currentPool.totalReserves,
        contractAddress: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
        adminWallet: contractService.getWalletAddress(),
        blockchainTx: depositResult.transactionHash,
        approvalNeeded: depositResult.approvalNeeded || false,
        approvalTxHash: depositResult.approvalTxHash || null,
        gasUsed: depositResult.gasUsed,
        blockNumber: depositResult.blockNumber
      },
      createdAt: now,
      updatedAt: now
    };

    await transactionService.createMVTWalletTransaction(transactionData);

    logSuccess(logger, 'depositUSDCToPool', transactionData, {
      transactionId,
      amount,
      adminUserId,
      blockchainTx: depositResult.transactionHash
    });

    // Add display-ready fields required by GraphQL schema
    const displayType = 'SENT'; // Admin is sending USDC to pool
    const displayDetails = {
      displayType: displayType,
      primaryLabel: 'To: USDC Liquidity Pool',
      secondaryInfo: 'USDC Transaction',
      showEtherscanLink: Boolean(depositResult.transactionHash && depositResult.transactionHash.startsWith('0x')),
      formattedDate: new Date(now).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'UTC'
      })
    };

    // Return transaction data with all required display fields
    const finalTransactionData = {
      ...transactionData,
      ...displayDetails,
      // Add backward compatibility aliases
      type: transactionData.transactionType,
      from: transactionData.fromUserId || transactionData.fromWalletId,
      to: transactionData.toUserId || transactionData.toWalletId,
      timestamp: transactionData.createdAt
    };

    return finalTransactionData;
  } catch (error) {
    logError(logger, error, 'depositUSDCToPool', { amount, adminUserId });
    throw new Error(error.message || "Failed to deposit USDC to liquidity pool");
  }
}

/**
 * Withdraw USDC from liquidity pool via blockchain contract
 * @param {number} amount - Amount to withdraw
 * @param {string} adminUserId - Admin user ID
 * @param {string} description - Transaction description
 * @returns {Promise<object>} - Transaction data
 */
async function withdrawUSDCFromPool(amount, adminUserId, description) {
  const logger = createDatabaseLogger({}, 'withdrawUSDCFromPool', 'USDCLiquidityPool', { amount, adminUserId });

  try {
    logger.info({ amount, adminUserId }, `Starting USDC withdrawal from liquidity pool`);

    // Validate USDC amount (allows floats)
    const usdcValidation = validationUtils.validateUSDCAmount(amount);
    if (!usdcValidation.isValid) {
      throw new Error(usdcValidation.error);
    }

    if (!contractService.isBlockchainConnected()) {
      throw new Error("Blockchain connection not available. Check environment variables.");
    }

    // Get current liquidity pool data (from contract)
    const currentPool = await getUSDCLiquidityPool();

    // Get admin wallet address for withdrawal
    const adminWalletAddress = contractService.getWalletAddress();
    if (!adminWalletAddress) {
      throw new Error("Admin wallet address not available");
    }

    // Get contract address for logging
    const poolAddress = process.env.MVT_WITHDRAW_CONTRACT_ADDRESS || "Not configured";

    // Log detailed balance information before attempting withdrawal
    logger.info({
      operation: 'withdrawUSDCFromPool',
      balanceCheck: {
        requestedAmount: amount,
        availableBalance: currentPool.availableBalance,
        totalReserves: currentPool.totalReserves,
        poolAddress: poolAddress,
        adminWalletAddress: adminWalletAddress,
        poolStatus: currentPool.status || 'UNKNOWN',
        lastUpdated: currentPool.lastUpdated
      }
    }, `USDC Pool Balance Check - Requested: ${amount} USDC, Available: ${currentPool.availableBalance} USDC, Pool Address: ${poolAddress}`);

    if (currentPool.availableBalance < amount) {
      logger.error({
        operation: 'withdrawUSDCFromPool',
        insufficientBalance: {
          requestedAmount: amount,
          availableBalance: currentPool.availableBalance,
          shortfall: amount - currentPool.availableBalance,
          poolAddress: poolAddress
        }
      }, `Insufficient USDC in liquidity pool. Available: ${currentPool.availableBalance}, Requested: ${amount}, Shortfall: ${amount - currentPool.availableBalance}`);

      throw new Error(`Insufficient USDC in liquidity pool. Available: ${currentPool.availableBalance}, Requested: ${amount}`);
    }

    // Log before attempting blockchain withdrawal
    logger.info({
      operation: 'withdrawUSDCFromPool',
      blockchainWithdrawal: {
        amount: amount,
        poolAddress: poolAddress,
        adminWalletAddress: adminWalletAddress
      }
    }, `Proceeding with blockchain withdrawal of ${amount} USDC from pool ${poolAddress}`);

    // Withdraw USDC from the contract using the withdraw method
    const withdrawResult = await contractService.withdrawUSDCFromContract(amount);

    // Create transaction record and persist to database
    const transactionId = transactionService.generateTransactionId('usdc-withdrawal');
    const now = new Date().toISOString();

    const transactionData = {
      id: transactionId,
      transactionType: TRANSACTION_TYPES.USDC_WITHDRAWAL,
      tokenType: TOKEN_TYPES.USDC, // Explicitly set token type for USDC transactions
      amount: amount,
      fromWalletId: "usdc-liquidity-pool",
      toWalletId: `admin-wallet-${adminUserId}`,
      fromUserId: null,
      toUserId: adminUserId,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: withdrawResult.transactionHash,
      internalTxId: transactionId,
      description: description || `Admin withdrew ${amount} USDC from liquidity pool`,
      adminUserId: adminUserId,
      gasUsed: parseFloat(withdrawResult.gasUsed || 0),
      blockNumber: withdrawResult.blockNumber,
      metadata: {
        operation: "usdc_withdrawal",
        previousReserves: currentPool.totalReserves,
        contractAddress: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
        adminWallet: adminWalletAddress,
        blockchainTx: withdrawResult.transactionHash
      },
      createdAt: now,
      updatedAt: now
    };

    await transactionService.createMVTWalletTransaction(transactionData);

    logSuccess(logger, 'withdrawUSDCFromPool', transactionData, {
      transactionId,
      amount,
      adminUserId,
      blockchainTx: withdrawResult.transactionHash
    });

    // Add display-ready fields required by GraphQL schema
    const displayType = 'RECEIVED'; // Admin is receiving USDC from pool
    const displayDetails = {
      displayType: displayType,
      primaryLabel: 'From: USDC Liquidity Pool',
      secondaryInfo: 'USDC Transaction',
      showEtherscanLink: Boolean(withdrawResult.transactionHash && withdrawResult.transactionHash.startsWith('0x')),
      formattedDate: new Date(now).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'UTC'
      })
    };

    // Return transaction data with all required display fields
    const finalTransactionData = {
      ...transactionData,
      ...displayDetails,
      // Add backward compatibility aliases
      type: transactionData.transactionType,
      from: transactionData.fromUserId || transactionData.fromWalletId,
      to: transactionData.toUserId || transactionData.toWalletId,
      timestamp: transactionData.createdAt
    };

    return finalTransactionData;
  } catch (error) {
    logError(logger, error, 'withdrawUSDCFromPool', { amount, adminUserId });
    throw new Error(error.message || "Failed to withdraw USDC from liquidity pool");
  }
}

/**
 * Transfer USDC from admin to user
 * @param {string} userId - Target user ID
 * @param {number} amount - Amount to transfer
 * @param {string} adminUserId - Admin user ID performing the transfer
 * @param {string} description - Transaction description
 * @returns {Promise<object>} - Transaction data
 */
async function transferUSDCToUser(userId, amount, adminUserId, description) {
  const logger = createDatabaseLogger({}, 'transferUSDCToUser', 'USDCTransfer', { userId, amount, adminUserId });

  try {
    // Validate USDC amount
    const usdcValidation = validationUtils.validateUSDCAmount(amount);
    if (!usdcValidation.isValid) {
      throw new Error(usdcValidation.error);
    }

    // Get user data to validate and get wallet address
    const userTableName = getTableName("User");
    const userParams = {
      TableName: userTableName,
      Key: {
        id: { S: userId }
      }
    };

    const userResult = await ddb.getItem(userParams).promise();
    if (!userResult.Item) {
      throw new Error("User not found");
    }

    const user = AWS.DynamoDB.Converter.unmarshall(userResult.Item);
    if (!user.walletAddress) {
      throw new Error("User does not have a wallet address configured");
    }

    // Get current liquidity pool data to check available balance
    const currentPool = await getUSDCLiquidityPool();

    // Get contract address for logging
    const poolAddress = process.env.MVT_WITHDRAW_CONTRACT_ADDRESS || "Not configured";

    // Log detailed balance information before attempting transfer
    logger.info({
      operation: 'transferUSDCToUser',
      balanceCheck: {
        requestedAmount: amount,
        availableBalance: currentPool.availableBalance,
        totalReserves: currentPool.totalReserves,
        poolAddress: poolAddress,
        targetUserId: userId,
        userWalletAddress: user.walletAddress,
        poolStatus: currentPool.status || 'UNKNOWN',
        lastUpdated: currentPool.lastUpdated
      }
    }, `USDC Pool Balance Check for Transfer - Requested: ${amount} USDC, Available: ${currentPool.availableBalance} USDC, Pool Address: ${poolAddress}, Target User: ${userId}`);

    if (currentPool.availableBalance < amount) {
      logger.error({
        operation: 'transferUSDCToUser',
        insufficientBalance: {
          requestedAmount: amount,
          availableBalance: currentPool.availableBalance,
          shortfall: amount - currentPool.availableBalance,
          poolAddress: poolAddress,
          targetUserId: userId
        }
      }, `Insufficient USDC in liquidity pool for transfer. Available: ${currentPool.availableBalance}, Requested: ${amount}, Shortfall: ${amount - currentPool.availableBalance}`);

      throw new Error(
        `Insufficient USDC in liquidity pool. Available: ${currentPool.availableBalance}, Requested: ${amount}`
      );
    }

    // Transfer USDC to user's wallet address using blockchain
    const transferResult = await contractService.transferUSDCToUser(
      user.walletAddress,
      amount
    );

    // Create transaction record and persist to database
    const transactionId = transactionService.generateTransactionId('usdc-transfer');
    const now = new Date().toISOString();

    const transactionData = {
      id: transactionId,
      transactionType: TRANSACTION_TYPES.USDC_WITHDRAWAL, // Using withdrawal type as USDC leaves the pool
      tokenType: TOKEN_TYPES.USDC,
      amount: amount,
      fromWalletId: "usdc-liquidity-pool",
      toWalletId: `user-wallet-${userId}`,
      fromUserId: adminUserId,
      toUserId: userId,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: transferResult.transactionHash,
      internalTxId: transactionId,
      description: description || `Admin transferred ${amount} USDC to user`,
      adminUserId: adminUserId,
      gasUsed: parseFloat(transferResult.gasUsed || 0),
      blockNumber: transferResult.blockNumber,
      metadata: {
        operation: "usdc_transfer_to_user",
        previousReserves: currentPool.totalReserves,
        contractAddress: process.env.MVT_WITHDRAW_CONTRACT_ADDRESS,
        userWalletAddress: user.walletAddress,
        blockchainTx: transferResult.transactionHash
      },
      createdAt: now,
      updatedAt: now
    };

    // Persist the transaction record to the database
    await transactionService.createMVTWalletTransaction(transactionData);

    logSuccess(logger, 'transferUSDCToUser', transactionData, {
      transactionId,
      amount,
      userId,
      adminUserId,
      userWalletAddress: user.walletAddress,
      blockchainTx: transferResult.transactionHash
    });

    // Add display-ready fields required by GraphQL schema
    const displayType = 'SENT'; // Admin is sending USDC to user
    const displayDetails = {
      displayType: displayType,
      primaryLabel: `To: ${user.givenName || ''} ${user.familyName || ''}`.trim() || 'To: User',
      secondaryInfo: 'USDC Transaction',
      showEtherscanLink: Boolean(transferResult.transactionHash && transferResult.transactionHash.startsWith('0x')),
      formattedDate: new Date(now).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'UTC'
      })
    };

    // Return transaction data with all required display fields
    return {
      ...transactionData,
      ...displayDetails,
      // Add backward compatibility aliases
      type: transactionData.transactionType,
      from: transactionData.fromUserId || transactionData.fromWalletId,
      to: transactionData.toUserId || transactionData.toWalletId,
      timestamp: transactionData.createdAt
    };
  } catch (error) {
    logError(logger, error, 'transferUSDCToUser', { userId, amount, adminUserId });
    throw new Error(error.message || "Failed to transfer USDC to user");
  }
}

module.exports = {
  getUSDCLiquidityPool,
  depositUSDCToPool,
  withdrawUSDCFromPool,
  transferUSDCToUser
};
