const { getChatHistory, formatChatHistory } = require('../shared/utils/chatHistory');
const { createLogger } = require('../shared/utils/logger');
const AWS = require('aws-sdk');

// Create a logger for this service
const logger = createLogger('chatHistoryService');

// Initialize DynamoDB DocumentClient
const docClient = new AWS.DynamoDB.DocumentClient();

/**
 * Fetches and formats chat history for a user
 * @param {string} userId - The ID of the user
 * @param {Object} options - Additional options
 * @param {number} [options.limit=5] - Maximum number of history items to return
 * @param {boolean} [options.includeTimestamps=true] - Whether to include timestamps in the formatted history
 * @param {string} [options.chatType] - Filter by specific chat type
 * @returns {Promise<string>} Formatted chat history or empty string if none found
 */
async function getFormattedChatHistory(userId, options = {}) {
  const { limit = 5, includeTimestamps = true, chatType } = options;

  const operation = 'getFormattedChatHistory';
  const startTime = Date.now();

  if (!userId) {
    logger.error('User ID is required', { operation });
    return '';
  }

  try {
    logger.info('🔍 [CHAT_HISTORY_DEBUG] getFormattedChatHistory called', {
      operation,
      userId,
      options: { limit, includeTimestamps, chatType },
    });

    // Get raw chat history
    const history = await getChatHistory(docClient, userId, chatType);

    logger.info('📋 [CHAT_HISTORY_DEBUG] Raw chat history retrieved', {
      operation,
      userId,
      hasHistory: !!history,
      historyLength: history?.length || 0,
      historyItems: history
        ? history.map((item) => ({
            id: item.id,
            type: item.type,
            chatType: item.chatType,
            role: item.role,
            message: item.message?.substring(0, 100) + (item.message?.length > 100 ? '...' : ''),
            createdAt: item.createdAt,
            timestamp: item.timestamp,
          }))
        : [],
    });

    if (!history || history.length === 0) {
      logger.info('❌ [CHAT_HISTORY_DEBUG] No chat history found', { operation, userId });
      return '';
    }

    // Format the history
    let formattedHistory;
    if (includeTimestamps) {
      formattedHistory = history
        .map((item) => {
          const timestamp = item.timestamp
            ? new Date(item.timestamp).toISOString()
            : 'Unknown time';
          return `[${timestamp}] ${item.role}: ${item.message}`;
        })
        .join('\n');
    } else {
      formattedHistory = history
        .map(
          (item, index) =>
            `[Message ${index + 1}] ${item.role === 'user' ? 'User' : 'Assistant'}: ${item.message}`
        )
        .join('\n');
    }

    // Limit the number of messages if needed
    if (limit && history.length > limit) {
      const messages = formattedHistory.split('\n');
      formattedHistory = messages.slice(-limit).join('\n');
      logger.debug(`Limited chat history to last ${limit} messages`, {
        operation,
        userId,
        originalCount: messages.length,
        limitedCount: limit,
      });
    }

    logger.debug('Successfully formatted chat history', {
      operation,
      userId,
      itemCount: history.length,
      formattedLength: formattedHistory.length,
      executionTimeMs: Date.now() - startTime,
    });

    return formattedHistory;
  } catch (error) {
    logger.error('Error getting formatted chat history', {
      operation,
      userId,
      error: error.message,
      stack: error.stack,
    });
    return ''; // Return empty string on error
  }
}

/**
 * Gets chat history prompt to be included in the chatbot context
 * @param {string} userId - The ID of the user
 * @param {Object} options - Additional options
 * @param {number} [options.limit=3] - Number of previous messages to include
 * @param {string} [options.chatType] - Filter by specific chat type
 * @returns {Promise<string>} Formatted chat history prompt
 */
async function getChatContext(userId, options = {}) {
  const { limit = 3, chatType } = options;
  const operation = 'getChatContext';

  try {
    logger.info('🔍 [CHAT_HISTORY_DEBUG] getChatContext called', {
      operation,
      userId,
      limit,
      chatType,
      options,
    });

    const history = await getFormattedChatHistory(userId, {
      limit,
      includeTimestamps: false,
      chatType,
    });

    logger.info('📋 [CHAT_HISTORY_DEBUG] getFormattedChatHistory result', {
      operation,
      userId,
      hasHistory: !!history,
      historyLength: history?.length || 0,
      historyContent: history || 'EMPTY',
    });

    if (!history) {
      logger.info('❌ [CHAT_HISTORY_DEBUG] No chat history available for context', {
        operation,
        userId,
      });
      return '';
    }

    const context = `### CONVERSATION HISTORY (Chronological Order):
${history}

### CRITICAL CONTEXT INSTRUCTION:
If the current user question contains ambiguous references like "which one?", "tell me more", "what about that organization?", you MUST examine the conversation history above to identify what specific item they are referring to from previous messages. Provide detailed information about that specific item rather than asking for clarification.

`;

    logger.info('✅ [CHAT_HISTORY_DEBUG] Generated chat context', {
      operation,
      userId,
      contextLength: context.length,
      messageCount: history.split('\n').filter(Boolean).length,
      fullContext: context,
    });

    return context;
  } catch (error) {
    logger.error('❌ [CHAT_HISTORY_DEBUG] Error generating chat context', {
      operation,
      userId,
      error: error.message,
      stack: error.stack,
    });
    return ''; // Return empty string on error
  }
}

module.exports = {
  getFormattedChatHistory,
  getChatContext,
};
