/**
 * Shared Logging Utility for AWS Lambda Functions
 * 
 * This module provides a structured logging solution using Pino,
 * optimized for AWS Lambda environments with CloudWatch integration.
 * 
 * Features:
 * - Structured JSON logging
 * - AWS Lambda context integration (requestId, functionName)
 * - Environment-based log levels
 * - Performance optimized for serverless
 * - CloudWatch compatible formatting
 */

const pino = require('pino');

/**
 * Default log level configuration based on environment
 */
const getLogLevel = () => {
  // Use AWS Lambda log level if set, otherwise default based on environment
  if (process.env.AWS_LAMBDA_LOG_LEVEL) {
    return process.env.AWS_LAMBDA_LOG_LEVEL.toLowerCase();
  }
  
  const env = process.env.ENV || process.env.NODE_ENV || 'development';
  
  switch (env.toLowerCase()) {
    case 'prod':
    case 'production':
      return 'warn';
    case 'dev':
    case 'development':
      return 'debug';
    case 'amplifydev':
    case 'staging':
      return 'info';
    default:
      return 'info';
  }
};

/**
 * Pino configuration optimized for AWS Lambda
 */
const pinoConfig = {
  level: getLogLevel(),
  
  // Custom formatters for AWS Lambda compatibility
  formatters: {
    // Add Lambda-specific context to all logs
    bindings: (bindings) => {
      return {
        nodeVersion: process.version,
        functionName: process.env.AWS_LAMBDA_FUNCTION_NAME || 'unknown',
        functionVersion: process.env.AWS_LAMBDA_FUNCTION_VERSION || '$LATEST',
        environment: process.env.ENV || 'unknown'
      };
    },
    
    // Ensure log levels are uppercase for CloudWatch compatibility
    level: (label) => {
      return { level: label.toUpperCase() };
    }
  },
  
  // Custom timestamp format for AWS Lambda
  timestamp: () => `,"timestamp":"${new Date().toISOString()}"`,
  
  // Optimize for Lambda - disable pretty printing in production
  prettyPrint: false,
  
  // Ensure logs are flushed immediately (important for Lambda)
  sync: true
};

/**
 * Base logger instance
 */
const baseLogger = pino(pinoConfig);

/**
 * Create a child logger with Lambda context
 * 
 * @param {Object} context - AWS Lambda context object
 * @param {Object} additionalFields - Additional fields to include in all logs
 * @returns {Object} Configured Pino logger instance
 */
function createLogger(context = {}, additionalFields = {}) {
  const contextFields = {
    requestId: context.awsRequestId || 'unknown',
    remainingTimeMs: context.getRemainingTimeInMillis ? context.getRemainingTimeInMillis() : undefined,
    ...additionalFields
  };
  
  // Remove undefined values
  Object.keys(contextFields).forEach(key => {
    if (contextFields[key] === undefined) {
      delete contextFields[key];
    }
  });
  
  return baseLogger.child(contextFields);
}

/**
 * Log performance metrics
 * 
 * @param {Object} logger - Pino logger instance
 * @param {string} operation - Operation name
 * @param {number} startTime - Start time in milliseconds
 * @param {Object} additionalMetrics - Additional metrics to log
 */
function logPerformance(logger, operation, startTime, additionalMetrics = {}) {
  const duration = Date.now() - startTime;
  
  logger.info({
    operation,
    duration,
    performanceMetrics: {
      durationMs: duration,
      ...additionalMetrics
    }
  }, `Performance: ${operation} completed in ${duration}ms`);
}

/**
 * Log error with context
 * 
 * @param {Object} logger - Pino logger instance
 * @param {Error} error - Error object
 * @param {string} operation - Operation that failed
 * @param {Object} context - Additional context
 */
function logError(logger, error, operation = 'unknown', context = {}) {
  logger.error({
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code
    },
    operation,
    errorContext: context
  }, `Error in ${operation}: ${error.message}`);
}

/**
 * Log successful operation with result summary
 * 
 * @param {Object} logger - Pino logger instance
 * @param {string} operation - Operation name
 * @param {Object} result - Operation result
 * @param {Object} summary - Summary of the result (avoid logging sensitive data)
 */
function logSuccess(logger, operation, result = {}, summary = {}) {
  logger.info({
    operation,
    success: true,
    resultSummary: summary
  }, `Successfully completed ${operation}`);
}

/**
 * Create a logger for GraphQL operations
 * 
 * @param {Object} context - AWS Lambda context object
 * @param {string} operation - GraphQL operation name
 * @param {string} fieldName - GraphQL field name
 * @param {Object} additionalFields - Additional fields
 * @returns {Object} Configured Pino logger instance for GraphQL operations
 */
function createGraphQLLogger(context = {}, operation = '', fieldName = '', additionalFields = {}) {
  const graphqlFields = {
    operation,
    fieldName,
    component: 'graphql',
    ...additionalFields
  };
  
  return createLogger(context, graphqlFields);
}

/**
 * Create a logger for external service operations
 * 
 * @param {Object} context - AWS Lambda context object
 * @param {string} service - External service name (e.g., 'bedrock', 'openai', 'lex')
 * @param {string} operation - Service operation name
 * @param {Object} additionalFields - Additional fields
 * @returns {Object} Configured Pino logger instance for external service operations
 */
function createServiceLogger(context = {}, service = '', operation = '', additionalFields = {}) {
  const serviceFields = {
    service,
    operation,
    component: 'external_service',
    ...additionalFields
  };
  
  return createLogger(context, serviceFields);
}

/**
 * Creates a wrapped logger with consistent message formatting and context
 * 
 * @param {Object} logger - Base logger instance (from createLogger or similar)
 * @param {Object} context - Context to be added to all log messages
 * @returns {Object} Wrapped logger with consistent methods
 * 
 * @example
 * const { createLogger, wrapLogger } = require('./logger');
 * const baseLogger = createLogger();
 * const logger = wrapLogger(baseLogger, { module: 'chatbot' });
 * 
 * // Usage:
 * logger.info('User logged in', { userId: '123' });
 * logger.error('Failed to process request', new Error('Invalid input'), { requestId: 'abc' });
 */
function wrapLogger(logger, context = {}) {
  const childLogger = logger.child(context);
  
  return {
    /**
     * Log an info message
     * @param {string} message - The message to log
     * @param {Object} [data={}] - Additional data to include in the log
     */
    info: (message, data = {}) => {
      childLogger.info({ message, ...data });
    },
    
    /**
     * Log a warning message
     * @param {string} message - The message to log
     * @param {Object} [data={}] - Additional data to include in the log
     */
    warn: (message, data = {}) => {
      childLogger.warn({ message, ...data });
    },
    
    /**
     * Log an error message
     * @param {string} message - The message to log
     * @param {Error|Object} [error=null] - Error object or additional data
     * @param {Object} [data={}] - Additional data to include in the log
     */
    error: (message, error = null, data = {}) => {
      if (error instanceof Error) {
        childLogger.error({ 
          message, 
          error: error.message, 
          stack: error.stack,
          ...data 
        });
      } else {
        childLogger.error({ 
          message, 
          ...(error && typeof error === 'object' ? error : { data: error }),
          ...data 
        });
      }
    },
    
    /**
     * Log a debug message
     * @param {string} message - The message to log
     * @param {Object} [data={}] - Additional data to include in the log
     */
    debug: (message, data = {}) => {
      childLogger.debug({ message, ...data });
    },
    
    /**
     * Create a new wrapped logger with additional context
     * @param {Object} additionalContext - Additional context to add to the logger
     * @returns {Object} A new wrapped logger with combined context
     */
    child: (additionalContext = {}) => {
      return wrapLogger(childLogger, additionalContext);
    }
  };
}

module.exports = {
  createLogger,
  createGraphQLLogger,
  createServiceLogger,
  logPerformance,
  logError,
  logSuccess,
  wrapLogger,
  baseLogger
};
