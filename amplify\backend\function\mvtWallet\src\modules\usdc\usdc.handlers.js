const usdcService = require('./usdc.service');
const validationUtils = require('../../shared/utils/validationUtils');
const { AuthorizationTypes, executeHandler } = require('../../shared/utils/handlerUtils');
const standardizedErrorUtils = require('../../shared/utils/standardizedErrorUtils');

/**
 * Handle getUSDCLiquidityPool request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleGetUSDCLiquidityPool(event, args) {
  return executeHandler(event, args, {
    authorization: { type: AuthorizationTypes.ADMIN_ONLY },
    operationName: 'getUSDCLiquidityPool',
    successMessage: 'USDC liquidity pool data retrieved successfully'
  }, async () => {
    const liquidityData = await usdcService.getUSDCLiquidityPool();
    return { data: liquidityData };
  });
}

/**
 * <PERSON>le adminDepositUSDC request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleAdminDepositUSDC(event, args) {
  return executeHandler(event, args, {
    authorization: { type: AuthorizationTypes.ADMIN_ONLY, requireUserId: true },
    validation: validationUtils.validateUSDCDepositInput,
    operationName: 'adminDepositUSDC',
    successMessage: `Successfully deposited ${args.input.amount} USDC to liquidity pool`
  }, async (authContext) => {
    const { amount, description } = args.input;

    const transactionData = await usdcService.depositUSDCToPool(
      amount,
      authContext.adminUserId,
      description
    );

    return { data: transactionData };
  });
}

/**
 * Handle adminWithdrawUSDC request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleAdminWithdrawUSDC(event, args) {
  return executeHandler(event, args, {
    authorization: { type: AuthorizationTypes.ADMIN_ONLY, requireUserId: true },
    validation: validationUtils.validateUSDCWithdrawalInput,
    operationName: 'adminWithdrawUSDC',
    successMessage: `Successfully withdrew ${args.input.amount} USDC from liquidity pool`
  }, async (authContext) => {
    const { amount, description } = args.input;

    const transactionData = await usdcService.withdrawUSDCFromPool(
      amount,
      authContext.adminUserId,
      description
    );

    return { data: transactionData };
  });
}

/**
 * Handle adminTransferUSDC request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleAdminTransferUSDC(event, args) {
  return executeHandler(event, args, {
    authorization: { type: AuthorizationTypes.ADMIN_ONLY, requireUserId: true },
    validation: validationUtils.validateUSDCTransferInput,
    operationName: 'adminTransferUSDC',
    successMessage: `Successfully transferred ${args.input.amount} USDC to user`
  }, async (authContext) => {
    const { userId, amount, description } = args.input;

    const transactionData = await usdcService.transferUSDCToUser(
      userId,
      amount,
      authContext.adminUserId,
      description
    );

    return { data: transactionData };
  });
}

module.exports = {
  handleGetUSDCLiquidityPool,
  handleAdminDepositUSDC,
  handleAdminWithdrawUSDC,
  handleAdminTransferUSDC
};
