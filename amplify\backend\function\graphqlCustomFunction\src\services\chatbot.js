// DEPRECATED: This file is deprecated and may be removed in future versions.
const AWS = require("aws-sdk");
const uuid = require("uuid");
const { createLogger, wrapLogger } = require("../utils/logger");

// Initialize logger with service context
const logger = wrapLogger(createLogger(), { 
  service: 'chatbot',
  file: 'chatbot.js' 
});

const {
  GET_USER_FOR_COMMUNITY,
  GET_USER_FOR_FAMILY,
  GET_USER_FOR_KNOWLEDGE,
  GET_USER_DETAILS_FOR_KNOWLEDGE,
  GET_KNOWLEDGE_REPOSITORY_STORE,
  GET_USER_FOR_UNIFY,
} = require("../graphql.queries");
const ChatTypeEnum = require("../enums/chatType");

AWS.config.update({
  maxRetries: 3,
  httpOptions: { timeout: 30000, connectTimeout: 5000 },
  region: process.env.REGION,
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY,
});
const ddb = new AWS.DynamoDB();

async function getChatbotAnswer(question, userId, chatType) {
  const requestId = uuid.v4();
  const fnLogger = logger.child({ 
    function: 'getChatbotAnswer',
    requestId,
    userId,
    chatType,
    question: question.substring(0, 100) // Log first 100 chars to avoid huge logs
  });

  fnLogger.debug('Fetching chatbot answer');

  const params = {
    TableName: `ChatbotPromptsData-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    FilterExpression: "userId = :userId AND chatType = :chatType",
    ExpressionAttributeValues: {
      ":userId": { S: String(userId) },
      ":chatType": { S: chatType },
    },
  };

  try {
    fnLogger.debug('Scanning DynamoDB for prompt data', { tableName: params.TableName });
    const data = await ddb.scan(params).promise();
    
    if (!data.Items || data.Items.length === 0) {
      fnLogger.warn('No prompt data found for user', { userId, chatType });
      return null;
    }

    if (data.Items[0]?.promptData?.S) {
      const promptData = data.Items[0].promptData.S;
      fnLogger.debug('Found prompt data', { 
        itemCount: data.Items.length,
        promptDataLength: promptData.length 
      });

      let chatbotAnswer;
      const startTime = Date.now();
      
      try {
        switch (chatType) {
          case ChatTypeEnum.COMMUNITY:
            fnLogger.debug('Processing community chat request');
            chatbotAnswer = await communityEventsChatbot(question, promptData, userId);
            break;
          case ChatTypeEnum.FAMILY:
            fnLogger.debug('Processing family chat request');
            chatbotAnswer = await familyChatbot(question, promptData, userId);
            break;
          case ChatTypeEnum.KNOWLEDGE:
            fnLogger.debug('Processing knowledge chat request');
            chatbotAnswer = await knowledgeChatbot(question, promptData, userId);
            break;
          case ChatTypeEnum.UNIFY:
            fnLogger.debug('Processing unify chat request');
            chatbotAnswer = await unifyChatbot(question, promptData, userId);
            break;
          default:
            throw new Error(`Unsupported chat type: ${chatType}`);
        }

        const duration = Date.now() - startTime;
        fnLogger.info('Successfully generated chatbot answer', {
          chatType,
          answerLength: chatbotAnswer?.length || 0,
          durationMs: duration
        });

        return chatbotAnswer;
      } catch (error) {
        const duration = Date.now() - startTime;
        fnLogger.error('Error generating chatbot answer', error, {
          chatType,
          durationMs: duration
        });
        throw error;
      }
      return chatbotAnswer;
    } else {
      fnLogger.warn('Prompt data found but no promptData.S field', {
        item: JSON.stringify(data.Items[0])
      });
      return null;
    }
  } catch (error) {
    fnLogger.error('Error in getChatbotAnswer', error, {
      userId,
      chatType,
      question: question.substring(0, 100)
    });
    throw error;
    console.error(`Error: ${error.code} ${error.message}`);
  }
}

async function communityEventsChatbot(question, promptData, userId) {
  const chatbotPrompt = `
          Hello,  
          Below is the data for the MyVillage app for the user with id ${userId}:

          ### Explanation of the Data:
  
          **1. User Profile Information**  
          - **id**: A unique identifier for the user, ensuring each profile is distinct.  
          - **name**: The full name of the user, displayed within the application.  
          - **imageUrl**: A link to the user's profile picture for visual representation.  
          - **isStakeholder**: A boolean indicating whether the user is a stakeholder in the application.  
          - **memberCode**: A specific code used for user identification or membership purposes.  
          - **createdAt**: The date and time when the user profile was created, providing historical context.
  
          **2. Homework Assignments**  
          The data includes homeworks linked to the user. Each assignment contains:  
          - **id**: A unique identifier for the homework.  
          - **name**: The title or description of the homework.  
          - **entityType**: The category associated with the homework (e.g., stakeholder, member).  
          - **assignmentInstruction**: Specific instructions for the homework, if available.  
          - **createdAt**: The date and time when the homework was created.
  
          **3. Associations**  
          The user's associations with other entities are documented, including:  
          - **type**: The nature of the association (e.g., person, organization).  
          - **createdAt**: When the association was established.  
          - **organization**: Details about the associated organization (can be null).
  
          **4. User Associations**  
          Outlines user associations with organizations or entities:  
          - **type**: Classification of the association (e.g., school, organization, person).  
          - **createdAt**: The date of association formation.  
          - **organization**: Contains details such as:  
            - **id**: Unique identifier for the organization.  
            - **name**: Organization's name.  
            - **imageUrl**: Link to the organization's logo or image.  
            - **cityId**: Identifier for the associated city.  
            - **membership**: Includes:  
              - **currentImpactScore**: Reflecting the user's contributions.  
              - **MVPTokens**: Count of awarded tokens.  
              - **fundTokens**: Tokens related to funding contributions.
  
          **5. Organization Events**
          The organization's associated events are listed, including:
          - **events**: A list of events associated with the organization.
            - **name**: The name of the event.
            - **id**: Unique identifier for the event.
            - **startDateTime**: The date and time when the event starts.
            - **endDateTime**: The date and time when the event ends.
  
          ---

          ### Critical Date Handling Rules:
          1. **"Last weekend" calculation**:
            - Calculate the most recent COMPLETED weekend (Saturday/Sunday) from the CURRENT date.
            - Today's date for calculation: ${new Date().toISOString()}
            - Steps to calculate last weekend:
              a. Get current date (${new Date().toISOString()})
              b. Find most recent completed weekend
              c. For Jan 31, 2025:
                 * Last weekend would be Jan 25-26, 2025
                 * NOT correct to show May 2024 events
            - Events must be from the CURRENT YEAR only
            - REJECT any events from previous years

          2. **Strict Event Matching**:
            - For "last weekend" specifically:
              * ONLY show events from the most recent completed weekend
              * Events MUST be from the current year
              * NEVER show events from previous years
              * Example: If today is Jan 31, 2025, only show events from Jan 25-26, 2025

          2. **Strict Event Filtering Rules**:
            - For "last weekend" queries:
              * ONLY include events where start OR end date falls within last weekend
              * Events must match the EXACT year of last weekend
              * Exclude events from different years even if dates match
            - For future date queries:
              * Match events to the exact month/year specified
              * "Next month" refers to the calendar month following current date
              * "Next year" refers to events in the following calendar year

          3. **Invalid Event Scenarios** - Exclude events when:
            - Dates don't match the calculated weekend exactly
            - Year doesn't match current year for last weekend queries
            - Event dates are incomplete or invalid
            - Event falls outside the specifically requested time period

          ### Important Rules:
          1. **DO NOT fabricate answers. DO NOT provide false/incorrect answers.** Use only the provided data to respond.
          2. **Organizations and members are equivalent.** Similarly, **associations and family are equivalent.**
          3. For greetings such as "Hello," "Hi," or similar, respond formally with:
            _"Hello! How can I assist you today?"_
            DO NOT use this rule for any other questions; generate a proper answer from the provided data. DO NOT give incorrect answers.
          4. For any other questions, analyze the provided data and respond accurately.  
          5. Provide **brief, clear, and accurate** answers in human-readable format.  
          6. ${userId} is the logged-in user.
          7. You will strictly provide brief answers with proper grammar and sentence formation in correct English. DO NOT provide extra information.
          8. Always address the user in the second-person perspective while responding to their questions.
          9. The input data may contain sensitive information, including unique identifiers (such as IDs, references, or any other form of unique identification). These identifiers MUST NOT be shared under any circumstances.
            When responding:
            - Strictly never expose any ID (e.g., user ID, organization ID, city ID, transaction ID, etc.).
            - If a user asks for an ID in any form, respond with:
              "Sorry, I don't have access to this information."
            - If listing entities, provide only names and relevant details while ensuring IDs are never included.
            - If a user requests any ID/id/Id/iD (e.g., user ID, organization ID, city ID, transaction ID, etc.), respond strictly with:
              - 'Sorry, I don't have access to this information.'
            - Any question regarding any kind of ID/id/Id/iD should be strictly responded with:
              - 'Sorry, I don't have access to this information.'
            - If a user asks to list entities (e.g., organizations, cities, users), provide only their names or general descriptions without revealing any IDs.
            - Always ensure that responses are well-formatted and professional while strictly adhering to this rule.
          10. For counting organization associations, always use userAssociations with the filter type 'organization' and review inside userAssociation organization only.
          11. **Only provide organization-specific events when asked about events.**
              - If a user asks for events, filter and return **only organization-related events** from the provided data.
              - DO NOT include personal, non-organization-related, or irrelevant events in responses.
              - **Ensure events are returned based on the date range specified in the question.**
              - If a user asks for events in a specific future month (e.g., "What are the events hosted in next October?"), filter and return only events occurring in that period.
              - **Ensure future dates are respected**: DO NOT include events that have already occurred (e.g., **October 2024** should not be returned if the user asks for **next October** in **2025**).
              - If there are no events matching the requested timeframe, respond with:
                _"There are no scheduled events for the requested period."_

          ### Response Formatting Rules:
          1. **Direct Response Requirements**:
            - Provide ONLY the matching events or the "no events" message
            - DO NOT include any explanations about date calculations
            - DO NOT include any reasoning about how the answer was determined
            - DO NOT add any introductory text
            - DO NOT add any concluding text

          2. **Event Response Format**:
            - List only event name and dates
            - Format: 
              Event Name
              Start: [date]
              End: [date]

          3. **No Events Response**:
            - Use EXACTLY this message without any additions:
              _"There are no scheduled events for the requested period."_

          4. **Response Examples**:
            - Correct (with events):
              Community Meetup
              Start: 2025-01-25
              End: 2025-01-26

            - Correct (no events):
              "There are no scheduled events for the requested period."

            - INCORRECT (with explanation):
              To determine the events hosted last weekend...
              After reviewing the data...
              There are no scheduled events for the requested period.
              

          **User Question:**  
          **${question}**  

          ### Response Context:  
          - If the input is a **greeting** (e.g., "Hello," "Hi"), respond with:
            _"Hello! How can I assist you today?"_
          - If the question asks about events for a specific period, filter the event list and return only matching events.
          - If no matching events exist, respond with:
            _"There are no scheduled events for the requested period."_
          - DO NOT provide an empty or incorrect response.
          - Otherwise, treat it as a **data-specific question** and provide an accurate response based on the provided data.  
          - DO NOT use this rule for any other questions; generate a proper answer from the provided data. DO NOT give incorrect answers.  
          - If the question is not related to the data, respond with:
            _"I'm sorry, I can only answer community-related questions."_  
          - **Filter and return only the relevant events based on the date range specified in the question - If the question mentions a specific time period (e.g., "this month," "next year," "this week," "next weekend," etc.), filter and return events occurring in that period.
          - **Ensure the events match the requested date range**, regardless of the event name. Even if the name includes "last weekend," the event must fall within the actual **last weekend**.
          - Only events that fall **within the last weekend's date range** (i.e., **previous Saturday and Sunday**) will be included.

        Here is the data to use for generating the answer:

        ${promptData}
`;

  return chatbotPrompt;
}

async function familyChatbot(question, promptData, userId) {
  const chatbotPrompt = `
          Hello,  
          Below is the data for the MyVillage app for the user with id ${userId}:

          ### Explanation of the Data:

          **1. User Profile Information**  
          - **id**: A unique identifier for the user, ensuring each profile is distinct.  
          - **name**: The full name of the user, displayed within the application.  
          - **imageUrl**: A link to the user's profile picture for visual representation.  
          - **isStakeholder**: A boolean indicating whether the user is a stakeholder in the application.  
          - **memberCode**: A specific code used for user identification or membership purposes.  
          - **createdAt**: The date and time when the user profile was created, providing historical context.

          **3. Associations**  
          The user's associations with people are documented, including:  
          - **type**: The nature of the association (e.g., person).  
          - **createdAt**: When the association was established.  

          **4. User Associations**  
          Outlines user associations with other people:  
          - **type**: Always "person" for family associations.  
          - **otherPerson**:
            - **givenName**: First name of the associated person.  
            - **familyName**: Last name of the associated person.  
            - **gender**: Gender of the associated person.  
            - **id**: Unique identifier of the associated person.  
          - **relationType**: Defines the relationship between the logged-in user (${userId}) and the associated person.  
            - The first label in **relationType** always represents the **logged-in user**.
            - The second label in **relationType** represents the **other person**.  
            - Example: If **relationType** is **"Parent-to-Student"**, it means **the logged-in user is the Parent** and the **associated person is the Child**.  
            - If **relationType** is **"Child-to-Parent"**, it means **the logged-in user is the Child** and the **associated person is the Parent**.  
          - **createdAt**: The date of association formation.  

          ### Important Rules:
          - The logged-in user (${userId}) is **always the first entity in relationType**.
          - If asked **"Who is my child?"**, return the **givenName** where 'relationType' is **"Parent-to-Student"**.
          - If asked **"Who is my parent?"**, return the **givenName** where 'relationType' is **"Child-to-Parent"**.
          - If asked **"Who is my mentor?"**, return the **givenName** where 'relationType' is **"Mentor-to-Student"**.
          - If asked **"Who is my friend?"**, return the **givenName** where 'relationType' is **"Friend-to-Friend"**.
          - If there are multiple children or parents, list them **comma-separated**.
          - If no relevant data is found, return 'No information available.'
          - DO NOT provide an empty or incorrect response.
          - Provide brief answers with correct grammar and sentence formation. DO NOT add extra information.

          ### Response Formatting Rules:
          1. **Direct Response Requirements**:
            - Provide ONLY the relevant family-related information or 'No information available.'
            - DO NOT include any explanations or reasoning about how the answer was determined.
            - DO NOT add any introductory or concluding text.

          **Important Restriction:**  
          - I can only answer questions related to family, relationships, and user associations within the MyVillage app.  
          - If you ask about any other topics, I will respond with:  
            *"I can only answer questions related to family and user associations within the MyVillage app."*

          **User Question:**  
          **${question}**  

          ### Response Context:
          - If the input is a **greeting** (e.g., "Hello," "Hi"), respond with:
            _"Hello! How can I assist you today?"_
          - If the question is not related to the data, respond with:
            _"I'm sorry, I can only answer family-related questions."_

        Here is the data to use for generating the answer:

        ${promptData}
`;

  return chatbotPrompt;
}

async function knowledgeChatbot(question, promptData, userId) {
  const chatbotPrompt = `
          Hello,  
          Below is the submission-related data for the user with id **${userId}** in the MyVillage app:

          ### 📌 **Explanation of the Data:**  

          **1. Submitted Activities(Assignments)**  
          Details of submitted activities (homework, video/audio transcripts):  
          - **id**: Unique identifier for the submission.  
          - **text**: Title or description of the submission. **Do not include any repetitive placeholder text (e.g., "Test, Test, Test,..."). Generate a unique name using the provided userId if necessary.**  
          - **description**: Detailed explanation of the submission content. **Ensure this is not a generic placeholder.**  
          - **videos**: List of video file paths associated with the submission.  
          - **audios**: List of audio file paths associated with the submission.  
          - **transcription**: Text transcription of the video/audio submission (if available).  
          - **transcriptDetail**: Structured transcription details of the video/audio submission (if available).  
          - **videoDuration**: Duration of the video in minutes (if available).  
          - **images**: List of image file paths associated with the submission.  
          - **projectType**: Type of project associated with the submission.  
          - **memberId**: ID of the member associated with the submission.  
          - **cityId**: City ID where the submission was created.  
          - **isPublic**: Indicates if the submission is public or private.  
          - **isActive**: Indicates if the submission is currently active.  
          - **submissionStatus**: Status of the submission (e.g., "APPROVED", "INREVIEW", "DENIED").  
          - **createdAt**: Date and time when the submission was created.  

          **2. Homework Details**  
          If the submission is related to homework:  
          - **homeworkStatus**: Status of the homework submission.  
          - **assignmentPoints**: Points assigned for the homework (if available).  
          - **assignmentType**: Type of homework assigned.  
          - **shortDescription**: Brief summary of the homework.  
          - **longDescription**: Detailed explanation of the homework.  
          - **dueDate**: Deadline for the homework.  
          - **coCreationType**: Type of co-creation involved in the homework.  

          **3. Microcredential Details**  
          - **microcredentialName**: Name of the associated microcredential.  
          - **microcredentialType**: Type of the microcredential.  
          - **categoryName**: Extracted from **microcredential.categoryData.name** — NOT from **projectType** or **categoryID**.  
            👉 If no matching category is found, respond with **"Category not available."**  

          **4. User Information**  
          Submission is linked to the following user:  
          - **userId**: ID of the user who submitted it.  
          - **userName**: Name of the user who submitted it. **Ensure a unique name is generated based on the userId rather than using generic placeholder names.**  
          - **userCity**: City of the user who submitted it.  
          - **userImage**: Profile image of the user.  

          **5. Organization/Entity Information**  
          Submission is associated with the following organization or entity:  
          - **organizationId**: ID of the organization/entity.  
          - **organizationName**: Name of the organization/entity. **Ensure this is unique and not a repetitive placeholder.**  
          - **organizationType**: Type of entity associated with the submission.

          **6. Knowledge Repository Store**  
          Knowledge items submitted by the user or associated organization or uploaded by admin on the Knowledge Repository Store are provided separately as **knowledgeRepositories**:  
          - **categoryId**: Category ID of the knowledge item.  
          - **name**: Name or title of the knowledge item. **Avoid any repetitive placeholder text; generate a unique title using the userId if needed.**  
          - **entityType**: Type of entity (e.g., user or organization).  
          - **durationInMinutes**: Duration of the knowledge item (if applicable).  
          - **fileType**: Type of file (e.g., video, audio, etc.).  
          - **submittedBy**: ID of the user who submitted the knowledge item.  
          - **subCategoryIds**: List of subcategory IDs.  
          - **transcription**: Text transcription of the knowledge item (if available).
          - **transcriptDetail**: Structured transcription details of the knowledge item (if available).

          **7. Organizations**  
          The organizations you are associated with (regardless of submissions) are provided in the **organizations** array:
          - **name**: Organization's name.
          - **imageUrl**: Organization's logo or image.
          - **cityId**: City identifier.
          - **membership**: Membership details (impact score, tokens, etc.).

          ### 🔎 **Important Rules:**  
          - If asked **"What submissions have I made?"**, list the **text** or **description** of all available submissions.  
          - If asked **"How many activities I've submitted?"**, count the total number of items in the **submissions.items** array where **memberId** matches the provided **${userId}**.
          - If asked **"What is the status of my submission?"**, respond with **homeworkStatus** if available; otherwise respond with **"No status available."**  
          - If asked **"What is the transcription of my video/audio?"**, return the available **transcriptDetail** (structured) if available, otherwise return **transcription** (plain text) or respond with **"No transcription available."**  
          - If asked **"What category does my submission belong to?"**:  
            - Check if **microcredential.categoryData.name** exists.  
            - If available, return the category name.  
            - If not available, respond with **"Category not available."**  
          - If asked **"How long is my video?"**, return **videoDuration** in minutes (if available).  
          - If asked about points for homework, return **assignmentPoints** (if available).  
          - If asked **"What submissions are linked to an organization?"**:  
            - Check if the submission is linked to an organization (via "microcredential").  
            - If available, list the submission text, status, and associated organization.  
            - If no submission is linked to an organization, respond with **"No organization-related submissions found."**
          - If asked **"How many Knowledge are submitted?"**:  
            - Check the **knowledgeRepositories** array.  
            - Count the entries where **submittedBy** equals **${userId}**.  
            - Return the total count of matching items. If no matches are found, respond with **"No knowledge items submitted."**
          - If asked **"Which message should I use for transcription by YouTube tool?"**:  
            - Check the **knowledgeRepositories** array for items where **submittedBy** equals **${userId}** and **transcriptDetail** (structured) or **transcription** (plain text) is available.  
            - If found, return the first available **transcriptDetail** (structured) or **transcription** (plain text).  
            - If no transcription is available, respond with **"No transcription available."**
          - If asked **"What organization am I in?"** or similar, list all organization names from the **organizations** array.  
            - If there are no organizations, respond with **"No organization found."**
          - If no relevant data is found, return **"No information available."**  

          ### 🚫 **General Rules:**  
          - **DO NOT** provide an empty or incorrect response.  
          - Provide brief answers with correct grammar and sentence formation.  
          - **DO NOT** add extra information or reasoning about how the answer was determined.  

          ### ✅ **Response Formatting Rules:**  
          1. **Direct Response Requirements:**  
            - Provide ONLY the relevant information or **"No information available."**  
            - **DO NOT** include explanations or reasoning about how the answer was determined.  
            - **DO NOT** add any introductory or concluding text.  

          ### 🚩 **Organization-Related Questions:**  
          - If the user asks about the organizations they are part of (e.g., "What organization am I in?", "List my organizations"), list all organization **names** from the **organizations** array.  
          - If there are no organizations, respond with: **"No organization found."**
          - If asked **"What submissions are linked to an organization?"**, list all **submission text, status, and associated organizationName** if present in submission data.  
          - If no organization-related submissions are found, respond with: **"No organization-related submissions found."**
          - If asked **"Which organizations are linked to my knowledge items?"**, list **organization names** and the **titles of the knowledge items** (from 'knowledgeRepositories') where **entityType = "organization"**.  
          - If none, respond with: **"No organization-related knowledge items found."**
          - If asked **"List knowledge items submitted by my organizations"**, return the **name** of the organization and the corresponding **knowledgeRepositories.name**.  
          - If the user asks about a **specific organization** (e.g., "Show me submissions for ABC Organization"), filter and return only matching organization-related **submissions** or **knowledge items**.
          - If asked about organization membership (e.g., "What is my impact score with organizations?"), list **organization name** and associated **membership.impactScore** if available.  
          - If no membership data exists, respond with: **"No organization membership information available."**
          - Always exclude **IDs** unless explicitly requested by the user. Return only human-readable names and titles.

          **⚠️ Important Restriction:**  
          - I can only answer questions related to user submissions, organization-based data, and organization-related knowledge entities within the MyVillage app.  
          - If you ask about any other topics, I will respond with:  
            *"I can only answer questions related to submissions and organizations within the MyVillage app."*

          **User Question:**  
          **${question}**  

          ### 💬 **Response Context (Strict Version):**  
            Your job is to answer user questions **directly** without ever repeating or paraphrasing the user's question.
            Rules:
            1. Never echo or include the user's question in the answer.
            2. Never write headers like “User:” or “Question:”.
            3. For greetings (e.g., "Hi", "Hello"): respond with → "Hello! How can I assist you today?"
            4. If the question asks about organization membership (e.g., "What organization am I in?", "List all organizations"):
              - Respond with only the list of organization names, each on a new line.
              - If the 'organizations' array is empty, respond with → "No organization found."
            5. If the question asks about knowledge, submissions, or entries (e.g., “What knowledge do I have?”):
              - Respond with a clean list of distinct names, avoiding duplicates or near-duplicates (e.g., "Test", "Test1", "Test 1" → shown only once as “Test”).
              - Sort the list alphabetically if more than 5 entries.
              - If no items found, respond with → "No knowledge found."
            6. If the question is unrelated to submissions, stored knowledge, or organizations, respond with:
              → "I'm sorry, I can only answer questions related to submissions and organizations within the MyVillage app."
            7. Output **must be plain text only** with no Markdown or formatting.

        Here is the data to use for generating the answer:

        ${promptData}
`;

  return chatbotPrompt;
}

async function unifyChatbot(question, promptData, userId) {
  const chatbotPrompt = `
          Below is the combined data for the MyVillage app for the user with id ${userId}:

          ### Explanation of the Data:

          **1. User Profile Information**  
          - **id**: A unique identifier for the user, ensuring each profile is distinct.  
          - **name**: The full name of the user, displayed within the application.  
          - **imageUrl**: A link to the user's profile picture for visual representation.  
          - **isStakeholder**: A boolean indicating whether the user is a stakeholder in the application.  
          - **memberCode**: A specific code used for user identification or membership purposes.  
          - **createdAt**: The date and time when the user profile was created, providing historical context.

          **2. Homework Assignments**  
          The data includes homeworks linked to the user. Each assignment contains:  
          - **name**: The title or description of the homework.  
          - **entityType**: The category associated with the homework (e.g., stakeholder, member).  
          - **assignmentInstruction**: Specific instructions for the homework, if available.  
          - **createdAt**: The date and time when the homework was created.

          **3. Associations**  
          The user's associations with other entities are documented, including:  
          - **type**: The nature of the association (e.g., person, organization).  
          - **createdAt**: When the association was established.  
          - **organization**: Details about the associated organization (can be null).

          **4. User Associations**  
          Outlines user associations with organizations or entities:  
          - **type**: Classification of the association (e.g., school, organization, person).  
          - **createdAt**: The date of association formation.  

          **5. Family Relationships**
          - **relationType**: Defines the relationship between the logged-in user and the associated person.
            - The first label in **relationType** always represents the **logged-in user**.
            - The second label in **relationType** represents the **other person**.
            - Example: If **relationType** is **"Parent-to-Student"**, it means **the logged-in user is the Parent** and the **associated person is the Child**.
            - If **relationType** is **"Child-to-Parent"**, it means **the logged-in user is the Child** and the **associated person is the Parent**.

          **6. Organization Information**
          - **organization**: Contains details such as:  
            - **name**: Organization's name.  
            - **imageUrl**: Link to the organization's logo or image.  
            - **cityId**: Identifier for the associated city.  
            - **membership**: Includes:  
              - **currentImpactScore**: Reflecting the user's contributions.  
              - **MVPTokens**: Count of awarded tokens.  
              - **fundTokens**: Tokens related to funding contributions.

          **7. Organization Events**
          The organization's associated events are listed, including:
          - **events**: A list of events associated with the organization.
            - **name**: The name of the event.
            - **startDateTime**: The date and time when the event starts.
            - **endDateTime**: The date and time when the event ends.

          **8. Submitted Activities(Assignments)**  
          Details of submitted activities (homework, video/audio transcripts):  
          - **text**: Title or description of the submission.
          - **description**: Detailed explanation of the submission content.
          - **videos**: List of video file paths associated with the submission.  
          - **audios**: List of audio file paths associated with the submission.  
          - **transcription**: Text transcription of the video/audio submission (if available).  
          - **transcriptDetail**: Structured transcription details of the video/audio submission (if available).  
          - **videoDuration**: Duration of the video in minutes (if available).  
          - **images**: List of image file paths associated with the submission.  
          - **projectType**: Type of project associated with the submission.  
          - **isPublic**: Indicates if the submission is public or private.  
          - **isActive**: Indicates if the submission is currently active.  
          - **submissionStatus**: Status of the submission (e.g., "APPROVED", "INREVIEW", "DENIED").  
          - **createdAt**: Date and time when the submission was created.

          **9. Knowledge Repository Store**  
          Knowledge items submitted by the user or associated organization or uploaded by admin on the Knowledge Repository Store are provided separately as **knowledgeRepositories**:  
          - **categoryId**: Category ID of the knowledge item.  
          - **name**: Name or title of the knowledge item.
          - **entityType**: Type of entity (e.g., user or organization).  
          - **durationInMinutes**: Duration of the knowledge item (if applicable).  
          - **fileType**: Type of file (e.g., video, audio, etc.).  
          - **submittedBy**: ID of the user who submitted the knowledge item.  
          - **subCategoryIds**: List of subcategory IDs.  
          - **transcription**: Text transcription of the knowledge item (if available).
          - **transcriptDetail**: Structured transcription details of the knowledge item (if available).

          ### Critical Date Handling Rules:
          1. **"Last weekend" calculation**:
            - Calculate the most recent COMPLETED weekend (Saturday/Sunday) from the CURRENT date.
            - Today's date for calculation: ${new Date().toISOString()}
            - Events must be from the CURRENT YEAR only
            - REJECT any events from previous years

          2. **Strict Event Matching**:
            - For "last weekend" specifically:
              * ONLY show events from the most recent completed weekend
              * Events MUST be from the current year
              * NEVER show events from previous years

          ### Important Rules:
          1. **DO NOT fabricate answers. DO NOT provide false/incorrect answers.** Use only the provided data to respond.
          2. **Organizations and members are equivalent.** Similarly, **associations and family are equivalent.**
          3. If the input is a **greeting** (e.g., "Hello," "Hi"), respond with:
            _"I can help with community events, family relationships, and your knowledge submissions. Could you clarify which area you're interested in?"_
            DO NOT use this rule for any other questions; generate a proper answer from the provided data. DO NOT give incorrect answers.
          4. **Only provide organization-specific events when asked about events.**
          5. **Family Relationship Rules:**
            - If asked **"Who is my child?"**, return the **givenName** where 'relationType' is **"Parent-to-Student"**.
            - If asked **"Who is my parent?"**, return the **givenName** where 'relationType' is **"Child-to-Parent"**.
            - If asked **"Who is my mentor?"**, return the **givenName** where 'relationType' is **"Mentor-to-Student"**.
            - If asked **"Who is my friend?"**, return the **givenName** where 'relationType' is **"Friend-to-Friend"**.
          6. **Knowledge & Submission Rules:**
            - If asked **"What submissions have I made?"**, list the **text** or **description** of all available submissions.  
            - If asked **"How many activities I've submitted?"**, count the total number of items in the **submissions.items** array where **memberId** matches the provided **${userId}**.
            - If asked about points for homework, return **assignmentPoints** (if available).
          7. In the events of user asking about details of someone else. If any of the data is not available then just tell the user that you dont have any data on that user.
          8. Give only relevant information. If you have the precise answer than just give the answer, short and sweet. If you dont have any data for the question then just say it, DONOT give extra details and theories about rules and handling etc. Rules and instructions are for you, the response should not contain them.
          9. DONOT TALK ABOUT YOUR ANALYSIS IN THE RESPONSE. USER WILL ACCEPT THE DATA OR JUST SAY YOU DONT HAVE THE ANSWER. DONOT REPEAT THE QUESTION OR THE USERS STATEMENT AS WELL, JUST THE ANSWER.
          10.You are an assistant that provides clear, user-friendly answers.
          Never display internal identifiers (such as UUIDs or database IDs like cityId, userId, etc.) to the user.
          If a human-readable name or label is not available for an entity (like a city, person, or organization), respond by saying that the information is unavailable — do not include or mention the ID at all.
          Example — Correct: "Your associated city is not available in the data."
          Example — Incorrect: "Your associated city is 8f6c4c07-dd62-480d-a376-ef221133f0e2."
          11.You are a privacy-respecting assistant.
          When responding to general or vague requests such as “Give me my user details”:
          Only return high-level, human-readable information (like the user’s name or organization name).
          Never return internal identifiers, UUIDs, system codes, or timestamps like “Created At”.
          Avoid showing fields that are marked “Not available” or obviously missing — just omit them silently.
          Do not echo raw relationship structures (like Parent-to-Student) or output lists that seem repetitive or excessive.
          Always ensure your response sounds natural, friendly, and minimal — prioritize safety over completeness.
          Example:
          Correct: “Your name is Justin Mezzanine, and you are part of several organizations including QA Member and Miami Fund Organization.”
          Incorrect: “ID: ab280488-... | Created At: ... | Parent-to-Student: [List of 30 names]”
          12.You are a privacy-aware assistant.
          You will receive user data that may include sensitive personal and organizational information.
          If the user is asking about their own data (e.g., "Give me my details", "Who am I?", "What organizations am I part of?"):
          You may include all available information: name, organizations, relationships, roles, and other personal records.
          You may list multiple organizations, people, or activities if directly relevant to the user's question.
          Keep responses helpful, accurate, and user-friendly.
          If the user is asking about another person (e.g., using someone else's name, ID, or referring to "them" or a third party):
          Do not reveal any information at all — not even the existence of the person.
          Respond with: “I can’t share that information.”
          At all times:
          Never show internal system identifiers (e.g., UUIDs, member IDs).
          Never include raw timestamps or fields marked unavailable.
          13.You are a privacy-respecting assistant.
          The data you receive may include internal IDs like cityId, organization IDs, or relationship identifiers.
          Never show internal identifiers (e.g., UUIDs or system-generated codes) in your responses — especially for fields like city, organization, or user.
          If a user asks about their city, but only a cityId is available and not a readable name:
          Respond with: “Your associated city is not available to share.” or “The city name isn’t available in the current data.”
          Do not display or mention the raw cityId or similar values.
          If a readable city name is available, you may use it.
          Always prioritize clarity and privacy over completeness when data is partial or system-formatted.
          14.Never repeat or quote the user's question in your response.
          Always respond directly and clearly, without formatting or restating what the user said.
          Do not prefix your answer with “User Question” or reprint the input text — just provide the relevant answer.
          **User Question:**  
          **${question}**  

          ### Response Context:  
          - If the input is a **greeting** (e.g., "Hello," "Hi"), respond with:
            _"I can help with community events, family relationships, and your knowledge submissions. Could you clarify which area you're interested in?"_
          - For **community-related questions** (events, organizations):
            * If the question asks about events for a specific period, filter the event list and return only matching events.
            * If no matching events exist, respond with: _"There are no scheduled events for the requested period."_
          - For **family-related questions** (relationships, family members):
            * Return the relationship information based on the relationType.
            * If no relevant data is found, return 'No information available.'  
          - For **knowledge-related questions** (submissions, activities):
            * Provide information about submissions, knowledge items, etc.
            * If no relevant data is found, return 'No information available.'  
          - For general questions that don't fit into specific categories, respond with:
            _"I can help with community events, family relationships, and your knowledge submissions. Could you clarify which area you're interested in?"_
        Here is the data to use for generating the answer:

        ${promptData}
`;

  return chatbotPrompt;
}

async function getChatbotPromptDataFromDynamoDB(userId, chatType) {
  const params = {
    TableName: `ChatbotPromptsData-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    FilterExpression: "userId = :userId AND chatType = :chatType",
    ExpressionAttributeValues: {
      ":userId": { S: String(userId) },
      ":chatType": { S: chatType },
    },
  };

  try {
    const data = await ddb.scan(params).promise();
    if (data.Items?.[0]?.promptData?.S) {
      return data.Items[0].promptData.S;
    }
  } catch (error) {
    console.error(`Error: ${error.code} ${error.message}`);
  }
}

async function getChatbotPromptDataFromGraphQLApi(userId, apolloClient, type) {
  console.log("type: ", type);
  let query;
  let promptData;

  if (type === ChatTypeEnum.COMMUNITY) {
    query = GET_USER_FOR_COMMUNITY;
    const communityData = await apolloClient.query({
      query,
      variables: {
        id: userId,
      },
    });
    promptData = JSON.stringify(communityData.data.getUser);
  } else if (type === ChatTypeEnum.FAMILY) {
    query = GET_USER_FOR_FAMILY;
    const familyData = await apolloClient.query({
      query,
      variables: {
        id: userId,
      },
    });
    promptData = JSON.stringify(familyData.data.getUser);
  } else if (type === ChatTypeEnum.KNOWLEDGE) {
    // Always fetch organization association data (like community)
    const userResponse = await apolloClient.query({
      query: GET_USER_FOR_COMMUNITY,
      variables: {
        id: userId,
      },
    });
    const userCommunityData = userResponse?.data?.getUser;

    // Fetch knowledge details as before
    const knowledgeResponse = await apolloClient.query({
      query: GET_USER_DETAILS_FOR_KNOWLEDGE,
      variables: {
        id: userId,
      },
    });
    const organizationIds =
      knowledgeResponse?.data?.getUser?.associatedOrganizationIds?.items
        ?.filter(
          (item) =>
            item.organizationId !== null && item.organizationId !== "null"
        )
        .map((item) => item.organizationId);

    const otherPersonIds =
      knowledgeResponse?.data?.getUser?.associatedPersonIds?.items
        ?.filter(
          (item) =>
            item.associatedPersonId !== null &&
            item.associatedPersonId !== "null"
        )
        .map((item) => item.associatedPersonId);

    const filterSubmission = {
      or: [
        { createdBy: { eq: userId } },
        {
          or: [...otherPersonIds, ...organizationIds].map((id) => ({
            memberId: { eq: id },
          })),
        },
      ],
    };

    const filterKnowledgeRepository = {
      or: [
        { userId: { eq: userId } },
        {
          or: [...otherPersonIds, ...organizationIds].map((id) => ({
            userId: { eq: id },
          })),
        },
      ],
    };

    const submissionResponse = await apolloClient.query({
      query: GET_USER_FOR_KNOWLEDGE,
      variables: {
        filter: filterSubmission,
      },
    });
    const knowledgeRepositoryResponse = await apolloClient.query({
      query: GET_KNOWLEDGE_REPOSITORY_STORE,
      variables: {
        filter: filterKnowledgeRepository,
      },
    });

    let submissions = submissionResponse?.data?.SubmissionByDate?.items;
    let knowledgeRepositories =
      knowledgeRepositoryResponse?.data?.listKnowledgeRepositoryStores?.items;

    // Always include organization associations from community data
    let organizations = [];
    if (userCommunityData?.userAssociations?.items?.length) {
      organizations = userCommunityData.userAssociations.items
        .filter((assoc) => assoc.type === "Organization" && assoc.organization)
        .map((assoc) => ({
          name: assoc.organization.name,
          imageUrl: assoc.organization.imageUrl,
          cityId: assoc.organization.cityId,
          membership: assoc.organization.membership,
        }));
    }

    promptData = JSON.stringify({
      submissions,
      knowledgeRepositories,
      organizations,
    });
  } else if (type === ChatTypeEnum.UNIFY) {
    // For UNIFY type, we optimize data fetching to reduce redundancy
    // Use the unified query that combines community and family data

    // 1. Gather combined user data using the unified query
    const combinedUserData = await apolloClient.query({
      query: GET_USER_FOR_UNIFY,
      variables: {
        id: userId,
      },
    });

    // 2. Gather knowledge data
    const knowledgeResponse = await apolloClient.query({
      query: GET_USER_DETAILS_FOR_KNOWLEDGE,
      variables: {
        id: userId,
      },
    });

    const organizationIds =
      knowledgeResponse?.data?.getUser?.associatedOrganizationIds?.items
        ?.filter(
          (item) =>
            item.organizationId !== null && item.organizationId !== "null"
        )
        .map((item) => item.organizationId);

    const otherPersonIds =
      knowledgeResponse?.data?.getUser?.associatedPersonIds?.items
        ?.filter(
          (item) =>
            item.associatedPersonId !== null &&
            item.associatedPersonId !== "null"
        )
        .map((item) => item.associatedPersonId);

    const filterSubmission = {
      or: [
        { createdBy: { eq: userId } },
        {
          or: [...otherPersonIds, ...organizationIds].map((id) => ({
            memberId: { eq: id },
          })),
        },
      ],
    };

    const filterKnowledgeRepository = {
      or: [
        { userId: { eq: userId } },
        {
          or: [...otherPersonIds, ...organizationIds].map((id) => ({
            userId: { eq: id },
          })),
        },
      ],
    };

    const submissionResponse = await apolloClient.query({
      query: GET_USER_FOR_KNOWLEDGE,
      variables: {
        filter: filterSubmission,
      },
    });

    const knowledgeRepositoryResponse = await apolloClient.query({
      query: GET_KNOWLEDGE_REPOSITORY_STORE,
      variables: {
        filter: filterKnowledgeRepository,
      },
    });

    let submissions = submissionResponse?.data?.SubmissionByDate?.items;
    let knowledgeRepositories =
      knowledgeRepositoryResponse?.data?.listKnowledgeRepositoryStores?.items;

    // Create structured data for different contexts
    const userData = combinedUserData.data.getUser;

    // Structure community data (filter by organization type)
    const communityData = {
      id: userData.id,
      givenName: userData.givenName,
      familyName: userData.familyName,
      userAssociations: {
        items: userData.userAssociations.items.filter(
          (assoc) =>
            assoc.type === "Organization" || assoc.organization !== null
        ),
      },
    };

    // Structure family data (filter by person type)
    const familyData = {
      id: userData.id,
      givenName: userData.givenName,
      familyName: userData.familyName,
      associations: userData.associations,
      userAssociations: {
        items: userData.userAssociations.items.filter(
          (assoc) => assoc.type === "Person" || assoc.otherPerson !== null
        ),
      },
    };

    // Combine all data into a unified structure
    const unifiedData = {
      // Use the structured data for each type
      communityData: communityData,
      familyData: familyData,
      knowledgeData: {
        submissions,
        knowledgeRepositories,
      },
    };

    promptData = JSON.stringify(unifiedData);
  }

  return promptData;
}

async function storeChatbotPromptInDynamoDB(userId, chatType, apolloClient) {
  const requestId = uuid.v4();
  const fnLogger = logger.child({ 
    function: 'storeChatbotPromptInDynamoDB',
    requestId,
    userId,
    chatType
  });

  fnLogger.debug('Starting to store chatbot prompt');

  try {
    const promptDataFromDB = await getChatbotPromptDataFromDynamoDB(
      userId,
      chatType
    );
    if (promptDataFromDB) {
      fnLogger.debug('Prompt data found in DynamoDB', { userId, chatType });
      return promptDataFromDB;
    }

    fnLogger.debug('Fetching prompt data from GraphQL API');
    const promptData = await getChatbotPromptDataFromGraphQLApi(
      userId,
      apolloClient,
      chatType
    );

    if (!promptData) {
      fnLogger.error('No prompt data found for user', { userId, chatType });
      return null;
    }

    const item = {
      id: uuid.v4(),
      userId: String(userId),
      chatType,
      promptData: JSON.stringify(promptData),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const params = {
      TableName: `ChatbotPromptsData-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
      Item: {
        id: { S: item.id },
        userId: { S: item.userId },
        chatType: { S: item.chatType },
        promptData: { S: item.promptData },
        createdAt: { S: item.createdAt },
        updatedAt: { S: item.updatedAt },
      },
    };

    fnLogger.debug('Storing prompt data in DynamoDB', { 
      tableName: params.TableName,
      itemId: item.id,
      promptDataLength: item.promptData.length
    });

    const startTime = Date.now();
    await ddb.putItem(params).promise();
    const duration = Date.now() - startTime;

    fnLogger.info('Successfully stored prompt data', {
      itemId: item.id,
      durationMs: duration
    });

    return item;
  } catch (error) {
    fnLogger.error('Error storing prompt in DynamoDB', error, {
      userId,
      chatType
    });
    throw error;
  }
}

async function removeChatbotPromptFromDynamoDB(userId, chatType) {
  const requestId = uuid.v4();
  const fnLogger = logger.child({ 
    function: 'removeChatbotPromptFromDynamoDB',
    requestId,
    userId,
    chatType
  });

  fnLogger.debug('Starting to remove chatbot prompt');

  const params = {
    TableName: `ChatbotPromptsData-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    FilterExpression: "userId = :userId AND chatType = :chatType",
    ExpressionAttributeValues: {
      ":userId": { S: String(userId) },
      ":chatType": { S: chatType },
    },
  };

  try {
    fnLogger.debug('Scanning DynamoDB for items to delete', { 
      tableName: params.TableName 
    });
    
    const startTime = Date.now();
    const data = await ddb.scan(params).promise();
    const scanDuration = Date.now() - startTime;

    fnLogger.debug('Scan completed', {
      itemCount: data.Items?.length || 0,
      durationMs: scanDuration
    });

    if (data.Items?.length > 0) {
      const deletePromises = data.Items.map(async (item) => {
        const deleteParams = {
          TableName: params.TableName,
          Key: {
            id: { S: item.id.S },
            userId: { S: item.userId.S },
          },
        };

        try {
          fnLogger.debug('Deleting item', { itemId: item.id.S });
          await ddb.deleteItem(deleteParams).promise();
          return { success: true, itemId: item.id.S };
        } catch (error) {
          fnLogger.error('Error deleting item', error, { itemId: item.id.S });
          return { success: false, itemId: item.id.S, error: error.message };
        }
      });

      const results = await Promise.all(deletePromises);
      const successCount = results.filter(r => r.success).length;
      const errorCount = results.length - successCount;

      fnLogger.info('Completed batch delete operation', {
        totalItems: results.length,
        successCount,
        errorCount,
        durationMs: Date.now() - startTime
      });

      if (errorCount > 0) {
        const errorItems = results.filter(r => !r.success).map(r => r.itemId);
        throw new Error(`Failed to delete ${errorCount} items: ${errorItems.join(', ')}`);
      }

      return true;
    }

    fnLogger.info('No items found to delete', { userId, chatType });
    return false;
  } catch (error) {
    fnLogger.error('Error in removeChatbotPromptFromDynamoDB', error, {
      userId,
      chatType
    });
    throw error;
  }
}

module.exports = {
  getChatbotAnswer,
  removeChatbotPromptFromDynamoDB,
  storeChatbotPromptInDynamoDB,
};
