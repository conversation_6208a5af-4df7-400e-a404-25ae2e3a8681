/**
 * Swap Handlers Test Suite
 * Tests for swap GraphQL handlers including request creation, approval/rejection, and user data population
 */

const swapHandlers = require('../swap.handlers');
const swapService = require('../swap.service');
const authService = require('../../../shared/services/authService');
const swapValidation = require('../swap.validation');
const responseUtils = require('../../../shared/utils/responseUtils');
const handlerUtils = require('../../../shared/utils/handlerUtils');

// Mock dependencies
jest.mock('../swap.service');
jest.mock('../../../shared/services/authService');
jest.mock('../swap.validation');
jest.mock('../../../shared/utils/responseUtils');
jest.mock('../../../shared/utils/handlerUtils');

// Set up global test utilities if not already available
if (!global.testUtils) {
  global.testUtils = {
    // Mock GraphQL event
    createMockEvent: (cognitoIdentityId = 'test-cognito-id', isAdmin = false) => ({
      requestContext: {
        identity: {
          cognitoIdentityId
        }
      },
      arguments: {},
      info: {
        fieldName: 'testField'
      },
      source: {},
      stateValues: {},
      prev: null
    }),

    // Mock GraphQL arguments
    createMockArgs: (input = {}) => ({
      input
    }),

    // Mock swap request
    createMockSwapRequest: (id = 'test-swap-123', status = 'PENDING') => ({
      id,
      swapRequestId: id, // Add this field for handler compatibility
      userId: 'test-user-123',
      mvtAmount: 100,
      usdcAmount: 50.0,
      exchangeRate: 0.5,
      status,
      userWalletAddress: '******************************************',
      description: 'Test swap request',
      requestedAt: '2024-01-01T00:00:00.000Z',
      processedAt: status !== 'PENDING' ? '2024-01-01T01:00:00.000Z' : null,
      adminUserId: status !== 'PENDING' ? 'admin-user-123' : null,
      transactionHash: status === 'APPROVED' ? '0xabcdef123456' : null,
      rejectionReason: status === 'REJECTED' ? 'Test rejection reason' : null,
      isDeleted: 'false',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    })
  };
}

describe('Swap Handlers', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Set up default response utility mocks
    responseUtils.createSuccessResponse.mockImplementation((data, message) => ({
      statusCode: 200,
      message,
      data
    }));

    responseUtils.createUnauthorizedResponse.mockImplementation((message) => ({
      statusCode: 401,
      message: message || 'Authentication required'
    }));

    responseUtils.createBadRequestResponse.mockImplementation((message) => ({
      statusCode: 400,
      message: message || 'Bad request'
    }));

    responseUtils.handleServiceError.mockImplementation((error, defaultMessage) => {
      let statusCode = 400;
      const errorMessage = (error.message || '').toLowerCase();

      if (errorMessage.includes('timeout')) {
        statusCode = 500;
      } else if (errorMessage.includes('network')) {
        statusCode = 500;
      }

      return {
        statusCode,
        message: error.message || defaultMessage
      };
    });

    responseUtils.handleSwapError.mockImplementation((error, operation) => {
      let statusCode = 400;
      const errorMessage = error.message.toLowerCase();

      if (errorMessage.includes('not found')) {
        statusCode = 404;
      } else if (errorMessage.includes('blockchain transaction failed')) {
        statusCode = 500;
      } else if (errorMessage.includes('network')) {
        statusCode = 500;
      } else if (errorMessage.includes('timeout')) {
        statusCode = 500;
      } else if (errorMessage.includes('unlock')) {
        statusCode = 500;
      } else if (errorMessage.includes('wallet address')) {
        statusCode = 400;
      } else if (errorMessage.includes('already processed')) {
        statusCode = 400;
      }

      return {
        statusCode,
        message: error.message,
        data: null
      };
    });

    // Set up default handlerUtils mocks
    handlerUtils.handleSwapRequestListAuthorization.mockResolvedValue({
      context: { userId: 'test-user-123', isAdmin: false }
    });
  });

  describe('handleRequestMVTWalletSwap', () => {
    const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
    const mockArgs = global.testUtils.createMockArgs({
      mvtAmount: 100,
      description: 'Test swap request'
    });

    test('should successfully create swap request', async () => {
      // Arrange
      const mockSwapRequest = global.testUtils.createMockSwapRequest('swap-123', 'PENDING');
      
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
      swapValidation.validateSwapRequestInput.mockReturnValue({ isValid: true });
      swapService.createSwapRequest.mockResolvedValue(mockSwapRequest);
      responseUtils.createSuccessResponse.mockReturnValue({
        statusCode: 200,
        message: 'Swap request created successfully',
        data: mockSwapRequest
      });

      // Act
      const result = await swapHandlers.handleRequestMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(authService.getCurrentUserDatabaseId).toHaveBeenCalledWith(mockEvent);
      expect(swapValidation.validateSwapRequestInput).toHaveBeenCalledWith(mockArgs.input);
      expect(swapService.createSwapRequest).toHaveBeenCalledWith(
        'test-user-123',
        100,
        'Test swap request'
      );
      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(mockSwapRequest);
    });

    test('should reject unauthenticated users', async () => {
      // Arrange
      authService.getCurrentUserDatabaseId.mockResolvedValue(null);
      responseUtils.createUnauthorizedResponse.mockReturnValue({
        statusCode: 401,
        message: 'Authentication required'
      });

      // Act
      const result = await swapHandlers.handleRequestMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(authService.getCurrentUserDatabaseId).toHaveBeenCalledWith(mockEvent);
      expect(swapService.createSwapRequest).not.toHaveBeenCalled();
      expect(result.statusCode).toBe(401);
    });

    test('should validate swap request input', async () => {
      // Arrange
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
      swapValidation.validateSwapRequestInput.mockReturnValue({
        isValid: false,
        error: 'MVT amount must be a positive integer'
      });
      responseUtils.createBadRequestResponse.mockReturnValue({
        statusCode: 400,
        message: 'MVT amount must be a positive integer'
      });

      // Act
      const result = await swapHandlers.handleRequestMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(swapValidation.validateSwapRequestInput).toHaveBeenCalledWith(mockArgs.input);
      expect(swapService.createSwapRequest).not.toHaveBeenCalled();
      expect(result.statusCode).toBe(400);
    });

    test('should handle insufficient balance errors', async () => {
      // Arrange
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
      swapValidation.validateSwapRequestInput.mockReturnValue({ isValid: true });
      swapService.createSwapRequest.mockRejectedValue(new Error('Insufficient MVT balance'));

      // Act
      const result = await swapHandlers.handleRequestMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(responseUtils.handleSwapError).toHaveBeenCalled();
      expect(result.statusCode).toBe(400);
    });

    test('should handle missing wallet address errors', async () => {
      // Arrange
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
      swapValidation.validateSwapRequestInput.mockReturnValue({ isValid: true });
      swapService.createSwapRequest.mockRejectedValue(new Error('Please add a valid blockchain wallet address'));

      // Act
      const result = await swapHandlers.handleRequestMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(400);
    });

    test('should handle liquidity validation failures', async () => {
      // Arrange
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
      swapValidation.validateSwapRequestInput.mockReturnValue({ isValid: true });
      swapService.createSwapRequest.mockRejectedValue(new Error('Insufficient USDC liquidity'));

      // Act
      const result = await swapHandlers.handleRequestMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(400);
    });
  });

  describe('handleGetMVTWalletSwapRequests', () => {
    const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
    const mockArgs = { isAdmin: false };

    test('should successfully retrieve user swap requests', async () => {
      // Arrange
      const mockSwapRequests = [
        global.testUtils.createMockSwapRequest('swap-1', 'PENDING'),
        global.testUtils.createMockSwapRequest('swap-2', 'APPROVED')
      ];

      swapService.getSwapRequestsList.mockResolvedValue(mockSwapRequests);
      handlerUtils.handleSwapRequestListAuthorization.mockResolvedValue({
        context: { userId: 'test-user-123', isAdmin: false }
      });

      // Act
      const result = await swapHandlers.handleGetMVTWalletSwapRequests(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(mockSwapRequests);
    });

    test('should allow admin to view all swap requests', async () => {
      // Arrange
      const adminEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const adminArgs = { isAdmin: true };
      const mockSwapRequests = [
        global.testUtils.createMockSwapRequest('swap-1', 'PENDING'),
        global.testUtils.createMockSwapRequest('swap-2', 'APPROVED')
      ];

      swapService.getSwapRequestsList.mockResolvedValue(mockSwapRequests);
      handlerUtils.handleSwapRequestListAuthorization.mockResolvedValue({
        context: { userId: null, isAdmin: true }
      });

      // Act
      const result = await swapHandlers.handleGetMVTWalletSwapRequests(adminEvent, adminArgs);

      // Assert
      expect(result.statusCode).toBe(200);
    });

    test('should reject non-admin trying to access admin view', async () => {
      // Arrange
      const userArgs = { isAdmin: true };

      handlerUtils.handleSwapRequestListAuthorization.mockResolvedValue({
        error: {
          statusCode: 403,
          message: 'Unauthorized: Admin access required'
        }
      });

      // Act
      const result = await swapHandlers.handleGetMVTWalletSwapRequests(mockEvent, userArgs);

      // Assert
      expect(result.statusCode).toBe(403);
    });

    test('should handle unauthenticated requests', async () => {
      // Arrange
      handlerUtils.handleSwapRequestListAuthorization.mockResolvedValue({
        error: {
          statusCode: 401,
          message: 'Authentication required'
        }
      });

      // Act
      const result = await swapHandlers.handleGetMVTWalletSwapRequests(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(401);
    });
  });

  describe('handleApproveMVTWalletSwap', () => {
    const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
    const mockArgs = global.testUtils.createMockArgs({
      swapRequestId: 'swap-123'
    });

    test('should successfully approve swap request', async () => {
      // Arrange
      const mockApprovedSwap = global.testUtils.createMockSwapRequest('swap-123', 'APPROVED');
      mockApprovedSwap.transactionHash = '0xabcdef123456';
      mockApprovedSwap.processedAt = '2024-01-01T01:00:00.000Z';
      
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      swapValidation.validateSwapApprovalInput.mockReturnValue({ isValid: true });
      swapService.approveSwapRequest.mockResolvedValue(mockApprovedSwap);

      // Act
      const result = await swapHandlers.handleApproveMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(authService.checkAdminAuthorization).toHaveBeenCalledWith(mockEvent);
      expect(swapService.approveSwapRequest).toHaveBeenCalledWith('swap-123', 'admin-user-123');
      expect(result.statusCode).toBe(200);
      expect(result.data.status).toBe('APPROVED');
    });

    test('should reject non-admin users', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(false);

      // Act
      const result = await swapHandlers.handleApproveMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(403);
      expect(swapService.approveSwapRequest).not.toHaveBeenCalled();
    });

    test('should validate approval input', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      swapValidation.validateSwapApprovalInput.mockReturnValue({
        isValid: false,
        error: 'Swap request ID is required'
      });

      // Act
      const result = await swapHandlers.handleApproveMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(400);
      expect(swapService.approveSwapRequest).not.toHaveBeenCalled();
    });

    test('should handle swap not found errors', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      swapValidation.validateSwapApprovalInput.mockReturnValue({ isValid: true });
      swapService.approveSwapRequest.mockRejectedValue(new Error('Swap request not found'));

      // Act
      const result = await swapHandlers.handleApproveMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(404);
    });

    test('should handle blockchain transaction failures', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      swapValidation.validateSwapApprovalInput.mockReturnValue({ isValid: true });
      swapService.approveSwapRequest.mockRejectedValue(new Error('Blockchain transaction failed'));

      // Act
      const result = await swapHandlers.handleApproveMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });

    test('should handle already processed swaps', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      swapValidation.validateSwapApprovalInput.mockReturnValue({ isValid: true });
      swapService.approveSwapRequest.mockRejectedValue(new Error('Swap request already processed'));

      // Act
      const result = await swapHandlers.handleApproveMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(400);
    });
  });

  describe('handleRejectMVTWalletSwap', () => {
    const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
    const mockArgs = global.testUtils.createMockArgs({
      swapRequestId: 'swap-123',
      rejectionReason: 'Insufficient documentation'
    });

    test('should successfully reject swap request', async () => {
      // Arrange
      const mockRejectedSwap = global.testUtils.createMockSwapRequest('swap-123', 'REJECTED');
      mockRejectedSwap.processedAt = '2024-01-01T01:00:00.000Z';
      mockRejectedSwap.rejectionReason = 'Insufficient documentation';
      
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      swapValidation.validateSwapRejectionInput.mockReturnValue({ isValid: true });
      swapService.rejectSwapRequest.mockResolvedValue(mockRejectedSwap);

      // Act
      const result = await swapHandlers.handleRejectMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(swapService.rejectSwapRequest).toHaveBeenCalledWith(
        'swap-123',
        'admin-user-123',
        'Insufficient documentation'
      );
      expect(result.statusCode).toBe(200);
      expect(result.data.status).toBe('REJECTED');
    });

    test('should reject non-admin users', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(false);

      // Act
      const result = await swapHandlers.handleRejectMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(403);
      expect(swapService.rejectSwapRequest).not.toHaveBeenCalled();
    });

    test('should validate rejection input', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      swapValidation.validateSwapRejectionInput.mockReturnValue({
        isValid: false,
        error: 'Rejection reason is required'
      });

      // Act
      const result = await swapHandlers.handleRejectMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(400);
      expect(swapService.rejectSwapRequest).not.toHaveBeenCalled();
    });

    test('should handle token unlock failures gracefully', async () => {
      // Arrange
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      swapValidation.validateSwapRejectionInput.mockReturnValue({ isValid: true });
      swapService.rejectSwapRequest.mockRejectedValue(new Error('Failed to unlock tokens'));

      // Act
      const result = await swapHandlers.handleRejectMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });
  });

  describe('Security and Race Conditions', () => {
    test('should handle concurrent swap requests from same user', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs1 = global.testUtils.createMockArgs({ mvtAmount: 100, description: 'Swap 1' });
      const mockArgs2 = global.testUtils.createMockArgs({ mvtAmount: 200, description: 'Swap 2' });
      
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
      swapValidation.validateSwapRequestInput.mockReturnValue({ isValid: true });
      swapService.createSwapRequest
        .mockResolvedValueOnce(global.testUtils.createMockSwapRequest('swap-1', 'PENDING'))
        .mockResolvedValueOnce(global.testUtils.createMockSwapRequest('swap-2', 'PENDING'));

      // Act
      const [result1, result2] = await Promise.all([
        swapHandlers.handleRequestMVTWalletSwap(mockEvent, mockArgs1),
        swapHandlers.handleRequestMVTWalletSwap(mockEvent, mockArgs2)
      ]);

      // Assert
      expect(result1.statusCode).toBe(200);
      expect(result2.statusCode).toBe(200);
      expect(swapService.createSwapRequest).toHaveBeenCalledTimes(2);
    });

    test('should handle concurrent admin operations safely', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const approveArgs = global.testUtils.createMockArgs({ swapRequestId: 'swap-1' });
      const rejectArgs = global.testUtils.createMockArgs({ swapRequestId: 'swap-2', reason: 'Test' });
      
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
      swapValidation.validateSwapApprovalInput.mockReturnValue({ isValid: true });
      swapValidation.validateSwapRejectionInput.mockReturnValue({ isValid: true });
      
      swapService.approveSwapRequest.mockResolvedValue(global.testUtils.createMockSwapRequest('swap-1', 'APPROVED'));
      swapService.rejectSwapRequest.mockResolvedValue(global.testUtils.createMockSwapRequest('swap-2', 'REJECTED'));

      // Act
      const [approveResult, rejectResult] = await Promise.all([
        swapHandlers.handleApproveMVTWalletSwap(mockEvent, approveArgs),
        swapHandlers.handleRejectMVTWalletSwap(mockEvent, rejectArgs)
      ]);

      // Assert
      expect(approveResult.statusCode).toBe(200);
      expect(rejectResult.statusCode).toBe(200);
    });

    test('should prevent unauthorized access to admin operations', async () => {
      // Arrange
      const userEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = global.testUtils.createMockArgs({ swapRequestId: 'swap-123' });
      
      authService.checkAdminAuthorization.mockResolvedValue(false);

      // Act
      const approveResult = await swapHandlers.handleApproveMVTWalletSwap(userEvent, mockArgs);
      const rejectResult = await swapHandlers.handleRejectMVTWalletSwap(userEvent, mockArgs);

      // Assert
      expect(approveResult.statusCode).toBe(403);
      expect(rejectResult.statusCode).toBe(403);
      expect(swapService.approveSwapRequest).not.toHaveBeenCalled();
      expect(swapService.rejectSwapRequest).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling Edge Cases', () => {
    test('should handle malformed event objects', async () => {
      // Arrange
      const malformedEvent = null;
      const mockArgs = global.testUtils.createMockArgs({ mvtAmount: 100 });

      // Mock authService to throw error for null event
      authService.getCurrentUserDatabaseId.mockRejectedValue(new Error('Cannot read properties of null'));

      // Act
      const result = await swapHandlers.handleRequestMVTWalletSwap(malformedEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(400);
    });

    test('should handle network connectivity issues', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = global.testUtils.createMockArgs({ mvtAmount: 100 });
      
      authService.getCurrentUserDatabaseId.mockResolvedValue('test-user-123');
      swapValidation.validateSwapRequestInput.mockReturnValue({ isValid: true });
      swapService.createSwapRequest.mockRejectedValue(new Error('network error'));

      // Act
      const result = await swapHandlers.handleRequestMVTWalletSwap(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });

    test('should handle database timeout errors', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = { isAdmin: false };

      swapService.getSwapRequestsList.mockRejectedValue(new Error('timeout'));
      handlerUtils.handleSwapRequestListAuthorization.mockResolvedValue({
        context: { userId: 'test-user-123', isAdmin: false }
      });

      // Act
      const result = await swapHandlers.handleGetMVTWalletSwapRequests(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });
  });
});
