const { createLogger } = require('../logger');
const {
  formatDate,
  formatUserInfo,
  formatMetadata,
  formatOrganization,
  formatEvent,
  withErrorHandling,
} = require('./commonFormatters');

const logger = createLogger('unifyDataFormatter');

/**
 * Formats connection data into a readable string
 * @param {Object} connection - Connection data
 * @returns {string} Formatted connection info
 */
function formatConnection(connection) {
  if (!connection) return 'No connection data';

  const displayName =
    connection.name ||
    (connection.givenName || connection.familyName
      ? `${connection.givenName || ''} ${connection.familyName || ''}`.trim()
      : 'Unnamed Connection');
  return (
    `\n  - **${displayName}**\n` +
    (connection.type ? `    Type: ${connection.type}\n` : '') +
    (connection.createdAt ? `    Connected: ${formatDate(connection.createdAt)}\n` : '')
  );
}

/**
 * Formats activity data into a readable string
 * @param {Object} activity - Activity data
 * @returns {string} Formatted activity info
 */
function formatActivity(activity) {
  if (!activity) return 'No activity data';

  return (
    `\n  - **${activity.type || 'Activity'}**\n` +
    (activity.description ? `    ${activity.description}\n` : '') +
    (activity.timestamp ? `    ${formatDate(activity.timestamp)}\n` : '')
  );
}

/**
 * Converts unified query data into a meaningful summary
 * @param {Object} data - Raw unified data from the query
 * @returns {string} Formatted summary of the unified data
 */
function formatUnifyData(data) {
  if (!data) return 'No unified data available';

  return withErrorHandling(
    (data) => {
      let summary = '';

      // User Profile Section
      if (data.user) {
        summary += formatUserInfo({
          givenName: data.user.givenName,
          familyName: data.user.familyName,
          email: data.user.email,
          phone: data.user.phone,
          memberSince: data.user.memberSince,
        });
      }

      // Organizations Section
      if (data.organizations?.items?.length > 0) {
        summary += `## Organizations (${data.organizations.items.length})\n`;
        data.organizations.items.forEach((org) => {
          summary += formatOrganization(org);
        });
        summary += '\n';
      }

      // Upcoming Events Section
      if (data.events?.items?.length > 0) {
        const upcomingEvents = data.events.items
          .filter((event) => event.startDateTime && new Date(event.startDateTime) > new Date())
          .sort((a, b) => new Date(a.startDateTime) - new Date(b.startDateTime));

        if (upcomingEvents.length > 0) {
          summary += `## Upcoming Events (${upcomingEvents.length})\n`;
          upcomingEvents.forEach((event) => {
            summary += formatEvent(event);
          });
          summary += '\n';
        }
      }

      // Recent Activity Section
      if (data.recentActivity?.items?.length > 0) {
        const recentActivities = data.recentActivity.items
          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
          .slice(0, 5);

        summary += `## Recent Activity (${recentActivities.length})\n`;
        recentActivities.forEach((activity) => {
          summary += formatActivity(activity);
        });
        summary += '\n';
      }

      // Connections Section
      if (data.connections?.items?.length > 0) {
        summary += `## Connections (${data.connections.items.length})\n`;
        data.connections.items.forEach((connection) => {
          summary += formatConnection(connection);
        });
        summary += '\n';
      }

      // Add metadata
      summary += formatMetadata(data._metadata);

      return summary;
    },
    data,
    'Error processing unified data. Please try again later.',
    'unifyDataFormatter'
  );
}

module.exports = {
  formatUnifyData,
  formatConnection,
  formatActivity,
};
