const { v4: uuidv4 } = require('uuid');
const { createLogger, logError, logSuccess } = require('../shared/utils/logger');
const { getItem, putItem, scanItems, batchWriteItems } = require('../shared/database/dynamoUtils');
const { ChatTypeEnum, TABLES } = require('../shared/utils/constants');
const { getChatbotFunction } = require('./chatbot');

/**
 * Fetch chatbot prompt configuration from DynamoDB
 * @param {string} chatType - Type of chat prompt to fetch
 * @returns {Promise<Object|null>} - The prompt configuration or null if not found
 */
/**
 * Fetch chatbot prompt configuration from DynamoDB
 * @param {string} chatType - Type of chat prompt to fetch
 * @returns {Promise<Object|null>} - The prompt configuration or null if not found
 */
async function getChatbotPrompt(chatType = ChatTypeEnum.COMMUNITY) {
  const startTime = Date.now();
  const logger = createLogger({}, 'getChatbotPrompt');
  const requestId = `prompt-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;

  logger.info(`=== [getChatbotPrompt] START - ${requestId} ===`, {
    chatType,
    requestId,
    table: TABLES.CHATBOT_PROMPT,
    env: process.env.ENV,
    region: process.env.REGION,
  });

  try {
    logger.debug('Initiating DynamoDB scan for prompt configuration', {
      requestId,
      chatType,
      table: TABLES.CHATBOT_PROMPT,
      consistentRead: true,
    });

    const items = await scanItems(TABLES.CHATBOT_PROMPT, {
      FilterExpression: 'chatType = :chatType',
      ExpressionAttributeValues: { ':chatType': chatType },
      Limit: 1,
      ConsistentRead: true,
    });

    const endTime = Date.now();

    if (items && items.length > 0) {
      const prompt = items[0];
      logger.info('Successfully retrieved prompt configuration', {
        requestId,
        chatType,
        durationMs: endTime - startTime,
        promptId: prompt.id || 'unknown',
        hasTemplate: !!prompt.template,
        templateLength: prompt.template?.length || 0,
        promptKeys: Object.keys(prompt).filter((key) => key !== 'template'),
      });

      logger.debug('Full prompt configuration (template truncated)', {
        requestId,
        ...prompt,
        template: prompt.template ? `${prompt.template.substring(0, 200)}...` : null,
      });

      return prompt;
    }

    logger.warn('No prompt configuration found in DynamoDB', {
      requestId,
      chatType,
      durationMs: endTime - startTime,
      table: TABLES.CHATBOT_PROMPT,
      scanCount: items?.length || 0,
    });

    return null;
  } catch (error) {
    const errorInfo = {
      name: error.name,
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      chatType,
      requestId,
      table: TABLES.CHATBOT_PROMPT,
      operation: 'scan',
      timestamp: new Date().toISOString(),
    };

    logger.error('Failed to fetch prompt configuration', errorInfo);

    // Create a more specific error with additional context
    const enhancedError = new Error(
      `Failed to fetch prompt configuration for ${chatType}: ${error.message}`
    );
    enhancedError.originalError = error;
    enhancedError.chatType = chatType;
    enhancedError.table = TABLES.CHATBOT_PROMPT;
    enhancedError.requestId = requestId;

    throw enhancedError;
  }
}

/**
 * Get chatbot prompt data from DynamoDB
 * @param {string} userId - The ID of the user
 * @param {string} chatType - The type of chat
 * @returns {Promise<Object|null>} - The prompt data or null if not found
 */
async function getChatbotPromptDataFromDynamoDB(userId, chatType) {
  const startTime = Date.now();
  const logger = createLogger({}, 'getChatbotPromptDataFromDynamoDB');
  const requestId = `prompt-data-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;

  logger.info(`=== [getChatbotPromptDataFromDynamoDB] START - ${requestId} ===`, {
    userId,
    chatType,
    table: TABLES.CHATBOT_PROMPTS,
    env: process.env.ENV,
    region: process.env.REGION,
  });

  try {
    logger.debug('Initiating DynamoDB scan for prompt data', {
      requestId,
      userId,
      chatType,
      table: TABLES.CHATBOT_PROMPTS,
      consistentRead: true,
    });

    const items = await scanItems(TABLES.CHATBOT_PROMPTS, {
      FilterExpression: 'userId = :userId AND chatType = :chatType',
      ExpressionAttributeValues: {
        ':userId': userId,
        ':chatType': chatType,
      },
      Limit: 1,
      ConsistentRead: true,
    });

    const endTime = Date.now();

    if (items && items.length > 0) {
      const promptData = items[0];
      logger.info('Successfully retrieved prompt data from DynamoDB', {
        requestId,
        userId,
        chatType,
        durationMs: endTime - startTime,
        itemId: promptData.id || 'unknown',
        itemSize: JSON.stringify(promptData).length,
        dataKeys: Object.keys(promptData),
      });

      logger.debug('Prompt data details (truncated)', {
        requestId,
        ...Object.entries(promptData).reduce((acc, [key, value]) => {
          if (typeof value === 'string' && value.length > 200) {
            acc[key] = `${value.substring(0, 200)}... [${value.length} chars]`;
          } else {
            acc[key] = value;
          }
          return acc;
        }, {}),
      });

      return promptData;
    }

    logger.warn('No prompt data found in DynamoDB', {
      requestId,
      userId,
      chatType,
      durationMs: endTime - startTime,
      table: TABLES.CHATBOT_PROMPTS,
      scanCount: items?.length || 0,
    });

    return null;
  } catch (error) {
    const errorTime = Date.now();
    logError(logger, `Error fetching prompt data after ${errorTime - startTime}ms`, error, {
      userId,
      chatType,
      requestId,
      table: TABLES.CHATBOT_PROMPTS,
      errorDetails: {
        name: error.name,
        message: error.message,
        code: error.code,
        statusCode: error.statusCode,
        retryable: error.retryable,
        time: error.time?.toISOString(),
      },
    });
    throw error;
  }
}

/**
 * Store chatbot prompt data in DynamoDB
 * @param {string} userId - The ID of the user
 * @param {string} chatType - The type of chat
 * @param {Object} apolloClient - The Apollo Client instance
 * @param {Function} getChatbotFunction - Function to get the appropriate chatbot function
 * @param {Function} getChatbotPromptDataFromGraphQLApi - Function to get prompt data from GraphQL API
 * @returns {Promise<Object>} - The stored item
 */
async function storeChatbotPromptInDynamoDB(
  userId,
  chatType,
  apolloClient,
  getChatbotFunction,
  getChatbotPromptDataFromGraphQLApi
) {
  const logger = createLogger(
    {},
    {
      functionName: 'storeChatbotPromptInDynamoDB',
      userId,
      chatType,
    }
  );

  try {
    logger.info('Storing chatbot prompt data');

    // Get the chatbot function based on chatType
    const chatbotFunction = getChatbotFunction(chatType);
    if (!chatbotFunction) {
      const errorMsg = `Unsupported chat type: ${chatType}`;
      logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    // Get the prompt data using the chatbot function
    const promptData = await getChatbotPromptDataFromGraphQLApi(userId, apolloClient, chatType);
    if (!promptData) {
      const errorMsg = 'Failed to get prompt data from GraphQL API';
      logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    // Check if we already have this data in DynamoDB
    const existingData = await getChatbotPromptDataFromDynamoDB(userId, chatType);

    if (existingData) {
      logger.info('Found existing prompt data in DynamoDB');
      // Compare the data to see if we need to update
      if (JSON.stringify(existingData.promptData) === JSON.stringify(promptData)) {
        logger.info('Prompt data has not changed, skipping update');
        return existingData;
      }
    }

    // Generate a sample prompt to validate the data
    const sampleQuestion = 'What is your purpose?';
    await chatbotFunction(sampleQuestion, promptData, userId, apolloClient, logger);

    // Prepare the item to store in DynamoDB
    const item = {
      id: uuidv4(),
      userId: String(userId),
      chatType,
      promptData: JSON.stringify(promptData),
      isDynamic: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Store the item in DynamoDB
    await putItem(TABLES.CHATBOT_PROMPTS, item);
    logger.info('Successfully stored prompt data', { itemId: item.id });

    return item;
  } catch (error) {
    logger.error('Error storing prompt data', {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
}

/**
 * Remove chatbot prompt data from DynamoDB
 * @param {string} userId - The ID of the user (required)
 * @param {string} [chatType] - Optional chat type. If not provided, deletes all chat data for the user
 * @returns {Promise<{success: boolean, message: string, count: number}>} Result of the operation
 */
async function removeChatbotPromptFromDynamoDB(userId, chatType) {
  const logger = createLogger(
    {},
    {
      functionName: 'removeChatbotPromptFromDynamoDB',
      userId,
      chatType: chatType || 'ALL',
    }
  );

  try {
    logger.info('Removing chatbot prompt data', { userId, chatType: chatType || 'ALL' });

    // Validate input parameters
    if (!userId) {
      throw new Error('userId is required');
    }

    // Prepare scan parameters based on whether chatType is provided
    const scanParams = {
      FilterExpression: '#userId = :userId',
      ExpressionAttributeNames: {
        '#userId': 'userId',
      },
      ExpressionAttributeValues: {
        ':userId': String(userId),
      },
      ProjectionExpression: 'id, chatType',
      ConsistentRead: true,
    };

    // Add chatType filter if provided
    if (chatType) {
      scanParams.FilterExpression += ' AND #chatType = :chatType';
      scanParams.ExpressionAttributeNames['#chatType'] = 'chatType';
      scanParams.ExpressionAttributeValues[':chatType'] = String(chatType);
    }

    logger.debug('Scanning for items to delete', {
      scanParams: {
        ...scanParams,
        ExpressionAttributeValues: Object.keys(scanParams.ExpressionAttributeValues || {}).reduce(
          (acc, key) => {
            acc[key] = '***';
            return acc;
          },
          {}
        ),
      },
    });

    // Find all matching items
    const items = await scanItems(TABLES.CHATBOT_PROMPTS, scanParams);

    if (!items || items.length === 0) {
      const message = chatType
        ? `No ${chatType} prompt data found for user ${userId}`
        : `No prompt data found for user ${userId}`;

      logger.info(message);
      return {
        success: true,
        message,
        count: 0,
      };
    }

    logger.info(`Found ${items.length} items to delete`, {
      chatTypes: [...new Set(items.map((i) => i.chatType))],
      itemCount: items.length,
    });

    // Delete items in batches
    const deleteItems = items.map((item) => ({ id: item.id }));
    await batchWriteItems(TABLES.CHATBOT_PROMPTS, deleteItems, 'delete');

    const message = chatType
      ? `Successfully deleted ${items.length} items for chat type ${chatType}`
      : `Successfully deleted all (${items.length}) chat prompt items for user`;

    logger.info(message, {
      count: items.length,
      chatTypes: [...new Set(items.map((i) => i.chatType))],
    });

    return {
      success: true,
      message,
      count: items.length,
      chatTypes: chatType ? [chatType] : [...new Set(items.map((i) => i.chatType))],
    };
  } catch (error) {
    logger.error('Error removing prompt data', {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
}

// Export with a different name to avoid conflicts
module.exports = {
  getChatbotPrompt,
  getChatbotPromptDataFromDynamoDB,
  storeChatbotPromptInDynamoDB: storeChatbotPromptInDynamoDB,
  removeChatbotPromptFromDynamoDB,
  TABLES,
  // Export the function with a different name for internal use
  _storeChatbotPromptInDynamoDB: storeChatbotPromptInDynamoDB,
};
