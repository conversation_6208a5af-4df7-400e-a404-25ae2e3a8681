/* Amplify Params - DO NOT EDIT
  API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT
  API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT
  AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID
  ENV
  REGION
Amplify Params - DO NOT EDIT */ /*
Copyright 2017 - 2017 Amazon.com, Inc. or its affiliates. All Rights Reserved.
Licensed under the Apache License, Version 2.0 (the "License"). You may not use this file except in compliance with the License. A copy of the License is located at
    http://aws.amazon.com/apache2.0/
or in the "license" file accompanying this file. This file is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and limitations under the License.
*/

const express = require("express");
const bodyParser = require("body-parser");
const awsServerlessExpressMiddleware = require("aws-serverless-express/middleware");
const cors = require("cors");
const dotEnv = require("dotenv");
dotEnv.config();

// declare a new express app
const app = express();

// Enable CORS for all methods
app.use(cors());
app.use(function (req, res, next) {
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS"
  );
  res.setHeader("Access-Control-Allow-Headers", "*");
  res.setHeader("Access-Control-Allow-Credentials", true);
  next();
});
// // Middleware to parse JSON for non-webhook routes
app.use((req, res, next) => {
  console.log("req.originalUrl", req.originalUrl);
  
  // Skip JSON parsing for webhook routes
  if (req.originalUrl.startsWith('/webhooks/')) {
    next();
  } else {
    bodyParser.json()(req, res, next);
  }
});

// Raw body parser for webhooks
app.use((req, res, next) => {
  if (req.originalUrl.startsWith('/webhooks/')) {
    const getRawBody = require('raw-body');
    const contentType = req.headers['content-type'] || '';
    
    getRawBody(req, {
      length: req.headers['content-length'],
      limit: '10mb',
      encoding: 'utf8'
    }, function(err, string) {
      if (err) return next(err);
      req.rawBody = string;
      next();
    });
  } else {
    next();
  }
});
app.use(awsServerlessExpressMiddleware.eventContext());

const AWS = require("aws-sdk");
const uuid = require("uuid");
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

console.log("process.env.SECRET_ACCESS_KEY: ", process.env.SECRET_ACCESS_KEY);
AWS.config.update({
  maxRetries: 3,
  httpOptions: { timeout: 30000, connectTimeout: 5000 },
  region: process.env.REGION,
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY,
});

const USERPOOLID = process.env.AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID;
// const USERPOOLID = "us-east-1_XuBAw1nKj";
const CLIENTID = process.env.WEB_CLIENT_ID;
// const CLIENTID = "2p3viuv1dv714a6jufiskquk51";

const endpointSecret = "whsec_utjX2iEdAsHKqGY8Cp72BiZeQoOVld4b";
const lambda = new AWS.Lambda();

var ddb = new AWS.DynamoDB();
const sns = new AWS.SNS();
const AmazonCognitoIdentity = new AWS.CognitoIdentityServiceProvider();

// open.ai
const OpenAI = require("openai");
const openai = new OpenAI({
  apiKey: "***************************************************",
});
const fs = require("fs");

/*Initializing CognitoIdentityServiceProvider from AWS SDK JS*/
const cognito = new AWS.CognitoIdentityServiceProvider({
  apiVersion: "2016-04-18",
});

/**
 * @deprecated use /userCreateV2 instead
 * It's not a creating anything it's getting cognito user
 */
app.post("/user", async function (req, res) {
  console.log("check");
  try {
    const USERNAME = req.body.username;
    const cognitoParams = {
      UserPoolId: USERPOOLID,
      Username: USERNAME,
    };
    const response = await cognito.adminGetUser(cognitoParams).promise();

    res.json({ data: response });
  } catch (error) {
    res.status(error.statusCode).json({ error: error });
  }
});

/**
 * @deprecated use /userCreateV2 instead
 */
const createCognitoUser = async (req) => {
  const cognitoUserExistListParams = {
    AttributesToGet: ["name"],
    Filter: `email = "${req.body?.email}"`,
    UserPoolId: USERPOOLID,
  };

  const listExistUserResponse = await cognito
    .listUsers(cognitoUserExistListParams)
    .promise();
  const userExist = listExistUserResponse?.Users?.length || 0;

  if (userExist === 0) {
    const cognitoParams = {
      UserPoolId: USERPOOLID,
      Username: req.body?.email,
      ForceAliasCreation: true,
      TemporaryPassword: req.body?.password,
      UserAttributes: [
        { Name: "email", Value: req.body?.email },
        { Name: "email_verified", Value: "true" },
        { Name: "phone_number_verified", Value: "true" },
        { Name: "given_name", Value: req.body?.firstName },
        { Name: "family_name", Value: req.body?.lastName },
        { Name: "phone_number", Value: `+1${req.body?.phoneNumber}` },
        { Name: "name", Value: req.body?.name },
        { Name: "custom:role", Value: req.body?.role },
        {
          Name: "custom:registeredFrom",
          Value: req.body?.registeredFrom ?? "WEB",
        },
      ],
    };

    const response = await cognito.adminCreateUser(cognitoParams).promise();
    if (req.body.role === "SUPER_ADMIN") {
      const addUserToGroupParams = {
        GroupName: "Administrators",
        UserPoolId: USERPOOLID,
        Username: req.body?.email,
      };
      const addUserToGroupResponse = await cognito
        .adminAddUserToGroup(addUserToGroupParams)
        .promise();
    }
    return response;
  } else {
    throw new Error("Email already exists.");
  }
};

/**
 * @deprecated This function is deprecated and will be removed in future releases.
 * Please use the createUserInDynamoDBV2 instead.
 */
const createUserInDynamoDB = async (response, req) => {
  const date = new Date();
  let phoneNumber = "";
  phoneNumber = req.body?.phoneNumber
    ? req.body?.phoneNumber.replace(/-/g, "")
    : "";
  phoneNumber = phoneNumber ? phoneNumber.replace(/\s/g, "") : "";
  phoneNumber = phoneNumber ? phoneNumber.replace("+1", "") : "";

  const scanParams = {
    ExpressionAttributeValues: { ":d": { S: "false" } },
    FilterExpression: "#D = :d",
    ExpressionAttributeNames: { "#D": "isDeleted" },
    ProjectionExpression: "memberCode",
    TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
  };
  const scanResponse = await ddb.scan(scanParams).promise();
  const newResponse = scanResponse.Items.map((records) =>
    AWS.DynamoDB.Converter.unmarshall(records)
  );
  const memberCodes = newResponse.map((item) => parseInt(item.memberCode) || 0);
  let highestMemberCode = Math.max(...memberCodes);
  const params = {
    Item: {
      id: { S: response.User.Username },
      __typename: { S: "User" },
      email: { S: req.body?.email },
      familyName: { S: req.body?.lastName },
      givenName: { S: req.body?.firstName },
      phoneNumber: { S: `+1${phoneNumber}` },
      name: { S: req.body?.name },
      role: { S: req.body?.role },
      cityId: { S: req.body?.cityId },
      registeredFrom: { S: req.body?.registeredFrom ?? "WEB" },
      isDeleted: { S: "false" },
      isStakeholder: { BOOL: req.body?.isStakeholder ?? true },
      memberCode: {
        S: (highestMemberCode + 1).toString().padStart(4, "0") ?? "",
      },
      createdAt: { S: date.toISOString() },
      updatedAt: { S: date.toISOString() },
      _version: { N: "1" },
      _lastChangedAt: { N: date.getTime().toString() },
    },
    ReturnConsumedCapacity: "TOTAL",
    ReturnValues: "ALL_OLD",
    TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
  };

  let userResponse = await ddb.putItem(params).promise();
  userResponse.memberCode =
    (highestMemberCode + 1).toString().padStart(4, "0") ?? "";
  return userResponse;
};

/**
 * Creates a user in DynamoDB with the specified target table
 * @param {Object} req - The request object containing user data
 * @param {string} targetTable - The name of the DynamoDB table to create the user in
 * @returns {Promise<Object>} The created user data
 */
/**
 * Verifies a user in the system (v3 with target table support)
 * @param {Object} req - The request object containing user data
 * @param {string} targetTable - The name of the DynamoDB table to verify the user in
 * @returns {Promise<Object>} The verification result
 */
const verifyUserDefaultV3 = async (req, targetTable) => {
  try {
    const tableName = targetTable || `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
    
    const checkUserExistParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":e": { S: req.email.toLowerCase() },
        ":m": { S: req.memberCode.toString() },
      },
      FilterExpression: "#D = :d and #E = :e and #M = :m",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#E": "email",
        "#M": "memberCode",
      },
      TableName: tableName,
    };

    console.log('Searching for user with params:', JSON.stringify(checkUserExistParams, null, 2));
    const checkUserExistParamsRes = await ddb.scan(checkUserExistParams).promise();
    console.log('Scan result:', JSON.stringify({
      Count: checkUserExistParamsRes.Count,
      ScannedCount: checkUserExistParamsRes.ScannedCount,
      Items: checkUserExistParamsRes.Items ? checkUserExistParamsRes.Items.length : 0
    }, null, 2));
    
    let records = [];
    if (checkUserExistParamsRes.Items && checkUserExistParamsRes.Items.length > 0) {
      records = checkUserExistParamsRes.Items.map((item) => {
        const unmarshalled = AWS.DynamoDB.Converter.unmarshall(item);
        console.log('Found user record:', unmarshalled);
        return unmarshalled;
      });
    } else {
      console.log('No user records found matching the criteria');
    
    // Debug: Scan the table to see what's actually there
    const debugScanParams = {
      TableName: tableName,
      Limit: 5  // Just get a few records to check
    };
    try {
      const debugScanResult = await ddb.scan(debugScanParams).promise();
      console.log('Debug scan - First few records in table:', 
        debugScanResult.Items.map(item => AWS.DynamoDB.Converter.unmarshall(item))
      );
    } catch (debugError) {
      console.error('Debug scan error:', debugError);
    }
    }

    // Ensure phone number is in E.164 format (add +1 if not present)
    let phoneNumber = req.phoneNumber;
    if (!phoneNumber.startsWith('+')) {
      // If no country code is provided, assume US/Canada (+1)
      phoneNumber = `+1${phoneNumber.replace(/^\+?1?/, '')}`;
    }
    
    const checkUserExistParamsPhone = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":ph": { S: phoneNumber },
        ":e": { S: req.email.toLowerCase() },
      },
      FilterExpression: "#D = :d and #PH = :ph and #E <> :e",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#PH": "phoneNumber",
        "#E": "email",
      },
      TableName: tableName,
    };

    console.log('checkUserExistParamsPhone: ', checkUserExistParamsPhone);
    const checkUserExistParamPhonesRes = await ddb.scan(checkUserExistParamsPhone).promise();
    console.log('checkUserExistParamPhonesRes: ', checkUserExistParamPhonesRes);
    let recordsPhone = checkUserExistParamPhonesRes.Items.map((item) =>
      AWS.DynamoDB.Converter.unmarshall(item)
  );
  
  console.log('recordsPhone: ', recordsPhone);
    if (records && records.length > 0 && recordsPhone.length == 0) {
      let firstRecordObject = { firstRecord: records[0] };
      const cognitoUserExistListParams = {
        AttributesToGet: ["name"],
        Filter: `email = "${req.email}"`,
        UserPoolId: 'us-east-1_8wlPfBQL5',
      };

      const listExistUserResponse = await cognito
        .listUsers(cognitoUserExistListParams)
        .promise();
      
      let userExist = listExistUserResponse?.Users?.length || 0;
      
      if (userExist === 0) {
        // Create user in Cognito with email as username (matching V2 behavior)
        const cognitoParams = {
          UserPoolId: 'us-east-1_8wlPfBQL5',
          Username: req.email.toLowerCase(), // Using email as username to match V2
          ForceAliasCreation: true,
          TemporaryPassword: req.password,
          MessageAction: 'SUPPRESS', // Suppress the welcome email
          UserAttributes: [
            { Name: "email", Value: req.email.toLowerCase() },
            { Name: "email_verified", Value: "true" },
            { Name: "phone_number_verified", Value: "true" },
            { Name: "given_name", Value: firstRecordObject.firstRecord.givenName || '' },
            { Name: "family_name", Value: firstRecordObject.firstRecord.familyName || '' },
            { Name: "phone_number", Value: phoneNumber },
            { Name: "name", Value: firstRecordObject.firstRecord.name || '' },
            // Set custom attributes during user creation
            { Name: "custom:role", Value: (firstRecordObject.firstRecord.role || 'MEMBER').toString() },
            { Name: "custom:registeredFrom", Value: (req.registeredFrom || 'MIGRATION').toString() },
            { Name: "custom:dynamodbId", Value: firstRecordObject.firstRecord.id.toString() }
          ]
        };

        console.log('Creating Cognito user with params:', JSON.stringify(cognitoParams, null, 2));

        // 1. First create the user
        const response = await cognito.adminCreateUser(cognitoParams).promise();
        const cognitoUsername = response.User.Username;
        console.log('Cognito user created with username:', cognitoUsername);

        // 2. Set the password as permanent before confirming
        try {
          await cognito.adminSetUserPassword({
            UserPoolId: 'us-east-1_8wlPfBQL5',
            Username: cognitoUsername, // Use the cognito username returned from create
            Password: req.password,
            Permanent: true
          }).promise();
          console.log('Password set as permanent');
        } catch (pwdError) {
          console.error('Error setting permanent password:', pwdError);
          // Continue even if this fails, as we'll try to update attributes anyway
        }

        // 3. Update all user attributes in a single call
        const updateAttributes = {
          UserPoolId: 'us-east-1_8wlPfBQL5',
          Username: cognitoUsername,
          UserAttributes: [
            { Name: 'email_verified', Value: 'true' },
            { Name: 'phone_number_verified', Value: 'true' },
            { Name: 'email', Value: req.email.toLowerCase() },
            { Name: 'phone_number', Value: phoneNumber },
            { Name: 'given_name', Value: firstRecordObject.firstRecord.givenName || '' },
            { Name: 'family_name', Value: firstRecordObject.firstRecord.familyName || '' },
            { Name: 'name', Value: firstRecordObject.firstRecord.name || '' },
            // Set custom attributes
            { Name: 'custom:role', Value: (firstRecordObject.firstRecord.role || 'MEMBER').toString() },
            { Name: 'custom:registeredFrom', Value: (req.registeredFrom || 'MIGRATION').toString() },
            { Name: 'custom:dynamodbId', Value: firstRecordObject.firstRecord.id.toString() }
          ]
        };

        try {
          await cognito.adminUpdateUserAttributes(updateAttributes).promise();
          console.log('User attributes updated successfully');
        } catch (attrError) {
          console.warn('Warning: Could not update all user attributes:', attrError);
          // Continue even if attributes update fails
        }

        // 4. Explicitly confirm the user (this is critical to avoid force password change)
        try {
          await cognito.adminConfirmSignUp({
            UserPoolId: 'us-east-1_8wlPfBQL5',
            Username: cognitoUsername
          }).promise();
          console.log('User confirmed successfully');
        } catch (confirmError) {
          console.error('Error confirming user:', confirmError);
          // If confirmation fails, we'll still try to proceed
        }

        // Add to Administrators group if SUPER_ADMIN (matching V2 behavior)
        if (firstRecordObject.firstRecord.role === "SUPER_ADMIN") {
          try {
            await cognito.adminAddUserToGroup({
              GroupName: "Administrators",
              UserPoolId: 'us-east-1_8wlPfBQL5',
              Username: req.email.toLowerCase()
            }).promise();
            console.log('Added user to Administrators group');
          } catch (groupError) {
            console.warn('Warning: Could not add user to Administrators group. The group may not exist.');
            console.debug('Group error details:', groupError);
          }
        }

        // Update user in DynamoDB with the new cognitoId and confirmation status
        const updateUserParams = {
          TableName: tableName,
          Key: { id: { S: firstRecordObject.firstRecord.id } },
          ExpressionAttributeNames: {
            "#RF": "registeredFrom",
            "#PV": "phoneNumberVerified",
            "#PN": "phoneNumber",
            "#IS": "isSignup",
            "#CI": "cognitoId",
            "#ST": "status",
            "#UT": "updatedAt",
            "#CF": "confirmationStatus"
          },
          ExpressionAttributeValues: {
            ":rf": { S: req.registeredFrom || 'MIGRATION' },
            ":pv": { BOOL: true },
            ":pn": { S: phoneNumber },
            ":is": { BOOL: true },
            ":ci": { S: cognitoUsername },
            ":st": { S: "active" },
            ":ut": { S: new Date().toISOString() },
            ":cf": { S: "CONFIRMED" }
          },
          UpdateExpression: "SET #RF = :rf, #PV = :pv, #PN = :pn, #IS = :is, #CI = :ci, #ST = :st, #UT = :ut, #CF = :cf",
          ReturnValues: "ALL_NEW",
          ConditionExpression: "attribute_not_exists(cognitoId) OR cognitoId = :ci",
        };

        await ddb.updateItem(updateUserParams).promise();
        
        return { 
          success: true, 
          message: "User verified successfully",
          cognitoId: response.User.Username
        };
      } else {
        // User already exists in Cognito, update their attributes and confirm them
        const existingCognitoUser = listExistUserResponse.Users[0];
        const cognitoId = existingCognitoUser.Username;
        
        console.log('User already exists in Cognito, updating attributes for:', cognitoId);
        
        // Update Cognito user attributes to ensure they're confirmed and have correct values
        const updateAttributes = {
          UserPoolId: 'us-east-1_8wlPfBQL5',
          Username: cognitoId,
          UserAttributes: [
            { Name: 'email_verified', Value: 'true' },
            { Name: 'phone_number_verified', Value: 'true' },
            { Name: 'email', Value: req.email.toLowerCase() },
            { Name: 'phone_number', Value: phoneNumber },
            { Name: 'given_name', Value: firstRecordObject.firstRecord.givenName || '' },
            { Name: 'family_name', Value: firstRecordObject.firstRecord.familyName || '' },
            { Name: 'name', Value: firstRecordObject.firstRecord.name || '' },
            // Set custom attributes
            { Name: 'custom:role', Value: (firstRecordObject.firstRecord.role || 'MEMBER').toString() },
            { Name: 'custom:registeredFrom', Value: (req.registeredFrom || 'MIGRATION').toString() },
            { Name: 'custom:dynamodbId', Value: firstRecordObject.firstRecord.id.toString() }
          ]
        };

        try {
          await cognito.adminUpdateUserAttributes(updateAttributes).promise();
          console.log('Existing Cognito user attributes updated successfully');
          
          // Ensure the user is confirmed
          try {
            await cognito.adminConfirmSignUp({
              UserPoolId: 'us-east-1_8wlPfBQL5',
              Username: cognitoId
            }).promise();
            console.log('Existing user confirmed successfully');
          } catch (confirmError) {
            console.warn('Warning: Could not confirm existing user - they may already be confirmed:', confirmError.message);
          }
          
          // Set password as permanent if not already
          try {
            await cognito.adminSetUserPassword({
              UserPoolId: 'us-east-1_8wlPfBQL5',
              Username: cognitoId,
              Password: req.password || 'Test@1234',
              Permanent: true
            }).promise();
            console.log('Password set as permanent for existing user');
          } catch (pwdError) {
            console.warn('Warning: Could not set password for existing user:', pwdError.message);
          }
          
        } catch (attrError) {
          console.error('Error updating attributes for existing Cognito user:', attrError);
          // Continue with the migration even if attributes update fails
        }
        
        // Update the DynamoDB record with the existing Cognito ID and confirmation status
        const updateUserParams = {
          TableName: tableName,
          Key: { id: { S: firstRecordObject.firstRecord.id } },
          ExpressionAttributeNames: {
            "#CI": "cognitoId",
            "#UT": "updatedAt",
            "#CF": "confirmationStatus"
          },
          ExpressionAttributeValues: {
            ":ci": { S: cognitoId },
            ":ut": { S: new Date().toISOString() },
            ":cf": { S: "CONFIRMED" }
          },
          UpdateExpression: "SET #CI = :ci, #UT = :ut, #CF = :cf",
          ReturnValues: "ALL_NEW",
        };

        await ddb.updateItem(updateUserParams).promise();
        
        return { 
          success: true, 
          message: "User verified successfully with existing Cognito account",
          cognitoId: cognitoId
        };
      }
    } else {
      if (recordsPhone.length > 0) {
        return {
          success: false,
          message: "Phone number already exists please try other phone number."
        };
      } else {
        return {
          success: false,
          message: "Email or member code not exists please contact admin."
        };
      }
    }
  } catch (error) {
    console.error("Error in verifyUserDefaultV3:", error);
    return { 
      success: false, 
      message: error.message || "Error verifying user" 
    };
  }
};

/**
 * Migrates a user from one DynamoDB table to another
 * @param {Object} req - The request object containing user data
 * @param {string} targetTable - The name of the target DynamoDB table to migrate the user to
 * @returns {Promise<Object>} The migrated user data
 */
const createUserInDynamoDBv3 = async (req, sourceTable, targetTable) => {
  try {
    if (!req.body.email) {
      throw new Error("Email is required for user migration");
    }

    // Use the targetTable if provided, otherwise use the static develop table name
    const tableName = targetTable || 'User-fznlrfufwfdbxkhodjhgase67a-develop';
    
    // Check if user already exists in target table
    const checkUserExistParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":e": { S: req.body.email.toLowerCase() },
      },
      FilterExpression: "#D = :d and #E = :e",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#E": "email",
      },
      TableName: tableName,
    };

    const checkUserExistParamsRes = await ddb.scan(checkUserExistParams).promise();
    if (checkUserExistParamsRes.Items && checkUserExistParamsRes.Items.length > 0) {
      throw new Error("User with this email already exists in the target table");
    }

    const scanParams = {
      TableName: sourceTable,
      FilterExpression: '#email = :email AND #isDeleted = :isDeleted',
      ExpressionAttributeNames: {
        '#email': 'email',
        '#isDeleted': 'isDeleted'
      },
      ExpressionAttributeValues: {
        ':email': { S: req.body.email.toLowerCase() },
        ':isDeleted': { S: 'false' }
      }
    };

    const scanResult = await ddb.scan(scanParams).promise();
    
    if (!scanResult.Items || scanResult.Items.length === 0) {
      throw new Error("User not found in source table");
    }
    
    // Get the first matching user (should be only one since emails are unique)
    const userData = { Item: scanResult.Items[0] };

    // Convert DynamoDB item to regular object
    const userItem = AWS.DynamoDB.Converter.unmarshall(userData.Item);


    // Preserve the original id for Cognito sub and custom:dynamodbId
    const originalId = userItem.id;

    // Sanitize phone number to E.164 format
    let phoneNumber = userItem.phoneNumber || "";
    // Remove all non-digit characters except leading +
    phoneNumber = phoneNumber.replace(/[^\d+]/g, "");
    // Ensure starts with +
    if (!phoneNumber.startsWith("+")) {
      // Assume US if not present
      phoneNumber = "+1" + phoneNumber.replace(/^1/, "");
    }
    // Ensure only digits after +
    phoneNumber = phoneNumber.replace(/(\+\d{1,3})(\d+)/, "$1$2");

    // Update timestamps
    const now = new Date();
    userItem.updatedAt = now.toISOString();
    userItem._lastChangedAt = now.getTime(); // Store as number
    userItem._version = 1; // Store as number

    // Ensure memberCode is preserved from source data
    userItem.memberCode = userItem.memberCode || "";

    try {
      // Prepare the item for the target table with proper number types
      const itemToSave = {
        ...userItem,
        _lastChangedAt: Number(userItem._lastChangedAt), // Ensure number type
        _version: Number(userItem._version) // Ensure number type
      };
      
      const putParams = {
        TableName: tableName,
        Item: AWS.DynamoDB.Converter.marshall(itemToSave, { convertEmptyValues: true }),
        ReturnConsumedCapacity: "TOTAL"
      };

      console.log('Attempting to save user with params:', JSON.stringify(putParams, null, 2));

      // Insert into target table
      await ddb.putItem(putParams).promise();
      console.log('Successfully migrated user to target table');

      // Create Cognito user with originalId as sub and custom:dynamodbId
      const cognitoParams = {
        UserPoolId: "us-east-1_8wlPfBQL5",
        Username: userItem.email.toLowerCase(),
        ForceAliasCreation: true,
        TemporaryPassword: req.body.password,
        UserAttributes: [
          { Name: "email", Value: userItem.email.toLowerCase() },
          { Name: "email_verified", Value: "true" },
          { Name: "phone_number_verified", Value: "true" },
          { Name: "given_name", Value: userItem.givenName || '' },
          { Name: "family_name", Value: userItem.familyName || '' },
          { Name: "phone_number", Value: phoneNumber },
          { Name: "name", Value: userItem.name || '' },
          { Name: "custom:role", Value: (userItem.role || 'MEMBER').toString() },
          { Name: "custom:registeredFrom", Value: (userItem.registeredFrom || 'MIGRATION').toString() },
          { Name: "custom:dynamodbId", Value: originalId }
        ],
        MessageAction: 'SUPPRESS'
      };

      let response;
      try {
        response = await cognito.adminCreateUser(cognitoParams).promise();

        // Set the password as permanent if provided
        if (req.body.password) {
          await cognito.adminSetUserPassword({
            UserPoolId: "us-east-1_8wlPfBQL5",
            Username: userItem.email.toLowerCase(),
            Password: req.body.password,
            Permanent: true,
          }).promise();
        }
      } catch (cognitoError) {
        if (cognitoError.code === 'UsernameExistsException') {
          // User already exists, fetch and update attributes
          console.warn('Cognito user already exists, updating attributes.');
          // Get existing user
          response = await cognito.adminGetUser({
            UserPoolId: "us-east-1_8wlPfBQL5",
            Username: userItem.email.toLowerCase()
          }).promise();
          // Optionally, update password if needed
          if (req.body.password) {
            try {
              await cognito.adminSetUserPassword({
                UserPoolId: "us-east-1_8wlPfBQL5",
                Username: userItem.email.toLowerCase(),
                Password: req.body.password,
                Permanent: true,
              }).promise();
            } catch (pwError) {
              console.warn('Could not update password for existing user:', pwError.message);
            }
          }
        } else {
          throw cognitoError;
        }
      }

      // Update custom attributes (redundant but ensures consistency)
      try {
        await cognito.adminUpdateUserAttributes({
          UserPoolId: "us-east-1_8wlPfBQL5",
          Username: userItem.email.toLowerCase(),
          UserAttributes: [
            { Name: "custom:role", Value: (userItem.role || 'MEMBER').toString() },
            { Name: "custom:registeredFrom", Value: (userItem.registeredFrom || 'MIGRATION').toString() },
            { Name: "custom:dynamodbId", Value: originalId }
          ]
        }).promise();
        console.log('Successfully updated custom attributes');
      } catch (attrError) {
        console.warn('Warning: Could not set custom attributes. This is expected if custom attributes are not defined in the User Pool schema.');
        console.debug('Attribute error details:', attrError);
      }

      // Add to Administrators group if SUPER_ADMIN
      if (userItem.role === "SUPER_ADMIN") {
        try {
          await cognito.adminAddUserToGroup({
            GroupName: "Administrators",
            UserPoolId: "us-east-1_8wlPfBQL5",
            Username: userItem.email.toLowerCase(),
          }).promise();
        } catch (groupError) {
          console.warn('Warning: Could not add user to Administrators group.', groupError);
        }
      }

      // Update user in DynamoDB with Cognito sub
      await ddb.updateItem({
        TableName: tableName,
        Key: { id: { S: originalId } },
        ExpressionAttributeNames: {
          "#CI": "cognitoId",
          "#UT": "updatedAt"
        },
        ExpressionAttributeValues: {
          ":ci": { S: response.User.Username },
          ":ut": { S: now.toISOString() }
        },
        UpdateExpression: "SET #CI = :ci, #UT = :ut",
        ReturnValues: "ALL_NEW",
      }).promise();

      // Return the migrated user data
      return {
        success: true,
        message: "User migrated successfully",
        data: userItem
      };
    } catch (putError) {
      console.error('Error saving user to target table or Cognito:', putError);
      throw new Error(`Failed to save user to target table or Cognito: ${putError.message}`);
    }
  } catch (error) {
    console.error("Error in createUserInDynamoDBv3:", error);
    throw error;
  }
};

const createUserInDynamoDBv2 = async (req) => {
  try {
    const checkUserExistParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":e": { S: req.body.email.toLowerCase() },
      },
      FilterExpression: "#D = :d and #E = :e",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#E": "email",
      },
      TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };

    const checkUserExistParamsRes = await ddb
      .scan(checkUserExistParams)
      .promise();
    let records = checkUserExistParamsRes.Items.map((item) =>
      AWS.DynamoDB.Converter.unmarshall(item)
    );

    const checkUserExistParamsPhone = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":ph": { S: `+1${req.body.phoneNumber}` },
      },
      FilterExpression: "#D = :d and #PH = :ph",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#PH": "phoneNumber",
      },
      TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };

    const checkUserExistParamsResPhone = await ddb
      .scan(checkUserExistParamsPhone)
      .promise();
    let recordsPhone = checkUserExistParamsResPhone.Items.map((item) =>
      AWS.DynamoDB.Converter.unmarshall(item)
    );

    if (records && records.length > 0) {
      throw new Error("Email already exists.");
    } else if (recordsPhone && recordsPhone.length > 0) {
      throw new Error("Phone number already exists.");
    } else {
      const cityNames = await Promise.all(
        req.body.citiesArray.map(async (city) => {
          const getCityParams = {
            TableName: `City-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
            Key: { id: { S: city } },
          };

          try {
            const getCityResponse = await ddb.getItem(getCityParams).promise();
            const cityData = getCityResponse.Item
              ? AWS.DynamoDB.Converter.unmarshall(getCityResponse.Item)
              : null;

            return cityData ? cityData.name : null;
          } catch (error) {
            console.error("Error fetching city data:", error);
            return null;
          }
        })
      );

      function formatCityNames(cityNamesArray) {
        const filteredCities = cityNamesArray.filter(Boolean);

        if (filteredCities.length === 0) return "";
        if (filteredCities.length === 1) return filteredCities[0];
        if (filteredCities.length === 2)
          return `${filteredCities[0]} and ${filteredCities[1]}`;

        return `${filteredCities.slice(0, -1).join(", ")} and ${filteredCities[filteredCities.length - 1]
          }`;
      }

      const stackholderCitiesArray = {
        L: req.body.citiesArray.map((city) => ({
          S: city,
        })),
      };
      const date = new Date();
      let phoneNumber = req.body?.phoneNumber
        ? req.body?.phoneNumber.replace(/-/g, "")
        : "";
      phoneNumber = phoneNumber ? phoneNumber.replace(/\s/g, "") : "";
      phoneNumber = phoneNumber ? phoneNumber.replace("+1", "") : "";
      let createId = uuid.v4();
      let createMembershipId = uuid.v4();

      const scanParams = {
        ExpressionAttributeValues: { ":d": { S: "false" } },
        FilterExpression: "#D = :d",
        ExpressionAttributeNames: { "#D": "isDeleted" },
        ProjectionExpression: "memberCode",
        TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
      };

      const scanResponse = await ddb.scan(scanParams).promise();
      const newResponse = scanResponse.Items.map((records) =>
        AWS.DynamoDB.Converter.unmarshall(records)
      );
      const memberCodes = newResponse.map(
        (item) => parseInt(item.memberCode) || 0
      );
      let highestMemberCode = Math.max(...memberCodes);
      let memberCodeGenerate =
        (highestMemberCode + 1).toString().padStart(4, "0") ?? "";
      console.log("memberCodeGenerate", memberCodeGenerate);
      const params = {
        Item: {
          id: { S: createId },
          __typename: { S: "User" },
          email: { S: req.body?.email.toLowerCase() },
          familyName: { S: req.body?.lastName },
          givenName: { S: req.body?.firstName },
          phoneNumber: { S: `+1${phoneNumber}` },
          gender: { S: req.body?.gender ?? "NONE" },
          streetAddressOne: {
            S: req.body?.streetAddressOne ?? "",
          },
          streetAddressTwo: {
            S: req.body?.streetAddressTwo ?? "",
          },
          birthday: { S: req.body?.birthday ?? "" },
          imageUrl: { S: req.body?.imageUrl ?? "" },

          city: { S: req.body?.city ?? "" },
          state: { S: req.body?.state ?? "" },
          zipCode: { S: req.body?.zipCode ?? "" },
          countryCode: { S: req.body?.countryCode ?? "" },

          name: { S: req.body?.name },
          role: { S: req.body?.role },
          assignedRole: { S: req.body?.assignedRole },
          cityId: { S: req.body?.cityId },
          registeredFrom: { S: req.body?.registeredFrom ?? "WEB" },
          isDeleted: { S: "false" },
          isStakeholder: { BOOL: req.body?.isStakeholder ?? true },
          memberCode: {
            S: memberCodeGenerate,
          },
          status: {
            S: req.body?.isStakeholder === true ? "inactive" : "not-enrolled",
          },
          userAddedFrom: {
            S: req.body?.userAddedFrom ?? "STAKEHOLDER_CREATED",
          },
          membershipId: { S: createMembershipId },
          stackholderCities: stackholderCitiesArray,
          userType: {
            S: req.body.userType ?? "loginUser",
          },
          isAssociated: {
            BOOL: req.body.isAssociated ?? false,
          },
          isSignup: {
            BOOL: false,
          },
          isDeleted: { S: "false" },

          createdAt: { S: date.toISOString() },
          updatedAt: { S: date.toISOString() },
          _version: { N: "1" },
          _lastChangedAt: { N: date.getTime().toString() },
        },
        ReturnConsumedCapacity: "TOTAL",
        ReturnValues: "ALL_OLD",
        TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
      };

      if (req.body?.isStakeholder === false && req.body?.ethnicity?.trim()) {
        params.Item.ethnicity = { S: req.body.ethnicity.trim() };
      }

      const membershipParams = {
        Item: {
          id: { S: createMembershipId },
          __typename: { S: "Membership" },
          type: { S: "User" },
          name: {
            S: req.body?.firstName + " " + req.body?.lastName ?? "",
          },
          personsID: { S: createId },
          isActive: { BOOL: false },
          lastAddedImpactScore: {
            S: req.body.isAssociated === true ? "5" : "0.00",
          },
          currentImpactScore: { S: "0.00" },
          MVPTokens: { N: "0.00" },
          cityId: { S: req.body?.cityId ?? "jacksonville" },
          memberCode: {
            S: memberCodeGenerate,
          },
          isDeleted: { S: "false" },
          createdAt: { S: date.toISOString() },
          updatedAt: { S: date.toISOString() },
          _version: { N: "1" },
          _lastChangedAt: { N: date.getTime().toString() },
        },
        TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
      };

      let userResponse = await ddb.putItem(params).promise();
      console.log("userResponse", userResponse);
      let membershipResponse = await ddb.putItem(membershipParams).promise();
      if (userResponse) {
        let sendMailFunction = `sendEmail-${process.env.ENV}`;
        // let sendMailFunction = `sendEmail-amplifydev`;
        let mailPayload = {
          arguments: {
            input: {
              firstName: req.body.givenName,
              memberCode: memberCodeGenerate,
              cityLocation: formatCityNames(cityNames),
              phoneNumber: req.body.phoneNumber,
              to: req.body?.email.toLowerCase(),
              // to: "<EMAIL>",
            },
          },
          fieldName: "welcomeMail",
        };

        let sendMailParams = {
          FunctionName: sendMailFunction,
          InvocationType: "RequestResponse",
          LogType: "None",
          Payload: JSON.stringify(mailPayload),
        };
        console.log("sendMailParams", sendMailParams);
        await lambda.invoke(sendMailParams).promise();
        userResponse.createId = createId;
        userResponse.memberCode = memberCodeGenerate;
      }
      return userResponse;
    }
  } catch (error) {
    console.error("Error creating user in DynamoDB:", error);
    throw error;
  }
};

const createAssoction = async (req, userRes) => {
  const date = new Date();
  let createAssociationId = uuid.v4();

  const createAssociationParams = {
    Item: {
      id: { S: createAssociationId },
      __typename: { S: "Association" },
      type: { S: "Organization" },
      createdBy: {
        S: req.body?.createdBy,
      },
      organizationID: {
        S: req.body?.organizationID,
      },
      relationType: {
        S: req.body?.relationType,
      },
      userUserAssociationsId: {
        S: userRes.createId,
      },
      organizationsAssociationsId: {
        S: req.body?.organizationID,
      },
      personsID: { S: userRes.createId },
      cityId: { S: req.body?.cityId },
      status: { BOOL: false },
      isDeleted: { S: "false" },
      createdAt: { S: date.toISOString() },
      updatedAt: { S: date.toISOString() },
      _version: { N: "1" },
      _lastChangedAt: { N: date.getTime().toString() },
    },
    TableName: `Association-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
  };

  try {
    let createAssociationRes = await ddb
      .putItem(createAssociationParams)
      .promise();

    const findMembershipParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":o": { S: req.body.organizationID },
      },
      FilterExpression: "#D = :d and #O=:o",
      ExpressionAttributeNames: { "#D": "isDeleted", "#O": "organizationID" },
      TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };
    const scanResponse = await ddb.scan(findMembershipParams).promise();
    const newResponse = scanResponse.Items.map((records) =>
      AWS.DynamoDB.Converter.unmarshall(records)
    );

    if (newResponse) {
      let addedImpactScoreUpdate =
        Number(newResponse[0].currentImpactScore) + 5;
      const updateMemberParams = {
        TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        Key: {
          id: { S: newResponse[0].id },
        },
        ExpressionAttributeNames: {
          "#LS": "lastAddedImpactScore",
          "#CS": "currentImpactScore",
        },
        ExpressionAttributeValues: {
          ":ls": { S: "5" },
          ":cs": { S: addedImpactScoreUpdate.toString() },
        },
        ReturnValues: "ALL_NEW",
        UpdateExpression: "SET #LS = :ls, #CS = :cs",
      };
      let updatedMember = await ddb.updateItem(updateMemberParams).promise();
      console.log("updatedMember", updatedMember);
    }

    return createAssociationRes;
  } catch (error) {
    console.error("Error creating association:", error);
    throw new Error("Failed to create association");
  }
};

const getCityAbbreviation = async (cityId) => {
  const params = {
    TableName: `City-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    Key: {
      id: { S: cityId },
    },
    ProjectionExpression: "abbreviation",
  };

  const data = await ddb.getItem(params).promise();
  const abbreviation = data.Item?.abbreviation?.S;
  return abbreviation;
};

const createIdeaInDynamoDB = async (req) => {
  const cityAbbreviation = await getCityAbbreviation(req.body.cityId);
  const scanParams = {
    ExpressionAttributeValues: {
      ":d": { S: "false" },
      ":c": { S: req.body.cityId },
    },
    FilterExpression: "#D = :d and #C=:c",
    ExpressionAttributeNames: { "#D": "isDeleted", "#C": "cityId" },
    TableName: `Ideas-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
  };
  const scanResponse = await ddb.scan(scanParams).promise();
  const newResponse = scanResponse.Items.map((records) =>
    AWS.DynamoDB.Converter.unmarshall(records)
  );
  const ideaIDs = newResponse.map((item) =>
    item.ideaID ? parseInt(item.ideaID.split("-")[1]) || 0 : 0
  );
  let highestIdeaId = Math.max(...ideaIDs);
  let newIdeaID = `${cityAbbreviation}-${(highestIdeaId + 1)
    .toString()
    .padStart(5, "0")}`;
  const updateIdeaParams = {
    TableName: `Ideas-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    Key: {
      id: { S: req.body.id },
    },
    ExpressionAttributeNames: {
      "#FT": "ideaID",
    },
    ExpressionAttributeValues: {
      ":ft": { S: newIdeaID },
    },
    ReturnValues: "ALL_NEW",
    UpdateExpression: "SET #FT = :ft",
  };
  console.log(updateIdeaParams);
  try {
    let updatedIdea = await ddb.updateItem(updateIdeaParams).promise();
    console.log("updatedIdea", updatedIdea);
    return true;
  } catch (err) {
    console.log("Error", err);
    return err.message;
  }
};

/**
 * @deprecated use /userCreatev2 instead
 */
app.post("/userCreate", async function (req, res) {
  try {
    const response = await createCognitoUser(req);
    const userResponse = await createUserInDynamoDB(response, req);
    res
      .status(200)
      .json({ isExist: 0, data: response, userData: userResponse });
  } catch (error) {
    res.status(500).json({ isExist: 1, message: error.message });
  }
});

app.post("/userCreatev2", async function (req, res) {
  try {
    const userResponse = await createUserInDynamoDBv2(req);
    if (req.body.isAssociated === true && userResponse) {
      await createAssoction(req, userResponse);
    }
    res.status(200).json({ isExist: 0, userData: userResponse });
  } catch (error) {
    res.status(500).json({ isExist: 1, message: error.message });
  }
});


app.post("/ideaCreate", async function (req, res) {
  try {
    const userResponse = await createIdeaInDynamoDB(req);

    res.status(200).json({ status: 200, message: "Idea created successfully" });
  } catch (error) {
    res.status(500).json({ isExist: 1, message: error.message });
  }
});

app.get("/user", async function (req, res) {
  console.log("Get User")
  try {
    let hasMore = true;
    let users = [];
    let nextPageToken = "";

    while (hasMore) {
      const cognitoParams = {
        AttributesToGet: ["name"],
        Filter: 'status = "Enabled"',
        Limit: 60,
        UserPoolId: USERPOOLID,
      };
      if (nextPageToken !== "") {
        cognitoParams.PaginationToken = nextPageToken;
      }
      const response = await cognito.listUsers(cognitoParams).promise();

      response.Users.forEach((element) =>
        users.push({
          userId: element.Username,
          name: element.Attributes?.[0]?.Value || "",
        })
      );

      if (response.hasOwnProperty("PaginationToken")) {
        nextPageToken = response.PaginationToken;
      } else {
        hasMore = false;
      }
    }

    res.json({ data: { users } });
  } catch (error) {
    console.log('error: ', error);
    res.status(500).json({ error: error });
  }
});

app.delete("/user", async function (req, res) {
  try {
    const USERID = req.body.id;
    // get user frist

    const userTableName = `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

    const getUserParams = {
      TableName: userTableName,
      Key: { id: { S: USERID } },
    };
    const userItem = await ddb.getItem(getUserParams).promise();
    const getUserResponse = userItem["Item"]
      ? AWS.DynamoDB.Converter.unmarshall(userItem["Item"])
      : null;
    console.log("getUserResponse", getUserResponse);
    if (getUserResponse) {
      // Association delete query
      var associationParams = {
        ExpressionAttributeValues: {
          ":u": { S: USERID },
          ":op": { S: USERID },
        },
        FilterExpression: "#P = :u or #OP = :op",
        ExpressionAttributeNames: {
          "#P": "personsID",
          "#OP": "otherPersonsID",
        },
        ProjectionExpression: "id",
        TableName:
          "Association-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      const response = await ddb.scan(associationParams).promise();

      const records = response.Items;

      const newResponse = records.map((record) => {
        const unmarshalledRecord = AWS.DynamoDB.Converter.unmarshall(record);
        return unmarshalledRecord.id; // Extract only the id field
      });

      // create aaray of newResponse.id and pass it to delete common function

      // delete associations with common function
      await Promise.all([
        deleteItems(
          "Association-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
          newResponse
        ),
      ]); // newResponse change with ids

      // Post delete query
      var postParams = {
        ExpressionAttributeValues: {
          ":u": { S: USERID },
        },
        FilterExpression: "#M = :u",
        ExpressionAttributeNames: {
          "#M": "memberId",
        },
        ProjectionExpression: "id",
        TableName:
          "Submission-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      const postResponse = await ddb.scan(postParams).promise();

      const postRecords = postResponse.Items;

      // const postMapResponse = postRecords.map((postRecords) =>
      //   AWS.DynamoDB.Converter.unmarshall(postRecords)
      // );

      const postMapResponse = postRecords.map((record) => {
        const unmarshalledRecord = AWS.DynamoDB.Converter.unmarshall(record);
        return unmarshalledRecord.id; // Extract only the id field
      });
      // delete comman function postMapResponse
      // Submission delete

      await Promise.all([
        deleteItems(
          "Submission-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
          postMapResponse
        ),
      ]); // postMapResponse change with ids

      // get homeworkUserId
      var homeWorkUserParams = {
        ExpressionAttributeValues: {
          ":u": { S: USERID },
        },
        FilterExpression: "#M = :u",
        ExpressionAttributeNames: {
          "#M": "studentStakeholderId",
        },
        ProjectionExpression: "id",
        TableName:
          "HomeworkUsers-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      const homeWorkUserResponse = await ddb.scan(homeWorkUserParams).promise();

      const homeWorkUserRecords = homeWorkUserResponse.Items;

      const homeWorkUserMapResponse = homeWorkUserRecords.map((record) => {
        const unmarshalledRecord = AWS.DynamoDB.Converter.unmarshall(record);
        return unmarshalledRecord.id; // Extract only the id field
      });
      // delete homeWorkUser

      const HomeworkUsersTableName = `HomeworkUsers-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

      await Promise.all([
        deleteItems(HomeworkUsersTableName, homeWorkUserMapResponse), // get ids from homeWorkUserMapResponse
      ]);

      // delete membership

      var membershipParams = {
        ExpressionAttributeValues: {
          ":u": { S: USERID },
        },
        FilterExpression: "#M = :u",
        ExpressionAttributeNames: {
          "#M": "personsID",
        },
        ProjectionExpression: "id",
        TableName:
          "Membership-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      const membershipResponse = await ddb.scan(membershipParams).promise();

      const membershipRecords = membershipResponse.Items;

      const membershipMapResponse = membershipRecords.map((record) => {
        const unmarshalledRecord = AWS.DynamoDB.Converter.unmarshall(record);
        return unmarshalledRecord.id; // Extract only the id field
      });
      // delete comman function membershipMapResponse
      // Submission delete

      await Promise.all([
        deleteItems(
          "Membership-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
          membershipMapResponse
        ),
      ]); // membershipMapResponse change with ids

      // delete points

      var pointsParams = {
        ExpressionAttributeValues: {
          ":u": { S: USERID },
        },
        FilterExpression: "#M = :u",
        ExpressionAttributeNames: {
          "#M": "memberId",
        },
        ProjectionExpression: "id",
        TableName:
          "Points-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      const pointsResponse = await ddb.scan(pointsParams).promise();

      const pointsRecords = pointsResponse.Items;

      const pointsMapResponse = pointsRecords.map((record) => {
        const unmarshalledRecord = AWS.DynamoDB.Converter.unmarshall(record);
        return unmarshalledRecord.id; // Extract only the id field
      });
      // delete comman function pointsMapResponse
      // Submission delete

      await Promise.all([
        deleteItems(
          "Points-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
          pointsMapResponse
        ),
      ]); // pointsMapResponse change with ids

      var assignmentParams = {
        ExpressionAttributeValues: {
          ":u": { S: USERID },
        },
        FilterExpression: "#M = :u",
        ExpressionAttributeNames: {
          "#M": "primaryContact",
        },
        ProjectionExpression: "id",
        TableName:
          "Homework-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      const assignmentResponse = await ddb.scan(assignmentParams).promise();

      const assignmentRecords = assignmentResponse.Items;

      const assignmentMapResponse = assignmentRecords.map((record) => {
        const unmarshalledRecord = AWS.DynamoDB.Converter.unmarshall(record);
        return unmarshalledRecord.id; // Extract only the id field
      });

      const promises = assignmentMapResponse.map(async (id) => {
        const updateAssignmentParams = {
          TableName:
            "Homework-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
          Key: {
            id: { S: id },
          },
          ExpressionAttributeNames: {
            "#PID": "primaryContact",
          },
          ExpressionAttributeValues: {
            ":pid": { S: "" },
          },
          ReturnValues: "ALL_NEW",
          UpdateExpression: "SET #PID = :pid",
        };
        try {
          await ddb.updateItem(updateAssignmentParams).promise();
        } catch (error) {
          console.log(error, error.stack);
        }
      });
      await Promise.all(promises);

      // delete Notifications

      var notificationsParams = {
        ExpressionAttributeValues: {
          ":u": { S: USERID },
        },
        FilterExpression: "#M = :u",
        ExpressionAttributeNames: {
          "#M": "personsID",
        },
        ProjectionExpression: "id",
        TableName:
          "Notifications-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      const notificationsResponse = await ddb
        .scan(notificationsParams)
        .promise();

      const notificationsRecords = notificationsResponse.Items;

      const notificationsMapResponse = notificationsRecords.map((record) => {
        const unmarshalledRecord = AWS.DynamoDB.Converter.unmarshall(record);
        return unmarshalledRecord.id; // Extract only the id field
      });
      // delete comman function notificationsMapResponse
      // Submission delete

      var eventsParams = {
        ExpressionAttributeValues: {
          ":u": { S: USERID },
        },
        FilterExpression: "#M = :u",
        ExpressionAttributeNames: {
          "#M": "createdBy",
        },
        ProjectionExpression: "id",
        TableName:
          "Events-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      const eventsResponse = await ddb.scan(eventsParams).promise();

      const eventsRecords = eventsResponse.Items;

      const eventsMapResponse = eventsRecords.map((record) => {
        const unmarshalledRecord = AWS.DynamoDB.Converter.unmarshall(record);
        return unmarshalledRecord.id; // Extract only the id field
      });

      await Promise.all([
        deleteItems(
          "Events-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
          eventsMapResponse
        ),
      ]); // eventsMapResponse change with ids

      var params = {
        TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        Key: {
          id: { S: USERID },
        },
      };
      const userResponse = await ddb.deleteItem(params).promise();

      if (getUserResponse.cognitoId) {
        console.log("inside if", getUserResponse);
        const cognitoParams = {
          UserPoolId: USERPOOLID,
          Username: getUserResponse.cognitoId,
        };
        console.log("cognitoParams", cognitoParams);
        const cognitoResponse = await cognito
          .adminDeleteUser(cognitoParams)
          .promise();
        console.log("cognitoResponse: ", cognitoResponse);
      }

      // CreateActivity
      let date = new Date();

      var userDeleteActivityParam = {
        Item: {
          id: { S: uuid.v4() },
          __typename: { S: "Activity" },
          activityType: { S: "MEMBERSHIP" ?? "" },
          createdUserId: { S: req.body.createdUserId ?? "" },
          createdUserName: { S: req.body.createdUserName ?? "" },
          cityId: { S: req.body?.cityId ?? "" },
          moduleId: { S: USERID ?? "" },
          moduleImageUrl: { S: getUserResponse.imageUrl ?? "" },
          moduleName: { S: getUserResponse?.name ?? "" },
          moduleType: { S: "stakeholder" },
          relatedId: { S: USERID ?? "" },
          relatedName: { S: getUserResponse?.name ?? "" },
          relatedTo: { S: "stakeholder" ?? "" },
          type: { S: "DELETED" },
          activityTokens: { N: "0" },
          isDeleted: { S: "false" },
          createdAt: { S: date.toISOString() },
          updatedAt: { S: date.toISOString() },
          _version: { N: "1" },
          _lastChangedAt: { N: date.getTime().toString() },
        },
        ReturnConsumedCapacity: "TOTAL",
        TableName:
          "Activity-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      let createActivityData = await ddb
        .putItem(userDeleteActivityParam)
        .promise();
      console.log("createActivityData", createActivityData);

      res.json({ message: "User deleted successfully" });
    } else {
      throw new Error("User not exists.");
    }
  } catch (error) {
    console.log("error", error);
    res.status(error.statusCode).json({ error: error });
  }
});

const deleteItems = async (tableName, ids) => {
  const promises = ids.map(async (id) => {
    const deleteParams = {
      TableName: tableName,
      Key: {
        id: { S: id },
      },
    };
    try {
      await ddb.deleteItem(deleteParams).promise();
    } catch (error) {
      console.log(error, error.stack);
    }
  });
  await Promise.all(promises);
};

/**
 * @deprecated This function is deprecated and may be removed in future versions.
 */
const bulkDelete = async (tableName, items) => {
  const chunks = [];
  while (items.length) {
    chunks.push(items.splice(0, 25));
  }

  for (const chunk of chunks) {
    const deleteRequests = chunk.map((item) => ({
      DeleteRequest: {
        Key: item,
      },
    }));

    const params = {
      RequestItems: {
        [tableName]: deleteRequests,
      },
    };

    try {
      const result = await ddb.batchWrite(params).promise();
      console.log("Batch delete successful:", result);
    } catch (error) {
      console.error("Error during batch delete:", error);
    }
  }
};

/**
 * @deprecated
 * This function is deprecated and may be removed in future versions.
 * It's used for soft delete
 */
app.delete("/userv2", async function (req, res) {
  try {
    const userTable = `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
    const USERID = req.body.id;

    const params = {
      TableName: userTable,
      Key: { id: { S: USERID } },
    };
    const getUserResponse = await ddb.getItem(params).promise();
    const userItem = getUserResponse["Item"]
      ? AWS.DynamoDB.Converter.unmarshall(getUserResponse["Item"])
      : null;

    const updateUserParams = {
      TableName: userTable,
      Key: {
        id: { S: USERID },
      },
      ExpressionAttributeNames: {
        "#ID": "isDeleted",
        "#ST": "status",
        "#SU": "isSignup",
      },
      ExpressionAttributeValues: {
        ":id": { S: "true" },
        ":st": { S: "inActive" },
        ":su": { BOOL: false },
      },
      ReturnValues: "ALL_NEW",
      UpdateExpression: "SET #ID = :id, #ST = :st, #SU = :su",
    };
    let updatedUser = await ddb.updateItem(updateUserParams).promise();
    if (
      updatedUser &&
      userItem &&
      userItem.status === "active" &&
      userItem.cognitoId &&
      userItem.isStakeholder === true
    ) {
      console.log("in if");
      const cognitoParams = {
        UserPoolId: USERPOOLID,
        Username: userItem.cognitoId,
      };
      await cognito.adminDeleteUser(cognitoParams).promise();
    }

    res.json({ message: "User deleted successfully" });
  } catch (error) {
    console.log("error", error);
    res.status(error.statusCode).json({ error: error });
  }
});

app.put("/user", async function (req, res) {
  try {
    let UserAttributes = [];
    let userExist = 0;
    let message = "";
    let statusCode = 200;
    let oldEmail;

    let getUserParams = {
      UserPoolId: USERPOOLID,
      Username: req.body.id, // Typically this would be the "sub" value
    };

    if (req.body.email) {
      const userResponse = await cognito.adminGetUser(getUserParams).promise();
      const emailAttribute = userResponse?.UserAttributes.find(
        (attr) => attr.Name === "email"
      );
      oldEmail = emailAttribute ? emailAttribute.Value : null;

      userExist = 1;
      let listExistUserResponse = "";

      let cognitoUserExistListParams = {
        AttributesToGet: ["name"],
        Filter: 'email = "' + req.body?.email + '"',
        UserPoolId: USERPOOLID,
      };
      listExistUserResponse = await cognito
        .listUsers(cognitoUserExistListParams)
        .promise();
      console.log("listExistUserResponse?.Users", listExistUserResponse?.Users);
      let userList = (listExistUserResponse?.Users).filter((object) => {
        return object?.Username !== req?.body?.id;
      });
      userExist = userList?.length ?? 0;
      console.log("userExist", userExist);
      if (userExist == 0)
        UserAttributes.push(
          { Name: "email", Value: req.body.email },
          { Name: "email_verified", Value: "true" }
        );
      else message = "Email already exist.";
    }
    if (req.body.lastName) {
      UserAttributes.push({ Name: "family_name", Value: req.body.lastName });
    }
    if (req.body.firstName) {
      UserAttributes.push({ Name: "given_name", Value: req.body.firstName });
    }
    if (req.body.phoneNumber) {
      UserAttributes.push(
        { Name: "phone_number", Value: `+1${req.body.phoneNumber}` },
        { Name: "phone_number_verified", Value: "true" }
      );
    }
    if (req.body.name) {
      UserAttributes.push({ Name: "name", Value: req.body.name });
    }
    if (req.body?.role) {
      UserAttributes.push({ Name: "custom:role", Value: req.body?.role });
    }

    // Remove subscriber user from Administrators group
    if (
      (req.body.role && req.body.role === "SUBSCRIBER") ||
      req.body.role === "STAFF_MEMBER" ||
      (req.body.role === "MEMBER" && userExist == 0)
    ) {
      console.log("here");
      const cognitoParams = {
        GroupName: "Administrators",
        UserPoolId: USERPOOLID,
        Username: oldEmail,
      };
      await cognito.adminRemoveUserFromGroup(cognitoParams).promise();
    }

    if (userExist == 0) {
      const params = {
        UserAttributes,
        UserPoolId: USERPOOLID,
        Username: req.body.id,
      };
      const addUserToGroupParams = {
        GroupName: "Administrators",
        UserPoolId: USERPOOLID,
        Username: req.body.email,
      };
      try {
        await cognito.adminUpdateUserAttributes(params).promise();
        if (req.body.role === "SUPER_ADMIN") {
          const addUserToGroupResponse = await cognito
            .adminAddUserToGroup(addUserToGroupParams)
            .promise();
        }
        message = "User data update successfully.";
      } catch (error) {
        console.log("error", error);
        message = error.message;
        statusCode = error.statusCode ? error.statusCode : 500;
      }
    }

    res.status(statusCode).json({ isExist: userExist, data: message });
  } catch (error) {
    console.log("error", error);
    res.json({ error: error });
  }
});

app.put("/userv2", async function (req, res) {
  try {
    let UserAttributes = [];
    let userExist = 0;
    let message = "";
    let statusCode = 200;
    let oldEmail;
    const userTableName = `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

    const getUserDetail = await getUserById(req.body.id);

    if (getUserDetail && getUserDetail.isDeleted === "false") {
      userExist = 1;

      try {
        const updateUserParams = {
          TableName: userTableName,
          Key: {
            id: { S: req.body.id },
          },
          ExpressionAttributeNames: {
            "#RF": "registeredFrom",
            "#FN": "familyName",
            "#GN": "givenName",
            "#PN": "phoneNumber",
            "#EM": "email",
            "#BD": "birthday",
            "#SA1": "streetAddressOne",
            "#SA2": "streetAddressTwo",
            "#CI": "city",
            "#RO": "role",
            "#ST": "state",
            "#ZC": "zipCode",
            "#STC": "stackholderCities",
            "#UT": "userType",
            "#STT": "status",
            "#NA": "name",
            "#G": "gender",
            "#WA": "walletAddress",
            "#IU": "imageUrl",
            "#CC": "countryCode",
          },
          ExpressionAttributeValues: {
            ":rf": { S: req.body.registeredFrom || "WEB" },
            ":fn": { S: req.body.familyName || "" },
            ":gn": { S: req.body.givenName || "" },
            ":pn": { S: `+1${req.body.phoneNumber}` || "" },
            ":em": { S: req.body.email.toLowerCase() || "" },
            ":bd": { S: req.body.birthday || "" },
            ":sa1": { S: req.body.streetAddressOne || "" },
            ":sa2": { S: req.body.streetAddressTwo || "" },
            ":ci": { S: req.body.city || "" },
            ":ro": { S: req.body.role || "MEMBER" },
            ":st": { S: req.body.state || "" },
            ":zc": { S: req.body.zipCode || "" },
            ":stc": {
              L: req.body.stackholderCities?.map((cityId) => ({ S: cityId })),
            },
            ":ut": { S: req.body.userType || "loginUser" },
            ":stt": { S: req.body.status || "active" },
            ":na": { S: `${req.body.givenName} ${req.body.familyName}` },
            ":g": { S: req.body.gender || "NONE" },
            ":wa": { S: req.body.walletAddress || "" },
            ":iu": { S: req.body.imageUrl || "" },
            ":cc": { S: req.body.countryCode || "" },
          },
          ReturnValues: "ALL_NEW",
          UpdateExpression: `SET 
              #RF = :rf, 
              #FN = :fn, 
              #GN = :gn, 
              #PN = :pn, 
              #EM = :em, 
              #BD = :bd, 
              #SA1 = :sa1, 
              #SA2 = :sa2, 
              #CI = :ci, 
              #RO = :ro, 
              #ST = :st, 
              #ZC = :zc, 
              #STC = :stc, 
              #UT = :ut, 
              #STT = :stt, 
              #NA = :na,
              #G = :g,
              #WA = :wa,
              #IU = :iu,
              #CC = :cc`,
        };
        let updatedUser = await ddb.updateItem(updateUserParams).promise();
        // console.log("updatedUser", updatedUser);

        // create activity

        let activityId = uuid.v4();
        const date = new Date();

        const acivityParams = {
          Item: {
            id: { S: activityId },
            __typename: { S: "Activity" },
            type: { S: "UPDATED" },
            moduleId: { S: req.body.id },
            moduleName: { S: req.body.name },
            moduleType: { S: `stakeholder` },
            requestStatus: { S: "SYSTEM_APPROVED" },
            moduleImageUrl: {
              S: req.body?.imageUrl ?? "",
            },
            activityType: {
              S: "MEMBERSHIP",
            },
            cityId: {
              S: req.body.cityId,
            },
            createdUserId: { S: req.body.createdUserId },
            createdUserName: { S: req.body.createdUserName },

            isDeleted: { S: "false" },

            createdAt: { S: date?.toISOString() },
            updatedAt: { S: date?.toISOString() },
            _version: { N: "1" },
            _lastChangedAt: { N: date?.getTime()?.toString() },
          },
          ReturnConsumedCapacity: "TOTAL",
          ReturnValues: "ALL_OLD",
          TableName: `Activity-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };

        let activityRes = await ddb.putItem(acivityParams).promise();
        // console.log("activityRes", activityRes);

        //find and update membership

        const scanParams = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
            ":p": { S: req.body.id },
          },
          FilterExpression: "#D = :d and #P=:p",
          ExpressionAttributeNames: { "#D": "isDeleted", "#P": "personsID" },
          TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };
        const response = await ddb.scan(scanParams).promise();

        const membersResponse = response?.Items?.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );
        // console.log("membersResponse", membersResponse);

        if (membersResponse) {
          const updateMemberParams = {
            TableName: `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
            Key: {
              id: { S: membersResponse[0].id },
            },
            ExpressionAttributeNames: {
              "#NM": "name",
              "#SD": "shortDescription",
              "#IU": "imageUrl",
            },
            ExpressionAttributeValues: {
              ":nm": { S: req.body.name || "" },
              ":sd": { NULL: req.body.shortDescription == null },
              ":iu": { S: req.body.imageUrl || "" },
            },
            ReturnValues: "ALL_NEW",
            UpdateExpression: `SET 
                #NM = :nm, 
                #SD = :sd, 
                #IU = :iu`,
          };

          let updatedMember = await ddb
            .updateItem(updateMemberParams)
            .promise();
          // console.log("updatedMember", updatedMember);
        }

        // update cognito user
        if (
          req.body.cognitoId &&
          req.body.cognitoId != null &&
          req.body.email
        ) {
          let getUserParams = {
            UserPoolId: USERPOOLID,
            Username: req.body.cognitoId, // Typically this would be the "sub" value
          };

          const userResponse = await cognito
            .adminGetUser(getUserParams)
            .promise();
          const emailAttribute = userResponse?.UserAttributes.find(
            (attr) => attr.Name === "email"
          );
          oldEmail = emailAttribute ? emailAttribute.Value : null;
          console.log("oldEmail", oldEmail);
          if (userResponse) {
            if (oldEmail.toLocaleLowerCase() !== req.body.email.toLowerCase()) {
              UserAttributes.push(
                { Name: "email", Value: req.body.email.toLowerCase() },
                { Name: "email_verified", Value: "true" }
              );
            }
            UserAttributes.push({
              Name: "family_name",
              Value: req.body.familyName,
            });
            UserAttributes.push({
              Name: "given_name",
              Value: req.body.givenName,
            });
            UserAttributes.push(
              { Name: "phone_number", Value: `+1${req.body.phoneNumber}` },
              { Name: "phone_number_verified", Value: "true" }
            );
            UserAttributes.push({ Name: "name", Value: req.body.name });
            UserAttributes.push({ Name: "custom:role", Value: req.body?.role });

            if (
              (req.body.role && req.body.role === "SUBSCRIBER") ||
              req.body.role === "STAFF_MEMBER" ||
              (req.body.role === "MEMBER" && userExist == 0)
            ) {
              console.log("here");
              const cognitoParams = {
                GroupName: "Administrators",
                UserPoolId: USERPOOLID,
                Username: oldEmail.toLowerCase(),
              };
              await cognito.adminRemoveUserFromGroup(cognitoParams).promise();
            }

            const params = {
              UserAttributes,
              UserPoolId: USERPOOLID,
              Username: req.body.cognitoId,
            };
            const addUserToGroupParams = {
              GroupName: "Administrators",
              UserPoolId: USERPOOLID,
              Username: req.body.email,
            };
            await cognito.adminUpdateUserAttributes(params).promise();
            if (req.body.role === "SUPER_ADMIN") {
              const addUserToGroupResponse = await cognito
                .adminAddUserToGroup(addUserToGroupParams)
                .promise();
            }
          }
        }

        message = "User data update successfully.";
      } catch (error) {
        console.log("error", error);
        message = error.message;
        statusCode = error.statusCode ? error.statusCode : 500;
      }
    } else {
      throw new Error("User not exists.");
    }

    res.status(statusCode).json({ isExist: userExist, data: message });
  } catch (error) {
    console.log("error", error);
    const statusCode = error.statusCode || 500;

    // Ensure error message is sent in the response
    const errorMessage = error.message || "An unexpected error occurred";
    res.status(statusCode).json({ error: errorMessage });
  }
});

app.post("/userConfirm", async function (req, res) {
  try {
    const USERNAME = req.body.username;
    const cognitoParams = {
      UserPoolId: USERPOOLID,
      Username: USERNAME,
    };

    const params = {
      UserAttributes: [
        {
          Name: "email_verified",
          Value: "true",
        },
        {
          Name: "phone_number_verified",
          Value: "true",
        },
      ],
      ...cognitoParams,
    };

    const response = await cognito.adminConfirmSignUp(cognitoParams).promise();
    const updateResponse = await cognito
      .adminUpdateUserAttributes(params)
      .promise();

    res.status(200).json({ data: response, updateData: updateResponse });
  } catch (error) {
    res.status(error.statusCode).json({ error: error });
  }
});

// Check phoone number exist or not
app.post("/checkPhoneNumberExist", async function (req, res) {
  try {
    const phoneNumber = req.body?.phoneNumber;
    console.log("phoneNumber", phoneNumber);
    let isPhoneNumberExist = 0;

    if (phoneNumber) {
      const cognitoPhoneNumberParams = {
        AttributesToGet: ["phone_number"],
        Filter: `phone_number = "+1${phoneNumber}"`,
        UserPoolId: USERPOOLID,
      };

      const listExistPhoneNumberResponse = await cognito
        .listUsers(cognitoPhoneNumberParams)
        .promise();
      isPhoneNumberExist = listExistPhoneNumberResponse?.Users?.length || 0;
    }

    const message = isPhoneNumberExist
      ? "Phone number already exists."
      : "No data found.";
    const checkPhoneNumberExistResponse = {
      isExist: isPhoneNumberExist ? 1 : 0,
      message,
    };
    res.json({ data: checkPhoneNumberExistResponse });
  } catch (error) {
    res.status(error.statusCode).json({ error: error });
  }
});

// Push notification
app.post("/publishNotification", async function (req, res) {
  try {
    const title = req.body.title;
    const body = req.body.body;
    const userList = req.body.userList;
    const sns = new AWS.SNS();
    const notificationData = [];
    const TableName =
      "Notifications-" +
      process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
      "-" +
      process.env.ENV;
    let date = new Date();

    await userList.map(async (rec) => {
      if (rec.id && rec.endpointArn) {
        let notificationId = uuid.v4();
        notificationData.push({
          PutRequest: {
            Item: {
              id: {
                S: notificationId,
              },
              __typename: { S: "Notifications" },
              title: {
                S: title,
              },
              description: {
                S: body,
              },
              userId: {
                S: rec.id,
              },
              imageUrl: {
                S: "",
              },
              isRead: {
                BOOL: false,
              },
              isDeleted: {
                S: "false",
              },
              createdAt: { S: date.toISOString() },
              updatedAt: { S: date.toISOString() },
              _version: { N: "1" },
              _lastChangedAt: { N: date.getTime().toString() },
            },
          },
        });
      }
    });

    if (notificationData && notificationData.length) {
      let notificationParams = {
        RequestItems: {
          [TableName]: notificationData,
        },
      };
      await ddb.batchWriteItem(notificationParams).promise();
    }

    //send push
    await userList.map(async (rec) => {
      var device_arn = rec.endpointArn;

      var updateARNParams = {
        Attributes: {
          Enabled: "true",
        },
        EndpointArn: device_arn,
      };
      await sns.setEndpointAttributes(updateARNParams).promise();

      var payload = {
        GCM: {
          notification: {
            body: body,
            title: title,
          },
        },
      };

      payload.GCM = JSON.stringify(payload.GCM);
      payload = JSON.stringify(payload);

      await sns
        .publish({
          Message: payload,
          MessageStructure: "json",
          TargetArn: device_arn,
        })
        .promise();
    });

    res.status(200).json({ data: "Message sent successfully" });
  } catch (error) {
    if (error.statusCode) res.status(error.statusCode).json({ error: error });
    else res.status(500).json({ error: error });
  }
});

// Membership management
app.post("/membership", async function (req, res) {
  try {
    let params = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":i": { S: "" },
        ":s": { BOOL: true },
      },
      FilterExpression:
        "#D = :d and #S = :s and #I <> :i and attribute_exists(imageUrl) and #DELETE <> :s",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#I": "imageUrl",
        "#S": "status",
        "#NAME": "name",
        "#STATUS": "status",
        "#DELETE": "_deleted",
      },
      ProjectionExpression:
        "id, imageUrl, #NAME, createdAt, isDeleted, #STATUS",
      TableName: `Programs-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };
    const response = await ddb.scan(params).promise();
    const membersResponse = response.Items.map((records) =>
      AWS.DynamoDB.Converter.unmarshall(records)
    );
    console.log("membersResponse", membersResponse);
    // let membersResponse = membersResponse.filter(ex=>ex._deleted !== true);
    membersResponse.sort(
      (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
    );

    res.status(200).json({
      success: true,
      message: "Get micro credentials data successfully.",
      data: membersResponse,
    });
  } catch (error) {
    const statusCode = error.statusCode || 500;
    res.status(statusCode).json({ error });
  }
});

// Delete records from tables
app.delete("/deleteRecord", async function (req, res) {
  try {
    const id = req.body.id;
    const sectionName = req.body.sectionName;

    if (sectionName == "Organizations") {
      // Points data delete query
      var pointsParams = {
        ExpressionAttributeValues: {
          ":m": { S: id },
          ":t": { S: "member" },
        },
        FilterExpression: "#M = :m and #T = :t",
        ExpressionAttributeNames: {
          "#M": "memberId",
          "#T": "type",
        },
        ProjectionExpression: "id",
        TableName:
          "Points-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      const pointsResponse = await ddb.scan(pointsParams).promise();
      const newPointsResponse = pointsResponse.Items.map((records) =>
        AWS.DynamoDB.Converter.unmarshall(records)
      );

      await newPointsResponse.map(async (rec) => {
        var params = {
          TableName:
            "Points-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
          Key: {
            id: { S: rec.id },
          },
        };
        await ddb.deleteItem(params).promise();
      });

      // Association delete query
      var associationParams = {
        ExpressionAttributeValues: {
          ":o": { S: id },
        },
        FilterExpression: "#O = :o",
        ExpressionAttributeNames: {
          "#O": "organizationID",
        },
        ProjectionExpression: "id",
        TableName:
          "Association-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      const response = await ddb.scan(associationParams).promise();
      const newResponse = response.Items.map((records) =>
        AWS.DynamoDB.Converter.unmarshall(records)
      );

      await newResponse.map(async (rec) => {
        var params = {
          TableName:
            "Association-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
          Key: {
            id: { S: rec.id },
          },
        };
        await ddb.deleteItem(params).promise();
      });

      // Post delete query
      var postParams = {
        ExpressionAttributeValues: {
          ":u": { S: id },
        },
        FilterExpression: "#M = :u",
        ExpressionAttributeNames: {
          "#M": "memberId",
        },
        ProjectionExpression: "id",
        TableName:
          "Submission-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      const postResponse = await ddb.scan(postParams).promise();

      const postRecords = postResponse.Items;

      const postMapResponse = postRecords.map((postRecords) =>
        AWS.DynamoDB.Converter.unmarshall(postRecords)
      );

      await postMapResponse.map(async (rec) => {
        var params = {
          TableName:
            "Submission-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
          Key: {
            id: { S: rec.id },
          },
        };
        await ddb.deleteItem(params).promise();
      });

      res.json({
        data: {
          statusCode: 200,
          message: "Delete record succesfully.",
        },
      });
    } else if (sectionName == "Business") {
      // Association delete query
      var associationParams = {
        ExpressionAttributeValues: {
          ":b": { S: id },
        },
        FilterExpression: "#B = :b",
        ExpressionAttributeNames: {
          "#B": "businessID",
        },
        ProjectionExpression: "id",
        TableName:
          "Association-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      const response = await ddb.scan(associationParams).promise();

      const records = response.Items;

      const newResponse = records.map((records) =>
        AWS.DynamoDB.Converter.unmarshall(records)
      );

      await newResponse.map(async (rec) => {
        var params = {
          TableName:
            "Association-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
          Key: {
            id: { S: rec.id },
          },
        };
        await ddb.deleteItem(params).promise();
      });

      // Post delete query
      var postParams = {
        ExpressionAttributeValues: {
          ":u": { S: id },
        },
        FilterExpression: "#M = :u",
        ExpressionAttributeNames: {
          "#M": "memberId",
        },
        ProjectionExpression: "id",
        TableName:
          "Submission-" +
          process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
          "-" +
          process.env.ENV,
      };
      const postResponse = await ddb.scan(postParams).promise();

      const postRecords = postResponse.Items;

      const postMapResponse = postRecords.map((postRecords) =>
        AWS.DynamoDB.Converter.unmarshall(postRecords)
      );

      await postMapResponse.map(async (rec) => {
        var params = {
          TableName:
            "Submission-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
          Key: {
            id: { S: rec.id },
          },
        };
        await ddb.deleteItem(params).promise();
      });

      res.json({
        data: {
          statusCode: 200,
          message: "Delete record succesfully.",
        },
      });
    }
  } catch (error) {
    if (error.statusCode) res.status(error.statusCode).json({ error: error });
    else res.status(500).json({ error: error });
  }
});

// Get user detail using device token
app.post("/userLoginDetail", async function (req, res) {
  try {
    const userId = req.body.userId;
    var userLoginDetailParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":id": { S: userId },
      },
      FilterExpression: "#D = :d and #ID = :id",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#ID": "id",
      },
      TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };

    const userLoginDetail = await ddb.scan(userLoginDetailParams).promise();
    const userLoginDetailRes = userLoginDetail.Items.map((records) =>
      AWS.DynamoDB.Converter.unmarshall(records)
    );

    res.json({ data: userLoginDetailRes });
  } catch (error) {
    res.status(error.statusCode).json({ error: error });
  }
});

// Webhook
app.post(
  "/webhook",
  bodyParser.raw({ type: "application/json" }),
  async (request, response) => {
    let event;
    try {
      const sig = request.headers["stripe-signature"];
      event = stripe.webhooks.constructEvent(request.body, sig, endpointSecret);
    } catch (err) {
      // On error, log and return the error message
      console.log(`❌ Error message: ${err.message}`);
      return response.status(500).send(`Webhook Error: ${err.message}`);
    }

    // Handle the event
    switch (event.type) {
      case "invoice.created":
        const invoiceObject = event.data.object;
        console.log(`Invoice intent ${invoiceObject["id"]} ${event}  created`);
        await invoicesList(invoiceObject);
        break;
      default:
        console.log(`Unhandled event type ${event.type}.`);
    }

    // Return a 200 response to acknowledge receipt of the event
    response.send();
  }
);

async function invoicesList(invoiceObject) {
  console.log("invoiceObject", invoiceObject);
  var invoiceCityId =
    invoiceObject?.subscription_details?.metadata?.city_id ??
    "8f6c4c07-dd62-480d-a376-ef221133f0e2";
  console.log("invoiceCityId", invoiceCityId);

  const transactionsParams = {
    ExpressionAttributeValues: {
      ":d": { S: "false" },
      ":c": { S: invoiceCityId },
    },
    FilterExpression: "#D = :d and #C = :c",
    ExpressionAttributeNames: {
      "#D": "isDeleted",
      "#C": "cityId",
    },
    TableName: `CityFundTransactions-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
  };

  const transactionsResponse = await ddb.scan(transactionsParams).promise();
  const sortedTransactions = transactionsResponse.Items.map((records) =>
    AWS.DynamoDB.Converter.unmarshall(records)
  ).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

  // Now sortedTransactions will contain items sorted by timestamp in descending order
  const latestRecord = sortedTransactions[0];
  console.log("latestRecord", latestRecord);

  var previousAmount = latestRecord?.currentAmount ?? 0;
  var currentAmount =
    parseFloat(previousAmount) + parseFloat(invoiceObject["total"]) / 100 ?? 0;
  var productName = invoiceObject?.subscription_details?.metadata?.product_name;
  var memberId = invoiceObject?.subscription_details?.metadata?.member_id;
  var memberType = invoiceObject?.subscription_details?.metadata?.member_type;
  var userId = invoiceObject?.subscription_details?.metadata?.user_id;

  if (invoiceObject["id"] != null || invoiceObject["id"] != undefined) {
    const invoice = await stripe.invoices.update(invoiceObject["id"], {
      metadata: {
        previous_amount: `${previousAmount}`,
        current_amount: `${currentAmount}`,
        product_name: `${productName}`,
        member_id: `${memberId}`,
        member_type: `${memberType}`,
        user_id: `${userId}`,
      },
    });
    console.log("Update invoice", invoice);
    const invoiceData = await createInvoiceInDynamoDB(invoice);
    const resData = await assignTokenToMember(invoice);
    console.log("resData", resData);
  }
}

const createInvoiceInDynamoDB = async (invoice) => {
  const date = new Date();
  const params = {
    Item: {
      id: { S: uuid.v4() },
      __typename: { S: "CityFundTransactions" },
      name: { S: invoice?.customer ?? "" },
      productName: { S: invoice?.metadata?.product_name ?? "" },
      invoiceId: { S: invoice?.id ?? "" },
      // memberId: { S: invoice?.metadata?.member_id ?? "" },
      type: { S: "subscription" },
      customerId: { S: invoice?.customer ?? "" },
      invoiceDatetime: { S: date.toISOString() },
      cityId: {
        S:
          invoice?.subscription_details?.metadata?.city_id ??
          "8f6c4c07-dd62-480d-a376-ef221133f0e2",
      },
      previousAmount: { N: invoice?.metadata?.previous_amount ?? 0 },
      currentAmount: { N: invoice?.metadata?.current_amount ?? 0 },
      amount: { N: (invoice?.total / 100).toString() ?? 0 },
      recurring: { BOOL: true },
      isDeleted: { S: "false" },
      subscriptionId: { S: invoice?.subscription ?? "" },
      amountStatus: { S: "CREDITED" ?? "" },
      createdAt: { S: date.toISOString() },
      updatedAt: { S: date.toISOString() },
      _version: { N: "1" },
      _lastChangedAt: { N: date.getTime().toString() },
    },
    ReturnConsumedCapacity: "TOTAL",
    TableName: `CityFundTransactions-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
  };

  const userResponse = await ddb.putItem(params).promise();
  console.log("userResponse", userResponse);
  return userResponse;
};

const assignTokenToMember = async (invoice) => {
  const memberId = invoice?.metadata?.user_id;
  const memberType = invoice?.metadata?.member_type ?? "User";
  const membershipTable = `Membership-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

  // Get Membership Data
  const params = {
    ExpressionAttributeValues: {
      ":d": { S: "false" },
      ":t": { S: "User" },
      ":mi": { S: memberId },
    },
    FilterExpression: "#D = :d and #T = :t and #P = :mi",
    ExpressionAttributeNames: {
      "#D": "isDeleted",
      "#T": "type",
      "#P": "personsID",
      "#O": "organizationID",
      "#N": "name",
    },
    ProjectionExpression:
      "id, #N, #P, #O, MVPTokens, currentImpactScore, fundTokens, cityId, #T",
    TableName: membershipTable,
  };
  const membershipResponse = await ddb.scan(params).promise();
  const membershipRes = membershipResponse.Items.map((records) =>
    AWS.DynamoDB.Converter.unmarshall(records)
  );
  const member = membershipRes[0] ?? [];
  let oldFundTokens = member?.fundTokens ?? 0;
  let currentToken = (invoice?.total / 100).toFixed(2) ?? 0;
  let currentFundTokens =
    parseFloat(parseFloat(oldFundTokens) + currentToken).toFixed(2) ?? 0;
  console.log("member", member);

  if (member) {
    const updateMemberParams = {
      TableName: membershipTable,
      Key: {
        id: { S: member.id },
      },
      ExpressionAttributeNames: {
        "#FT": "fundTokens",
      },
      ExpressionAttributeValues: {
        ":ft": { N: currentFundTokens.toString() },
      },
      ReturnValues: "ALL_NEW",
      UpdateExpression: "SET #FT = :ft",
    };
    let updatedMember = await ddb.updateItem(updateMemberParams).promise();
  }

  return updatedMember ?? [];
};

// Send SMS
app.post("/sendVerificationCode", async function (req, res) {
  try {
    let verificationCode;
    const {
      memberCode = "",
      email = "",
      phoneNumber = "",
      password = "",
    } = req.body;
    console.log("req.body", req.body);
    let message = "";
    let isSuccess = false;
    const userTableName = `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

    const userParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":s": { BOOL: true },
      },
      FilterExpression: "#D = :d and #S = :s",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#S": "isStakeholder",
        "#N": "name",
      },
      ProjectionExpression: "id, memberCode, #N, email, phoneNumberVerified",
      TableName: userTableName,
    };
    const userDetail = await ddb.scan(userParams).promise();
    const userDetailRes = userDetail.Items.map((records) =>
      AWS.DynamoDB.Converter.unmarshall(records)
    );

    const existingUser =
      userDetailRes.find(
        (inner) =>
          Number(inner.memberCode) === Number(memberCode) &&
          inner.email.toLowerCase() === email.toLowerCase()
      ) || "";
    const userId = existingUser?.id ?? "";
    console.log("existingUser", existingUser);

    if (existingUser && existingUser?.phoneNumberVerified !== true) {
      verificationCode = await sendVerificationCode(
        phoneNumber,
        userId,
        userTableName
      );
      message = "Verification code send successfully.";
      isSuccess = true;
    } else {
      message = "Member code and Email does not matched.";
      isSuccess = false;
    }

    if (existingUser && existingUser?.phoneNumberVerified === true) {
      message = "User already exists.";
      isSuccess = false;
    }

    const response = {
      success: isSuccess,
      message: message,
      data: {
        isExist: !!existingUser,
        userId: userId ?? "",
        verificationCode,
      },
    };

    res.status(200).json(response);
  } catch (error) {
    const statusCode = error.statusCode || 500;
    res.status(statusCode).json({ error: error });
  }
});

async function sendVerificationCode(phoneNumber, userId, userTableName) {
  // Implementation for sending verification code via SNS
  let verificationCode = Math.floor(100000 + Math.random() * 900000);
  let codeExpiration = Date.now() + 3 * 60 * 1000;
  let originationNumber = phoneNumber ? phoneNumber.replace("+", "") : "";
  console.log("originationNumber", originationNumber);
  console.log("verificationCode", verificationCode);

  // Create publish parameters
  var params = {
    Message: `This is your One Time Password: ${verificationCode}` /* required */,
    PhoneNumber: `+1${phoneNumber}`,
    MessageAttributes: {
      "AWS.SNS.SMS.SMSType": {
        DataType: "String",
        StringValue: "Promotional",
      },
      "AWS.MM.SMS.OriginationNumber": {
        DataType: "String",
        StringValue: originationNumber,
      },
    },
  };
  console.log("params", params);

  // Create promise and SNS service object
  const publishTextPromise = await sns.publish(params).promise();
  console.log("MessageID is " + publishTextPromise?.MessageId);
  console.log("publishTextPromise", publishTextPromise);

  // Update user after code send
  const updateUserParams = {
    TableName: userTableName,
    Key: {
      id: { S: userId },
    },
    ExpressionAttributeNames: {
      "#VC": "verificationCode",
      "#CE": "codeExpiration",
      // "#PV": "phoneNumberVerified",
    },
    ExpressionAttributeValues: {
      ":vc": { S: verificationCode.toString() },
      ":ce": { N: codeExpiration.toString() },
      // ":pv": { BOOL: false },
    },
    ReturnValues: "ALL_NEW",
    UpdateExpression: "SET #VC = :vc, #CE = :ce",
  };
  let updatedUser = await ddb.updateItem(updateUserParams).promise();
  return verificationCode;
  // console.log('updatedUser', updatedUser)
}

// Verify code
app.post("/verifyCode", async function (req, res) {
  try {
    const { verificationCode = "", userId = "", password = "" } = req.body;
    let phoneNumber = req.body?.phoneNumber ?? "";
    console.log("req.body", req.body);
    let message = "";
    let isSuccess = false;
    const userTableName = `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

    const params = {
      TableName: userTableName,
      Key: { id: { S: userId } },
    };
    const getUserResponse = await ddb.getItem(params).promise();
    const userItem = getUserResponse["Item"]
      ? AWS.DynamoDB.Converter.unmarshall(getUserResponse["Item"])
      : null;
    console.log("userItem", userItem);

    if (userItem) {
      console.log("verificationCode", verificationCode);
      if (userItem?.verificationCode == verificationCode) {
        if (userItem?.codeExpiration > Date.now()) {
          console.log("userItem?.verificationCode", userItem?.verificationCode);
          // Update cognito user after verification
          phoneNumber = phoneNumber ? phoneNumber.replace(/-/g, "") : "";
          phoneNumber = phoneNumber ? phoneNumber.replace(/\s/g, "") : "";
          console.log("phoneNumber", phoneNumber);
          const params = {
            UserAttributes: [
              { Name: "phone_number", Value: `+1${phoneNumber}` },
              { Name: "phone_number_verified", Value: "true" },
            ],
            UserPoolId: USERPOOLID,
            Username: userId,
          };
          await cognito.adminUpdateUserAttributes(params).promise();
          phoneNumber = phoneNumber ? phoneNumber.replace("+91", "") : "";
          phoneNumber = phoneNumber ? phoneNumber.replace("+1", "") : "";
          console.log("phoneNumber DB", phoneNumber);

          // Change cognito user password
          const userPasswordParam = {
            UserPoolId: USERPOOLID,
            Username: userId,
            Password: password,
            Permanent: true,
          };
          await cognito.adminSetUserPassword(userPasswordParam).promise();

          // Update user after verification
          const updateUserParams = {
            TableName: userTableName,
            Key: {
              id: { S: userId },
            },
            ExpressionAttributeNames: {
              "#RF": "registeredFrom",
              "#CE": "codeExpiration",
              "#PV": "phoneNumberVerified",
              "#PN": "phoneNumber",
            },
            ExpressionAttributeValues: {
              ":rf": { S: "MOBILE" },
              ":pv": { BOOL: true },
              ":pn": { S: `+1${phoneNumber}` },
            },
            ReturnValues: "ALL_NEW",
            UpdateExpression: "SET #RF = :rf, #PV = :pv, #PN = :pn REMOVE #CE",
          };
          let updatedUser = await ddb.updateItem(updateUserParams).promise();
          console.log("updatedUser", updatedUser);

          isSuccess = true;
          message = "Phone number verify successfully.";
        } else {
          isSuccess = false;
          message = "Your verification code has expired.";
        }
      } else {
        isSuccess = false;
        message = "Your verification code does not match.";
      }
    } else {
      isSuccess = false;
      message = "User not found.";
    }

    const response = {
      success: isSuccess,
      message: message,
      data: {},
    };

    res.status(200).json(response);
  } catch (error) {
    const statusCode = error.statusCode || 500;
    res.status(statusCode).json({ error: error });
  }
});
/**
 * @deprecated This route is deprecated and may be removed in future versions.
 * It is recommended to use /verifyUserDefaultV2 instead.
 */
app.post("/verifyUserDefault", async function (req, res) {
  try {
    const {
      memberCode = "",
      email = "",
      verificationCode = "",
      userId = "",
      password = "",
    } = req.body;
    let phoneNumber = req.body?.phoneNumber ?? "";
    console.log("req.body", req.body);
    let message = "";
    let isSuccess = false;
    const userTableName = `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

    const userParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":s": { BOOL: true },
      },
      FilterExpression: "#D = :d and #S = :s",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#S": "isStakeholder",
        "#N": "name",
      },
      ProjectionExpression: "id, memberCode, #N, email, phoneNumberVerified",
      TableName: userTableName,
    };
    const userDetail = await ddb.scan(userParams).promise();
    const userDetailRes = userDetail.Items.map((records) =>
      AWS.DynamoDB.Converter.unmarshall(records)
    );

    const existingUser =
      userDetailRes.find(
        (inner) =>
          Number(inner.memberCode) === Number(memberCode) &&
          inner.email.toLowerCase() === email.toLowerCase()
      ) || "";

    // const params = {
    //   TableName: userTableName,
    //   Key: { id: { S: userId } },
    // };
    // const getUserResponse = await ddb.getItem(params).promise();
    // const userItem = getUserResponse["Item"]
    //   ? AWS.DynamoDB.Converter.unmarshall(getUserResponse["Item"])
    //   : null;
    // const existingUser = (
    //   Number(userItem.memberCode) === Number(memberCode) &&
    //   userItem.email.toLowerCase() === email.toLowerCase()
    // ) ? true : false;
    console.log("existingUser", existingUser);
    if (existingUser && existingUser?.phoneNumberVerified !== true) {
      // if (userItem) {
      phoneNumber = phoneNumber ? phoneNumber.replace(/-/g, "") : "";
      phoneNumber = phoneNumber ? phoneNumber.replace(/\s/g, "") : "";
      console.log("phoneNumber", phoneNumber);
      const params = {
        UserAttributes: [
          { Name: "phone_number", Value: `+1${phoneNumber}` },
          { Name: "phone_number_verified", Value: "true" },
        ],
        UserPoolId: USERPOOLID,
        Username: existingUser?.id,
      };
      await cognito.adminUpdateUserAttributes(params).promise();
      phoneNumber = phoneNumber ? phoneNumber.replace("+91", "") : "";
      phoneNumber = phoneNumber ? phoneNumber.replace("+1", "") : "";
      console.log("phoneNumber DB", phoneNumber);

      // Change cognito user password
      const userPasswordParam = {
        UserPoolId: USERPOOLID,
        Username: existingUser?.id,
        Password: password,
        Permanent: true,
      };
      await cognito.adminSetUserPassword(userPasswordParam).promise();

      // Update user after verification
      const updateUserParams = {
        TableName: userTableName,
        Key: {
          id: { S: existingUser?.id },
        },
        ExpressionAttributeNames: {
          "#RF": "registeredFrom",
          "#CE": "codeExpiration",
          "#PV": "phoneNumberVerified",
          "#PN": "phoneNumber",
        },
        ExpressionAttributeValues: {
          ":rf": { S: "MOBILE" },
          ":pv": { BOOL: true },
          ":pn": { S: `+1${phoneNumber}` },
        },
        ReturnValues: "ALL_NEW",
        UpdateExpression: "SET #RF = :rf, #PV = :pv, #PN = :pn REMOVE #CE",
      };
      let updatedUser = await ddb.updateItem(updateUserParams).promise();
      console.log("updatedUser", updatedUser);

      isSuccess = true;
      message = "Signed Up Successfully.";
      // } else {
      //   isSuccess = false;
      //   message = "User not found.";
      // }
    } else {
      console.log("existingUser", existingUser);
      if (!existingUser) {
        message = "User does not exist.";
        isSuccess = false;
      }
      if (existingUser && existingUser?.phoneNumberVerified === true) {
        message = "User already exists.";
        isSuccess = false;
      }
    }

    const response = {
      success: isSuccess,
      message: message,
      data: {},
    };

    res.status(200).json(response);
  } catch (error) {
    const statusCode = error.statusCode || 500;
    res.status(statusCode).json({ error: error });
  }
});

/**
 * Used for signup in mobile
 * */
app.post("/verifyUserDefaultV2", async function (req, res) {
  try {
    const checkUserExistParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":e": { S: req.body.email.toLowerCase() },
        ":m": { S: req.body.memberCode.toString() },
      },
      FilterExpression: "#D = :d and #E = :e and #M = :m",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#E": "email",
        "#M": "memberCode",
      },
      TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };

    const checkUserExistParamsRes = await ddb
      .scan(checkUserExistParams)
      .promise();
    let records = checkUserExistParamsRes.Items.map((item) =>
      AWS.DynamoDB.Converter.unmarshall(item)
    );

    const checkUserExistParamsPhone = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":ph": { S: `+1${req.body?.phoneNumber}` },
        ":e": { S: req.body.email.toLowerCase() },
      },
      FilterExpression: "#D = :d and #PH = :ph and #E <> :e",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#PH": "phoneNumber",
        "#E": "email",
      },
      TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };

    const checkUserExistParamPhonesRes = await ddb
      .scan(checkUserExistParamsPhone)
      .promise();
    let recordsPhone = checkUserExistParamPhonesRes.Items.map((item) =>
      AWS.DynamoDB.Converter.unmarshall(item)
    );

    if (records && records.length > 0 && recordsPhone.length == 0) {
      let firstRecordObject = { firstRecord: records[0] };
      const cognitoUserExistListParams = {
        AttributesToGet: ["name"],
        Filter: `email = "${req.body?.email}"`,
        UserPoolId: USERPOOLID,
      };

      const listExistUserResponse = await cognito
        .listUsers(cognitoUserExistListParams)
        .promise();
      let userExist = listExistUserResponse?.Users?.length || 0;
      console.log("userExist", userExist);
      console.log("firstRecordObject", firstRecordObject.firstRecord.isSignup);
      if (
        (firstRecordObject.firstRecord.isSignup == false ||
          firstRecordObject.firstRecord.isSignup == undefined) &&
        userExist === 0
      ) {
        // create user in cognito

        const cognitoParams = {
          UserPoolId: USERPOOLID,
          Username: req.body?.email.toLowerCase(),
          ForceAliasCreation: true,
          TemporaryPassword: req.body?.password, // Set initial password here
          UserAttributes: [
            { Name: "email", Value: req.body?.email.toLowerCase() },
            { Name: "email_verified", Value: "true" },
            { Name: "phone_number_verified", Value: "true" },
            {
              Name: "given_name",
              Value: firstRecordObject?.firstRecord.givenName,
            },
            {
              Name: "family_name",
              Value: firstRecordObject?.firstRecord.familyName,
            },
            { Name: "phone_number", Value: `+1${req.body?.phoneNumber}` },
            { Name: "name", Value: firstRecordObject?.firstRecord.name },
            { Name: "custom:role", Value: firstRecordObject?.firstRecord.role },
            {
              Name: "custom:registeredFrom",
              Value: req.body?.registeredFrom ?? "MOBILE",
            },
            {
              Name: "custom:dynamodbId",
              Value: firstRecordObject?.firstRecord.id,
            },
          ],
        };

        // Create user with temporary password
        const response = await cognito.adminCreateUser(cognitoParams).promise();

        console.log("response", response);

        // Optionally set the password as permanent
        await cognito
          .adminSetUserPassword({
            UserPoolId: USERPOOLID,
            Username: req.body?.email.toLowerCase(),
            Password: req.body?.password,
            Permanent: true,
          })
          .promise();
        if (firstRecordObject.firstRecord.role === "SUPER_ADMIN") {
          const addUserToGroupParams = {
            GroupName: "Administrators",
            UserPoolId: USERPOOLID,
            Username: req.body?.email,
          };
          const addUserToGroupResponse = await cognito
            .adminAddUserToGroup(addUserToGroupParams)
            .promise();
        }

        console.log(
          "firstRecordObject?.firstRecord?.id",
          firstRecordObject?.firstRecord?.id
        );

        const updateUserParams = {
          TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          Key: {
            id: { S: firstRecordObject?.firstRecord?.id },
          },
          ExpressionAttributeNames: {
            "#RF": "registeredFrom",
            "#CE": "codeExpiration",
            "#PV": "phoneNumberVerified",
            "#PN": "phoneNumber",
            "#IS": "isSignup",
            "#CI": "cognitoId",
            "#ST": "status",
          },
          ExpressionAttributeValues: {
            ":rf": { S: req.body?.registeredFrom ?? "MOBILE" },
            ":pv": { BOOL: true },
            ":pn": { S: `+1${req.body?.phoneNumber}` },
            ":is": { BOOL: true },
            ":ci": { S: response.User.Username },
            ":st": { S: "active" },
          },
          ReturnValues: "ALL_NEW",
          UpdateExpression:
            "SET #RF = :rf, #PV = :pv, #PN = :pn, #IS = :is, #CI = :ci, #ST = :st REMOVE #CE",
        };
        let updatedUser = await ddb.updateItem(updateUserParams).promise();
        console.log("updatedUser", updatedUser);

        // add activity
        let activityId = uuid.v4();
        const date = new Date();

        const acivityParams = {
          Item: {
            id: { S: activityId },
            __typename: { S: "Activity" },
            type: { S: "CREATED" },
            moduleId: { S: firstRecordObject?.firstRecord?.id },
            moduleName: { S: firstRecordObject?.firstRecord?.name },
            moduleType: { S: `stakeholder` },
            requestStatus: { S: "SYSTEM_APPROVED" },
            moduleImageUrl: {
              S: firstRecordObject?.firstRecord?.imageUrl ?? "",
            },
            activityType: {
              S: "MEMBERSHIP",
            },
            cityId: {
              S: firstRecordObject?.firstRecord?.cityId,
            },
            createdUserId: { S: firstRecordObject?.firstRecord?.id },
            createdUserName: { S: firstRecordObject?.firstRecord?.name },

            isDeleted: { S: "false" },

            createdAt: { S: date.toISOString() },
            updatedAt: { S: date.toISOString() },
            _version: { N: "1" },
            _lastChangedAt: { N: date.getTime().toString() },
          },
          ReturnConsumedCapacity: "TOTAL",
          ReturnValues: "ALL_OLD",
          TableName: `Activity-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };

        let activityRes = await ddb.putItem(acivityParams).promise();
        console.log("activityRes", activityRes);
        // add channel

        const createChannelParams = {
          Item: {
            id: { S: firstRecordObject?.firstRecord?.id },
            __typename: { S: "Channel" },
            name: { S: firstRecordObject?.firstRecord?.id },
            createdAt: { S: date.toISOString() },
            updatedAt: { S: date.toISOString() },
            _version: { N: "0" },
            _lastChangedAt: { N: date.getTime().toString() },
          },
          TableName: `Channel-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };

        let createChannel = await ddb.putItem(createChannelParams).promise();
        console.log("createChannel", createChannel);

        // update associations
        var associationParams = {
          ExpressionAttributeValues: {
            ":u": { S: firstRecordObject?.firstRecord?.id },
            ":op": { S: firstRecordObject?.firstRecord?.id },
          },
          FilterExpression: "#P = :u or #OP = :op",
          ExpressionAttributeNames: {
            "#P": "personsID",
            "#OP": "otherPersonsID",
          },
          ProjectionExpression: "id",
          TableName:
            "Association-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        const responseAssociation = await ddb.scan(associationParams).promise();

        const records = responseAssociation.Items;

        const newResponse = records.map((records) =>
          AWS.DynamoDB.Converter.unmarshall(records)
        );

        newResponse.map(async (rec) => {
          var params = {
            TableName:
              "Association-" +
              process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
              "-" +
              process.env.ENV,
            Key: {
              id: { S: rec.id },
            },
            ExpressionAttributeNames: {
              "#ST": "status",
            },
            ExpressionAttributeValues: {
              ":st": { BOOL: true },
            },
            ReturnValues: "ALL_NEW",
            UpdateExpression: "SET #ST = :st",
          };
          await ddb.updateItem(params).promise();
        });

        isSuccess = true;
        message = "Signed Up Successfully.";

        res.status(200).json({
          success: isSuccess,
          message: message,
          data: {},
        });
      } else {
        throw new Error("You are already signedup with us.");
      }
    } else {
      if (recordsPhone.length > 0) {
        throw new Error(
          "Phone number already exists please try other phone number."
        );
      } else {
        throw new Error(
          "Email or member code not exists please contact admin."
        );
      }
    }
  } catch (error) {
    console.log("error", error);
    const statusCode = error.statusCode || 500;

    // Ensure error message is sent in the response
    const errorMessage = error.message || "An unexpected error occurred";
    res.status(statusCode).json({ message: errorMessage });
  }
});

// Resend verification code
app.post("/resendVerificationCode", async function (req, res) {
  try {
    const { userId = "", phoneNumber = "" } = req.body;
    console.log("req.body", req.body);
    const userTableName = `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

    const verificationCode = await sendVerificationCode(
      phoneNumber,
      userId,
      userTableName
    );

    const response = {
      success: true,
      message: "Verification code resend successfully.",
      data: {
        userId: userId ?? "",
        verificationCode,
      },
    };

    res.status(200).json(response);
  } catch (error) {
    const statusCode = error.statusCode || 500;
    res.status(statusCode).json({ error: error });
  }
});

// Set auth token
app.post("/setAuthToken", async function (req, res) {
  try {
    const { email = "", password = "" } = req.body;
    console.log("req.body", req.body);
    const { accessToken, userData } = await getToken(email, password);
    console.log("accessToken", accessToken);

    res.json({ accessToken: accessToken, userData: userData });
  } catch (error) {
    res.status(500).json({ error: error });
  }
});

async function getToken(username, password) {
  try {
    const authResponse = await AmazonCognitoIdentity.adminInitiateAuth({
      UserPoolId: USERPOOLID,
      ClientId: CLIENTID,
      AuthFlow: "ADMIN_USER_PASSWORD_AUTH",
      AuthParameters: {
        USERNAME: username,
        PASSWORD: password,
      },
    }).promise();
    console.log("authResponse", authResponse);
    const cognitoUser = await AmazonCognitoIdentity.adminGetUser({
      UserPoolId: USERPOOLID,
      Username: username,
    }).promise();
    console.log("cognitoUser", cognitoUser);
    const userData = await getUserById(cognitoUser?.Username);
    console.log("userData", userData);

    return {
      accessToken: authResponse.AuthenticationResult.AccessToken,
      userData: userData,
    };
  } catch (error) {
    console.error("Error getting auth token:", error);
    throw error;
  }
}

async function getUserById(userId) {
  const params = {
    TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    Key: { id: { S: userId } },
  };
  const getUserResponse = await ddb.getItem(params).promise();
  const userItem = getUserResponse["Item"]
    ? AWS.DynamoDB.Converter.unmarshall(getUserResponse["Item"])
    : null;
  console.log("userItem", userItem);
  return userItem;
}

/**
 * @deprecated This endpoint is no longer in use.
 * It was used for handling chat member requests.
 * Open.ai custom code
 */
app.post("/chatMember", async function (req, res) {
  try {
    let response = await main(req);
    res.json({ response });
  } catch (error) {
    console.log("error", error);
    res.status(500).json({ error: error });
  }
});

/**
 * This endpoint checks the user's status before a login API call.
 */
app.post("/checkUserStatus", async function (req, res) {
  try {
    let { email = "", type = "" } = req.body; // type can be login, signup, or reset
    console.log("req.body", req.body);
    let message = "";
    let isSuccess = false;
    const userTableName = `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
    const lowerCaseEmail = email.toLowerCase();

    // Fetch the user from Cognito using the email
    const cognitoUserParams = {
      UserPoolId: USERPOOLID,
      Filter: `email = "${lowerCaseEmail}"`, // Use the lowercase email
    };

    let cognitoUser = null;
    try {
      const cognitoUsersResponse = await cognito
        .listUsers(cognitoUserParams)
        .promise();
      cognitoUser = cognitoUsersResponse.Users && cognitoUsersResponse.Users[0];
      console.log("cognitoUser", cognitoUser);
    } catch (error) {
      console.error("Error fetching user:", error);
      return res
        .status(500)
        .json({ message: `Error fetching user: ${error.message}` });
    }

    let existingUser = null;

    const userParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
      },
      FilterExpression: "#D = :d",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#S": "isStakeholder",
        "#N": "name",
        "#ST": "status",
        "#R": "role",
      },
      ProjectionExpression:
        "id, memberCode, #N, email, phoneNumberVerified, #ST, #R,#S",
      TableName: userTableName,
    };

    const userDetail = await ddb.scan(userParams).promise();
    const userDetailRes = userDetail.Items.map((records) =>
      AWS.DynamoDB.Converter.unmarshall(records)
    );

    existingUser =
      userDetailRes.find(
        (inner) => inner.email.toLowerCase() === email.toLowerCase()
      ) || "";

    const userId = existingUser?.id ?? "";
    console.log("existingUser", existingUser);

    // Check if the user exists and their status is confirmed
    if (
      cognitoUser &&
      cognitoUser.UserStatus === "CONFIRMED" &&
      existingUser &&
      userDetailRes
    ) {
      // User is confirmed, proceed to check in database

      if (
        existingUser &&
        existingUser?.status == "active" &&
        existingUser.isStakeholder == true
      ) {
        message =
          type === "login"
            ? "This user is an active stakeholder and can log in."
            : type === "signup"
              ? "This user is an active stakeholder and can sign up."
              : "This user is an active stakeholder.";
        isSuccess = true;
      } else if (
        existingUser &&
        (existingUser?.status == "inactive" || existingUser?.status == "") &&
        existingUser.isStakeholder == true
      ) {
        message =
          type === "login"
            ? "You are not activated. Please contact the admin to activate your account."
            : type === "signup"
              ? "You are not activated. Please contact the admin to activate your account before signing up."
              : "You are not activated. Please contact the admin.";
        isSuccess = false;
      } else if (!existingUser) {
        message =
          type === "login"
            ? "User not found. Please sign up first."
            : type === "signup"
              ? "User not found. Please sign up first."
              : "User not found.";
        isSuccess = false;
      } else if (
        existingUser &&
        existingUser.isStakeholder == false &&
        (type === "login" || type === "signup")
      ) {
        message =
          type === "login"
            ? "You are not authorized to log in to the app."
            : type === "signup"
              ? "You are not authorized to sign up for the app. Please contact the admin for more details."
              : "You are not authorized.";
        isSuccess = false;
      }
    } else {
      // Handle other user statuses with switch case

      if (cognitoUser && userDetailRes) {
        switch (cognitoUser?.UserStatus) {
          case "FORCE_CHANGE_PASSWORD":
            message =
              type === "reset"
                ? "You need to sign up first, then you can reset the password."
                : type === "login" || type === "signup"
                  ? "User needs to sign up first, then you can log in."
                  : "User needs to sign up first.";
            isSuccess = false;
            break;

          case "UNCONFIRMED":
            message =
              type === "reset"
                ? "User is unconfirmed. Please check your email to confirm the account with the admin before resetting the password."
                : type === "login" || type === "signup"
                  ? "User is unconfirmed. Please check your email to confirm the account with the admin."
                  : "User is unconfirmed. Please check your email to confirm the account with the admin.";
            isSuccess = false;
            break;

          case "ARCHIVED":
            message =
              type === "reset"
                ? "This user is archived and cannot reset the password."
                : type === "login" || type === "signup"
                  ? "This user is archived and cannot log in or sign up."
                  : "This user is archived.";
            isSuccess = false;
            break;

          case "UNKNOWN":
            message =
              type === "reset"
                ? "We are unable to verify your account status. Please contact the admin for assistance with resetting your password."
                : type === "login" || type === "signup"
                  ? "There was an issue with your account. Please contact the admin for assistance."
                  : "There was an issue with your account. Please contact the admin for assistance.";
            isSuccess = false;
          default:
            message =
              type === "reset"
                ? "User is not registered."
                : type === "login" || type === "signup"
                  ? "User is not registered."
                  : "User is not register.";
            isSuccess = false;
            break;
        }
      } else {
        message =
          type === "reset"
            ? "The user is not signed up or registered with us. Please click on 'Sign Up' or contact the admin for further details."
            : type === "login" || type === "signup"
              ? "The user is not signed up or registered with us. Please click on 'Sign Up' or contact the admin for further details."
              : "The user is not signed up or registered with us. Please click on 'Sign Up' or contact the admin for further details.";
        isSuccess = false;
      }
    }

    // Send the response
    const response = {
      success: isSuccess,
      message: message,
      data: {
        isExist: !!existingUser,
        userId: existingUser?.id ?? "",
      },
    };

    res.status(200).json(response);
  } catch (error) {
    console.log("error", error);
    const statusCode = error.statusCode || 500;
    res.status(statusCode).json({ error: error.message });
  }
});

/**
 * This is used for custom updation of table
 */
app.post("/updateTableData", async (req, res) => {
  try {
    console.log("Starting table update process...");

    const prod = "Association-vhxxfscf6vakrgoevj7se63eqq-prod";
    const dev = "Association-t4nkkv2uhreizaztlxiidrwbku-dev";
    const local = "Association-rlumv7mljje5bb65hsiqpi77hu-amplifydev";
    const envName = local;

    // Define AssociationRelationType table name
    const relationTypeTableProd = "AssociationRelationType-vhxxfscf6vakrgoevj7se63eqq-prod";
    const relationTypeTableDev = "AssociationRelationType-t4nkkv2uhreizaztlxiidrwbku-dev";
    const relationTypeTableLocal = "AssociationRelationType-rlumv7mljje5bb65hsiqpi77hu-amplifydev";
    const relationTypeTableName = relationTypeTableLocal;

    // Fetch all relationship types from AssociationRelationType table
    console.log("Fetching relationship types from:", relationTypeTableName);
    const relationTypeParams = {
      TableName: relationTypeTableName,
    };
    
    const relationTypeResult = await ddb.scan(relationTypeParams).promise();
    const relationTypes = relationTypeResult.Items.map((item) =>
      AWS.DynamoDB.Converter.unmarshall(item)
    );
    
    // Create a mapping of relationName to ID
    const relationNameToIdMap = {};
    relationTypes.forEach((relationType) => {
      if (relationType.relationName && !relationType._deleted) {
        relationNameToIdMap[relationType.relationName] = relationType.id;
      }
    });
    
    console.log("Relation name to ID mapping:", relationNameToIdMap);

    // Fetch all items from the Associations table
    const params = {
      TableName: envName,
    };
    console.log("Fetching data from:", params.TableName);

    const scanResult = await ddb.scan(params).promise();
    const items = scanResult.Items.map((item) =>
      AWS.DynamoDB.Converter.unmarshall(item)
    );

    // Update each association with the correct relationTypeID
    let updatedCount = 0;
    let skippedCount = 0;
    
    await Promise.all(
      items.map(async (item) => {
        // Skip if the item is already deleted
        if (item._deleted || item.isDeleted === "true") {
          console.log(`Skipping deleted item ID: ${item.id}`);
          skippedCount++;
          return;
        }
        
        // Check if relationType exists and find corresponding ID in the mapping
        if (item.relationType && relationNameToIdMap[item.relationType]) {
          const relationTypeId = relationNameToIdMap[item.relationType];
          
          const updateParams = {
            TableName: envName,
            Key: { id: { S: item.id } },
            UpdateExpression: "SET relationTypeID = :relationTypeID",
            ExpressionAttributeValues: {
              ":relationTypeID": { S: relationTypeId },
            },
          };

          try {
            await ddb.updateItem(updateParams).promise();
            console.log(`Updated Association ID: ${item.id} with relationTypeID: ${relationTypeId} for relationType: ${item.relationType}`);
            updatedCount++;
          } catch (error) {
            console.error(`Error updating Association ID: ${item.id}`, error);
            skippedCount++;
          }
        } else {
          console.log(`Skipping Association ID: ${item.id} - relationType not found in mapping: ${item.relationType}`);
          skippedCount++;
        }
      })
    );

    console.log(`Total associations updated: ${updatedCount}, skipped: ${skippedCount}`);
    res.status(200).json({ 
      message: "Table update completed successfully.", 
      updatedCount,
      skippedCount 
    });
  } catch (error) {
    console.error("Error updating table:", error);
    res.status(500).json({ error: "Failed to fetch and update data." });
  }
});


async function updateCognitoUserCustomField(cognitoId, email) {
  try {
    const getUserParams = {
      UserPoolId: USERPOOLID,
      Username: cognitoId,
    };

    return await cognito.adminGetUser(getUserParams).promise();
  } catch (error) {
    console.error("Error get user:", error);
  }
}

async function main(req) {
  const { username = "", question = "" } = req.body;

  const myFile = await openai.files.create({
    file: fs.createReadStream("json-demo.json"),
    purpose: "assistants",
  });
  console.log("This is the file object: ", myFile, "\n");

  // Step 2: Create an Assistant
  const myAssistant = await openai.beta.assistants.create({
    model: "gpt-4-turbo-preview",
    instructions:
      "You are a support bot, and you have access to JSON files to answer user questions about their associations.",
    name: "Customer Support Chatbot",
    tools: [{ type: "retrieval" }],
    file_ids: [myFile.id],
  });
  console.log("This is the assistant object: ", myAssistant, "\n");

  // Step 3: Create a Thread
  const myThread = await openai.beta.threads.create();
  console.log("This is the thread object: ", myThread, "\n");

  // Step 4: Add a Message to a Thread
  const myThreadMessage = await openai.beta.threads.messages.create(
    (thread_id = myThread.id),
    {
      role: "user",
      content: `${question}`,
      file_ids: [myFile.id],
    }
  );
  console.log("This is the message object: ", myThreadMessage, "\n");

  // Step 5: Run the Assistant
  const myRun = await openai.beta.threads.runs.create(
    (thread_id = myThread.id),
    {
      assistant_id: myAssistant.id,
      instructions: `Please address the user as ${username}.`,
    }
  );
  console.log("This is the run object: ", myRun, "\n");

  // Step 6: Periodically retrieve the Run to check on its status to see if it has moved to completed
  const retrieveRun = async () => {
    let keepRetrievingRun;

    while (myRun.status === "queued" || myRun.status === "in_progress") {
      keepRetrievingRun = await openai.beta.threads.runs.retrieve(
        (thread_id = myThread.id),
        (run_id = myRun.id)
      );
      console.log(`Run status: ${keepRetrievingRun.status}`);

      if (keepRetrievingRun.status === "completed") {
        console.log("\n");

        // Step 7: Retrieve the Messages added by the Assistant to the Thread
        const allMessages = await openai.beta.threads.messages.list(
          (thread_id = myThread.id)
        );

        console.log(
          "------------------------------------------------------------ \n"
        );

        console.log("User: ", myThreadMessage.content[0].text.value);
        console.log("Assistant: ", allMessages.data[0].content[0].text.value);

        return {
          User: myThreadMessage.content[0].text.value,
          Assistant: allMessages.data[0].content[0].text.value,
        };
      } else if (
        keepRetrievingRun.status === "queued" ||
        keepRetrievingRun.status === "in_progress"
      ) {
        // pass
      } else {
        console.log(`Run status: ${keepRetrievingRun.status}`);
        break;
      }
    }
  };

  return await retrieveRun();
}

// Stripe webhook handler for onramp success
app.post("/webhooks/stripe/onramp-success", async function (req, res) {
  try {
    // Get the signature from the headers (case-insensitive check)
    const sig = req.headers['stripe-signature'] || 
                req.headers['Stripe-Signature'];
    
    if (!sig) {
      console.error('No Stripe signature found in headers');
      console.log('Available headers:', JSON.stringify(req.headers, null, 2));
      return res.status(400).json({ error: 'Missing Stripe signature' });
    }

    // Get the raw body for signature verification
    const rawBody = req.rawBody;
    
    if (!rawBody) {
      console.error('No raw body available for signature verification');
      return res.status(400).json({ error: 'Invalid request body' });
    }
    
    console.log('Raw body length:', rawBody.length);
    console.log('Signature:', sig.substring(0, 20) + '...');
    
    let event;
    
    try {
      // Verify the webhook signature
      event = stripe.webhooks.constructEvent(
        rawBody,
        sig,
        process.env.STRIPE_WEBHOOK_SECRET
      );
      console.log('Stripe webhook verified successfully');
    } catch (err) {
      console.error('Webhook signature verification failed:', err.message);
      return res.status(400).json({ error: `Webhook Error: ${err.message}` });
    }

    console.log('event.type: ', event.type);
    // Handle the crypto.onramp_session.updated event
    if (event.type === 'crypto.onramp_session.updated') {
      const session = event.data.object;
      console.log('session: ', session);
      
      console.log('Processing onramp session update:', {
        sessionId: session.id,
        status: session.status,
        metadata: session.metadata
      });
      
      // Only process fulfillment_complete status for successful purchases
      if (session.status === 'fulfillment_complete') {
        try {
          const { 
            user_id: userId,
            mvt_amount: mvtAmount,
            usdc_amount: usdcAmount,
            exchange_rate: exchangeRate,
            transaction_type: transactionType
          } = session.metadata || {};
          
          if (!userId || !mvtAmount || !usdcAmount || !exchangeRate) {
            console.error('Missing required metadata in onramp session', {
              userId,
              mvtAmount,
              usdcAmount,
              exchangeRate,
              transactionType,
              sessionId: session.id,
              allMetadata: session.metadata
            });
            return res.status(400).json({ 
              error: 'Missing required metadata in onramp session',
              details: {
                received: session.metadata,
                expected: ['user_id', 'mvt_amount', 'usdc_amount', 'exchange_rate']
              }
            });
          }
          
          console.log('Processing successful on-ramp purchase', { 
            userId, 
            mvtAmount, 
            usdcAmount, 
            exchangeRate,
            transactionType,
            sessionId: session.id
          });

          const lambdaParams = {
            FunctionName: `mvtWallet-${process.env.ENV}`,
            InvocationType: 'RequestResponse',
            Payload: JSON.stringify({
              field: 'processOnrampTransfer',
              fieldName: 'processOnrampTransfer',
              arguments: {
                userId,
                mvtAmount: parseFloat(mvtAmount),
                usdcAmount: parseFloat(usdcAmount),
                exchangeRate: parseFloat(exchangeRate),
                transactionType,
                sessionId: session.id,
                description: `Stripe on-ramp purchase: ${session.id}`,
                status: session.status
              },
              identity: {
                username: `stripe-onramp-${session.id}`,
                groups: ['stripe-onramp'],
                sourceIp: req.ip || '0.0.0.0'
              },
              request: {
                headers: {
                  'x-api-key': process.env.API_KEY,
                  'Content-Type': 'application/json'
                }
              },
              info: {
                selectionSetList: [
                  'statusCode',
                  'message',
                  'data { id transactionType amount status description metadata createdAt }'
                ]
              }
            })
          };

          console.log('Invoking mvtWallet Lambda with params:', JSON.stringify({
            ...lambdaParams,
            Payload: JSON.parse(lambdaParams.Payload) // Log the parsed payload for better readability
          }, null, 2));
          
          const lambdaResponse = await lambda.invoke(lambdaParams).promise();
          
          if (lambdaResponse.FunctionError) {
            console.error('Lambda function error:', lambdaResponse.FunctionError);
            throw new Error('Error processing on-ramp transaction');
          }
          
          let responsePayload;
          try {
            responsePayload = typeof lambdaResponse.Payload === 'string' 
              ? JSON.parse(lambdaResponse.Payload) 
              : lambdaResponse.Payload;
              
            if (responsePayload.body) {
              responsePayload = JSON.parse(responsePayload.body);
            }
            
            console.log('Lambda response:', JSON.stringify(responsePayload, null, 2));
            
            if (responsePayload.errorMessage) {
              throw new Error(responsePayload.errorMessage);
            }
            
            if (responsePayload.statusCode && responsePayload.statusCode !== 200) {
              throw new Error(responsePayload.message || 'Failed to process on-ramp transaction');
            }
            
            console.log('Successfully processed on-ramp purchase', { 
              userId, 
              result: responsePayload
            });
            
            return res.status(200).json({ 
              received: true, 
              status: 'success',
              message: 'On-ramp transaction processed successfully',
              result: responsePayload 
            });
            
          } catch (parseError) {
            console.error('Error parsing Lambda response:', parseError);
            console.error('Raw Lambda response:', lambdaResponse);
            throw new Error('Error processing transaction response');
          }
          
        } catch (error) {
          console.error('Error processing onramp.session.updated:', error);
          return res.status(500).json({ 
            error: error.message || 'Internal server error',
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined
          });
        }
      }
    }

    console.log(`Skiped event type: ${event.type}`);
    return res.status(200).json({ received: true, message: `Skiped event type: ${event.type}` });
  } catch (error) {
    console.error('Error in webhook handler:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Migrate data between DynamoDB tables in different environments
 * @param {string} sourceTable - Source table name (e.g., 'Association-t4nkkv2uhreizaztlxiidrwbku-dev')
 * @param {string} targetTable - Target table name (e.g., 'Association-fznlrfufwfdbxkhodjhgase67a-develop')
 * @returns {Promise<Object>} - Migration result with counts and status
 */
const migrateTableData = async (sourceTable, targetTable) => {
  try {
    console.log(`Starting migration from ${sourceTable} to ${targetTable}`);
    
    // 1. Scan all items from source table
    const scanParams = {
      TableName: sourceTable,
      ConsistentRead: true
    };
    
    const items = [];
    let lastEvaluatedKey = null;
    let scannedCount = 0;
    
    // Handle pagination in case there are more than 1MB of data
    do {
      if (lastEvaluatedKey) {
        scanParams.ExclusiveStartKey = lastEvaluatedKey;
      }
      
      const scanResult = await ddb.scan(scanParams).promise();
      items.push(...(scanResult.Items || []));
      scannedCount += scanResult.ScannedCount || 0;
      lastEvaluatedKey = scanResult.LastEvaluatedKey;
    } while (lastEvaluatedKey);
    
    if (items.length === 0) {
      return {
        success: true,
        message: `No items found in source table: ${sourceTable}`,
        scannedCount,
        migratedCount: 0
      };
    }
    
    // 2. Log total items to be migrated
    const totalItems = items.length;
    console.log(`Found ${totalItems} items to migrate from ${sourceTable} to ${targetTable}`);
    
    // 3. Prepare batch write requests (max 25 items per batch)
    const batchSize = 25;
    const batches = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchRequest = {
        RequestItems: {
          [targetTable]: batch.map(item => ({
            PutRequest: {
              Item: item
            }
          }))
        }
      };
      batches.push(batchRequest);
    }
    
    // 4. Execute batch writes with progress logging
    let migratedCount = 0;
    const startTime = Date.now();
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const batchSize = batch.RequestItems[targetTable].length;
      
      await ddb.batchWriteItem(batch).promise();
      migratedCount += batchSize;
      
      // Calculate progress
      const progress = ((migratedCount / totalItems) * 100).toFixed(2);
      const elapsedTime = (Date.now() - startTime) / 1000; // in seconds
      const itemsPerSecond = (migratedCount / elapsedTime).toFixed(2);
      const estimatedTotalTime = (totalItems / (migratedCount / elapsedTime)).toFixed(2);
      const remainingTime = (estimatedTotalTime - elapsedTime).toFixed(2);
      
      console.log(`
Migration Progress:
- Completed: ${migratedCount} of ${totalItems} items (${progress}%)
- Rate: ${itemsPerSecond} items/second
- Elapsed Time: ${elapsedTime.toFixed(2)} seconds
- Estimated Remaining Time: ${remainingTime} seconds
- Current Batch: ${i + 1} of ${batches.length}
`);
    }
    
    return {
      success: true,
      message: `Successfully migrated ${migratedCount} items from ${sourceTable} to ${targetTable}`,
      scannedCount,
      migratedCount
    };
    
  } catch (error) {
    console.error('Migration error:', error);
    return {
      success: false,
      message: `Migration failed: ${error.message}`,
      error: error.stack
    };
  }
};

/**
 * API endpoint to migrate data between DynamoDB tables
 * Expected request body: {
 *   sourceTable: 'TableName-env',
 *   targetTable: 'TableName-env'
 * }
 */
app.post('/migrateTableData', async (req, res) => {
  try {
    const { sourceTable, targetTable } = req.body;
    
    if (!sourceTable || !targetTable) {
      return res.status(400).json({
        success: false,
        message: 'Both sourceTable and targetTable are required in the request body'
      });
    }
    
    console.log(`Migration request received: ${sourceTable} -> ${targetTable}`);
    
    // Execute the migration
    const result = await migrateTableData(sourceTable, targetTable);
    
    // Return appropriate status code based on success/failure
    if (result.success) {
      res.status(200).json(result);
    } else {
      res.status(500).json(result);
    }
    
  } catch (error) {
    console.error('Error in migration endpoint:', error);
    res.status(500).json({
      success: false,
      message: `Internal server error: ${error.message}`,
      error: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

/**
 * API endpoint to migrate a user between environments
 * Expected request body: {
 *   email: '<EMAIL>',
 *   sourceTable: 'User-tableid-dev',   // Full source table name
 *   targetTable: 'User-tableid-develop' // Full target table name
 * }
 */
app.post('/migrateUser', async (req, res) => {
  try {
    const { email, sourceTable, targetTable } = req.body;
    const developApiUrl = 'https://u4gyl6dmeh.execute-api.us-east-1.amazonaws.com';
    
    // Validate required fields
    const missingFields = [];
    if (!email) missingFields.push('email');
    if (!sourceTable) missingFields.push('sourceTable');
    if (!targetTable) missingFields.push('targetTable');
    
    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        message: `Missing required fields: ${missingFields.join(', ')}`,
        requiredFields: ['email', 'sourceTable', 'targetTable']
      });
    }
    
    console.log(`User migration request received: ${email} from ${sourceTable} to ${targetTable}`);
    
    // 1. Fetch user from source environment
    const params = {
      TableName: sourceTable,
      FilterExpression: 'email = :email',
      ExpressionAttributeValues: {
        ':email': { S: email.toLowerCase() }
      }
    };
    
    const userData = await ddb.scan(params).promise();
    
    if (!userData.Items || userData.Items.length === 0) {
      return res.status(404).json({
        success: false,
        message: `User with email ${email} not found in dev environment`
      });
    }
    
    // 2. Prepare user data for creation in target environment
    const user = AWS.DynamoDB.Converter.unmarshall(userData.Items[0]);
    const userCreatePayload = {
      // Basic Info
      email: user.email,
      familyName: user.familyName,
      givenName: user.givenName,
      name: user.name,
      phoneNumber: user.phoneNumber,
      
      // Address
      cityId: user.cityId,
      stackholderCities: user.stackholderCities,
      streetAddressOne: user.streetAddressOne,
      streetAddressTwo: user.streetAddressTwo,
      city: user.city,
      state: user.state,
      zipCode: user.zipCode,
      countryCode: user.countryCode,
      
      // Profile
      imageUrl: user.imageUrl,
      profileUrl: user.profileUrl,
      birthday: user.birthday,
      ethnicity: user.ethnicity,
      gender: user.gender || 'NONE',
      
      // Account
      role: user.role || 'USER',
      assignedRole: user.assignedRole || 'BOARD_MEMBER',
      type: user.type,
      userType: user.userType,
      impactScore: user.impactScore,
      status: user.status,
      registeredFrom: user.registeredFrom,
      isAssociated: user.isAssociated || false,
      
      // Device/Login
      deviceId: user.deviceId,
      FCMToken: user.FCMToken,
      endpointArn: user.endpointArn,
      loginPlatform: user.loginPlatform,
      isLogin: user.isLogin,
      otherDeviceId: user.otherDeviceId,
      otherFCMToken: user.otherFCMToken,
      isLoginOther: user.isLoginOther,
      isStakeholder: user.isStakeholder,
      defaultMessageDate: user.defaultMessageDate,
      
      // Membership/Subscription
      membershipId: user.membershipId,
      customerId: user.customerId,
      
      // Verification
      memberCode: user.memberCode,
      verificationCode: user.verificationCode,
      codeExpiration: user.codeExpiration,
      phoneNumberVerified: user.phoneNumberVerified,
      studentEnrollmentDate: user.studentEnrollmentDate,
      
      // Web3
      walletAddress: user.walletAddress || '',
      
      // System
      isSignup: user.isSignup,
      socketConnection: user.socketConnection,
      logoutTime: user.logoutTime,
      userAddedFrom: user.userAddedFrom || 'STAKEHOLDER_CREATED',
      cognitoId: user.cognitoId,
      isDeleted: user.isDeleted || 'false',
      createdAt: user.createdAt || new Date().toISOString()
    };
    
    console.log(`Creating user in target environment (${targetTable}):`, userCreatePayload);
    
    // 3. Create user in target environment using createUserInDynamoDBv3
    console.log(`Creating user in target table: ${targetTable}`);
    
    // 3. Create user in target environment using createUserInDynamoDBv3
    console.log(`Creating user in target table: ${targetTable}`);
    
    // Create a request object with values directly from the source table
    const userReq = {
      body: {
        ...userCreatePayload,
        // Only include fields that exist in the source data
        isAssociated: userCreatePayload.isAssociated,
        status: userCreatePayload.status,
        isActive: userCreatePayload.isActive,
        isEmailVerified: userCreatePayload.isEmailVerified,
        isPhoneVerified: userCreatePayload.isPhoneVerified,
        role: userCreatePayload.role,
        registeredFrom: userCreatePayload.registeredFrom, // Use the value from source table
        countryCode: userCreatePayload.countryCode,
        cognitoId: userCreatePayload.cognitoId
      }
    };
    
    // Remove undefined values to avoid overwriting with undefined
    Object.keys(userReq.body).forEach(key => {
      if (userReq.body[key] === undefined) {
        delete userReq.body[key];
      }
    });
    
    try {
      let createResult;
      
      try {
        // First try to create the user
        createResult = await createUserInDynamoDBv3(userReq, sourceTable, targetTable);
        console.log('User created successfully:', createResult);
        
        // Add a small delay to ensure the record is fully propagated in DynamoDB
        console.log('Waiting for record propagation...');
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
      } catch (createError) {
        // If user already exists, try to get the existing user
        if (createError.message.includes('already exists')) {
          console.log('User already exists, fetching existing user...');
          const getParams = {
            TableName: targetTable,
            FilterExpression: 'email = :email',
            ExpressionAttributeValues: {
              ':email': { S: userReq.body.email.toLowerCase() }
            }
          };
          
          const existingUser = await ddb.scan(getParams).promise();
          if (existingUser.Items && existingUser.Items.length > 0) {
            createResult = AWS.DynamoDB.Converter.unmarshall(existingUser.Items[0]);
            console.log('Found existing user, proceeding with verification');
          } else {
            throw new Error('User creation failed and could not find existing user');
          }
        } else {
          throw createError; // Re-throw if it's not a duplicate user error
        }
      }
      
      // Prepare verification request with all necessary fields
      const verifyRequest = {
        email: userReq.body.email,
        memberCode: userReq.body.memberCode || (createResult && createResult.memberCode) || '',
        phoneNumber: userReq.body.phoneNumber || (createResult && createResult.phoneNumber) || '',
        password: userReq.body.password || 'Test@1234', // Default password if not provided
        registeredFrom: userReq.body.registeredFrom || 'MIGRATION'
      };
      
      // Ensure memberCode is a string for comparison in verifyUserDefaultV3
      if (verifyRequest.memberCode) {
        verifyRequest.memberCode = verifyRequest.memberCode.toString();
      }
      
      console.log('Verification request:', JSON.stringify(verifyRequest, null, 2));
      
      console.log('Verifying user...');
      const verifyResult = await verifyUserDefaultV3(verifyRequest, targetTable);
      
      if (verifyResult.success) {
        console.log('User verified successfully');
        // Update the createResult with cognitoId from verification
        createResult.cognitoId = verifyResult.cognitoId;
      } else {
        console.warn('User verification failed:', verifyResult.message);
      }
      
      if (!verifyResult?.success) {
        console.error('User verification failed:', verifyResult?.message || 'Unknown error');
        // Don't fail the migration if verification fails, just log it
      }
      
      // 4. If user has an association, create it
      if (userCreatePayload.isAssociated) {
        try {
          await createAssoction(userReq, { createId: createResult.id });
          console.log('User association created successfully');
        } catch (assocError) {
          console.error('Error creating user association:', assocError);
          // Continue even if association fails, as the user was created successfully
        }
      }
      
      return res.status(200).json({
        success: true,
        message: `User migrated successfully from ${sourceTable} to ${targetTable}`,
        sourceTable,
        targetTable,
        data: {
          userCreate: createResult
        }
      });
    } catch (createError) {
      console.error('Error creating user in DynamoDB:', createError);
      throw new Error(`Failed to create user: ${createError.message}`);
    }
  } catch (error) {
    console.error('Error in user migration:', error);
    res.status(500).json({
      success: false,
      message: `User migration failed: ${error.message}`,
      error: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

app.listen(3000, function () {
  console.log("App started");
});

// Export the app object. When executing the application local this does nothing. However,
// to port it to AWS Lambda we will create a wrapper around that will load the app from
// this file
module.exports = app;
