<!-- MVT Swap Approval Panel -->
<div class="card">
    <div class="card-header">
        <div class="card-title d-flex justify-content-between align-items-center w-100">
            <h3>MVT Swap Approval Panel</h3>
            <div>
                <button class="btn btn-sm btn-light-success me-2" (click)="refreshRequests()" [disabled]="isLoading">
                    <i *ngIf="!isLoading" class="bi bi-arrow-repeat me-2"></i>
                    <i *ngIf="isLoading" class="spinner-border spinner-border-sm me-2"></i>
                    Refresh
                </button>
                <button class="btn btn-sm btn-light-primary" (click)="navigateToDashboard()">
                    <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- Tabs for Different Request Statuses -->
        <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
            <li class="nav-item">
                <a class="nav-link" [ngClass]="{'active': activeTab === SwapRequestStatus.PENDING}"
                    (click)="changeTab(SwapRequestStatus.PENDING)">
                    Pending Requests
                    <span class="badge badge-primary ms-2"
                        *ngIf="pendingRequests.length > 0">{{pendingRequests.length}}</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" [ngClass]="{'active': activeTab === SwapRequestStatus.APPROVED}"
                    (click)="changeTab(SwapRequestStatus.APPROVED)">
                    Approved Requests
                    <span class="badge badge-success ms-2"
                        *ngIf="approvedRequests.length > 0">{{approvedRequests.length}}</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" [ngClass]="{'active': activeTab === SwapRequestStatus.REJECTED}"
                    (click)="changeTab(SwapRequestStatus.REJECTED)">
                    Rejected Requests
                    <span class="badge badge-danger ms-2"
                        *ngIf="rejectedRequests.length > 0">{{rejectedRequests.length}}</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" [ngClass]="{'active': activeTab === SwapRequestStatus.FAILED}"
                    (click)="changeTab(SwapRequestStatus.FAILED)">
                    Failed Requests
                    <span class="badge badge-dark ms-2"
                        *ngIf="failedRequests.length > 0">{{failedRequests.length}}</span>
                </a>
            </li>
            <li class="nav-item" hidden>
                <a class="nav-link" [ngClass]="{'active': activeTab === SwapRequestStatus.EXPIRED}"
                    (click)="changeTab(SwapRequestStatus.EXPIRED)">
                    Expired Requests
                    <span class="badge badge-warning ms-2"
                        *ngIf="expiredRequests.length > 0">{{expiredRequests.length}}</span>
                </a>
            </li>
        </ul>

        <!-- Request Table -->
        <div class="table-responsive">
            <table class="table align-middle table-row-bordered table-row-solid gy-4 gs-9">
                <!-- Table header -->
                <thead class="border-gray-200 fs-5 fw-semibold bg-lighten">
                    <tr>
                        <th class="min-w-150px ps-9">Date</th>
                        <th class="min-w-200px">User</th>
                        <th class="min-w-200px">Wallet Address</th>
                        <th class="min-w-125px">MVT Amount</th>
                        <th class="min-w-125px">USDC Amount</th>
                        <th class="min-w-120px">Status</th>
                        <th class="min-w-100px text-center">Transaction</th>
                        <th class="min-w-150px text-end pe-9" *ngIf="activeTab === SwapRequestStatus.PENDING || activeTab === SwapRequestStatus.FAILED">Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="fs-6 fw-semibold text-gray-600">
                    <!-- Loading state for swap requests -->
                    <tr *ngIf="isLoading">
                        <td [attr.colspan]="(activeTab === SwapRequestStatus.PENDING || activeTab === SwapRequestStatus.FAILED) ? 8 : 7" class="text-center">
                            <div class="d-flex flex-column align-items-center py-5">
                                <div class="spinner-border text-primary mb-3">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="text-muted">{{spinnerMessage || 'Loading requests...'}}</span>
                            </div>
                        </td>
                    </tr>

                    <!-- Regular request rows when not loading -->
                    <tr *ngFor="let request of displayRequests" [hidden]="isLoading">
                        <td class="ps-9">{{formatDate(request.date)}}</td>
                        <td>{{request.userName}}</td>
                        <td class="text-gray-600">
                            <span title="{{request.walletAddress}}">{{truncateAddress(request.walletAddress)}}</span>
                        </td>
                        <td>{{request.mvtAmount}} MVT</td>
                        <td>{{request.usdcAmount}} USDC</td>
                        <td>
                            <span [ngClass]="{
                                'badge badge-light-primary': request.status === SwapRequestStatus.PENDING,
                                'badge badge-light-success': request.status === SwapRequestStatus.APPROVED,
                                'badge badge-light-danger': request.status === SwapRequestStatus.REJECTED,
                                'badge badge-light-dark': request.status === SwapRequestStatus.FAILED,
                                'badge badge-light-warning': request.status === SwapRequestStatus.EXPIRED
                            }">{{request.status}}</span>
                        </td>
                        <td class="text-center">
                            <a *ngIf="request.transactionHash" href="javascript:void(0)"
                                class="btn btn-icon btn-sm btn-light-primary"
                                (click)="openInEtherscan(request.transactionHash)" title="View on Etherscan">
                                <i class="bi bi-box-arrow-up-right"></i>
                            </a>
                            <span *ngIf="!request.transactionHash" class="text-muted">—</span>
                        </td>
                        <td class="text-end pe-9" *ngIf="activeTab === SwapRequestStatus.PENDING || activeTab === SwapRequestStatus.FAILED">
                            <div *ngIf="request.status === SwapRequestStatus.PENDING">
                                <button class="btn btn-sm btn-light-success me-2" (click)="approveRequest(request)"
                                    [disabled]="isProcessingRequest(request.id)">
                                    <span *ngIf="!isProcessingRequest(request.id)">Approve</span>
                                    <span *ngIf="isProcessingRequest(request.id)">
                                        <span class="spinner-border spinner-border-sm me-1"></span>
                                        Processing...
                                    </span>
                                </button>
                                <button class="btn btn-sm btn-light-danger" (click)="rejectRequest(request)"
                                    [disabled]="isProcessingRequest(request.id)">
                                    <span *ngIf="!isProcessingRequest(request.id)">Reject</span>
                                    <span *ngIf="isProcessingRequest(request.id)">
                                        <span class="spinner-border spinner-border-sm me-1"></span>
                                        Processing...
                                    </span>
                                </button>
                            </div>
                            <div *ngIf="request.status === SwapRequestStatus.FAILED">
                                <button class="btn btn-sm btn-light-warning me-2" (click)="retryRequest(request)"
                                    [disabled]="isProcessingRequest(request.id)" title="Retry failed swap">
                                    <span *ngIf="!isProcessingRequest(request.id)">
                                        <i class="bi bi-arrow-clockwise me-1"></i>Retry
                                    </span>
                                    <span *ngIf="isProcessingRequest(request.id)">
                                        <span class="spinner-border spinner-border-sm me-1"></span>
                                        Retrying...
                                    </span>
                                </button>
                                <button class="btn btn-sm btn-light-danger" (click)="rejectRequest(request)"
                                    [disabled]="isProcessingRequest(request.id)" title="Reject failed swap">
                                    <span *ngIf="!isProcessingRequest(request.id)">Reject</span>
                                    <span *ngIf="isProcessingRequest(request.id)">
                                        <span class="spinner-border spinner-border-sm me-1"></span>
                                        Processing...
                                    </span>
                                </button>
                            </div>
                            <div *ngIf="request.status !== SwapRequestStatus.PENDING && request.status !== SwapRequestStatus.FAILED">
                                <span class="text-muted">No actions available</span>
                            </div>
                        </td>
                    </tr>

                    <!-- Empty state when not loading but no data -->
                    <tr *ngIf="displayRequests.length === 0 && !isLoading">
                        <td [attr.colspan]="(activeTab === SwapRequestStatus.PENDING || activeTab === SwapRequestStatus.FAILED) ? 8 : 7" class="text-center py-5">
                            <div class="d-flex flex-column align-items-center">
                                <i class="bi bi-inbox fs-3x text-muted mb-3"></i>
                                <span class="text-muted">No {{activeTab}} requests found</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>