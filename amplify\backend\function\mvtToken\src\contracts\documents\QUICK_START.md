# MVT Token System Quick Start Guide

This quick start guide provides practical information about the MVT Token system, including the core functionality, how to interact with the contracts, and integration examples.

## Table of Contents
- [System Overview](#system-overview)
- [MVT Token Functionality](#mvt-token-functionality)
- [Withdraw Contract Functionality](#withdraw-contract-functionality)
- [Example Workflows](#example-workflows)
- [Frontend Integration](#frontend-integration)
- [Admin Operations](#admin-operations)
- [Monitoring and Management](#monitoring-and-management)

## System Overview

The MVT Token system consists of two main smart contracts:

1. **MVT Token (ERC-20)**: A standard ERC-20 token with additional features for the My Village platform
2. **WithdrawMVTToken**: A management contract for handling token swaps between MVT and USDC

The system enables:
- Minting and distribution of MVT tokens
- Depositing USDC into the contract
- Swapping MVT for USDC (and vice versa)
- Handling approval workflows for withdrawals

## MVT Token Functionality

### Core Features

- **Minting**: Create new MVT tokens (admin only)
- **Transfer**: Send MVT tokens between accounts
- **Balance Check**: View token balances for any address

### Key Functions

```solidity
// Mint tokens to the contract itself
function mint(uint256 amount) public onlyOwner

// Approve another contract to spend tokens
function approveMVT(address spender, uint256 amount) public onlyOwner

// Transfer tokens from contract to recipient
function transferMVT(address recipient, uint256 amount) public onlyOwner

// Check balance of any address
function getMVTBalance(address user) public view returns (uint256)
```

## Withdraw Contract Functionality

### Core Features

- **Deposit USDC**: Users can deposit USDC into the contract
- **Request Swap**: Users can request to swap MVT for USDC
- **Approve Swap**: Admins can approve swap requests
- **Exchange Rate**: Dynamic calculation of exchange rates

### Key Functions

```solidity
// Get current exchange rate between MVT and USDC
function getExchangeRate() public view returns (uint256)

// User deposits USDC into the contract
function depositUSDC(uint256 amount) external

// User requests to swap MVT for USDC
function requestSwap(uint256 mvtAmount) external

// Admin approves a swap request
function approveSwap(uint256 requestId) external onlyOwner

// Admin approves USDC withdrawal
function approveWithdraw(address user, uint256 amount) external onlyOwner
```

## Example Workflows

### 1. User Deposits USDC

1. User approves USDC contract to spend tokens:
```javascript
const usdcContract = new ethers.Contract(usdcAddress, usdcAbi, signer);
await usdcContract.approve(withdrawContractAddress, amountToDeposit);
```

2. User deposits USDC:
```javascript
const withdrawContract = new ethers.Contract(withdrawContractAddress, withdrawAbi, signer);
await withdrawContract.depositUSDC(amountToDeposit);
```

### 2. User Requests to Swap MVT for USDC

1. User approves MVT contract to spend tokens:
```javascript
const mvtContract = new ethers.Contract(mvtAddress, mvtAbi, signer);
await mvtContract.approve(withdrawContractAddress, amountToSwap);
```

2. User requests swap:
```javascript
const withdrawContract = new ethers.Contract(withdrawContractAddress, withdrawAbi, signer);
await withdrawContract.requestSwap(amountToSwap);
```

### 3. Admin Approves a Swap Request

```javascript
const withdrawContract = new ethers.Contract(withdrawContractAddress, withdrawAbi, adminSigner);
await withdrawContract.approveSwap(requestId);
```

## Frontend Integration

### Setting Up Contracts in Angular Service

Add this to your shared service:

```typescript
import { Injectable } from '@angular/core';
import { ethers } from 'ethers';
import { Observable, from } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class TokenService {
  private provider: ethers.providers.Web3Provider;
  private mvtContract: ethers.Contract;
  private withdrawContract: ethers.Contract;
  
  // Contract addresses (from environment or config)
  private mvtAddress = environment.mvtTokenAddress;
  private withdrawAddress = environment.withdrawContractAddress;
  
  // ABIs (imported from JSON files)
  private mvtAbi = require('../../../assets/abis/MVTToken.json');
  private withdrawAbi = require('../../../assets/abis/WithdrawMVTToken.json');

  constructor() {
    // Initialize provider when MetaMask is available
    if (window.ethereum) {
      this.provider = new ethers.providers.Web3Provider(window.ethereum);
      this.initContracts();
    }
  }

  private initContracts(): void {
    const signer = this.provider.getSigner();
    this.mvtContract = new ethers.Contract(this.mvtAddress, this.mvtAbi, signer);
    this.withdrawContract = new ethers.Contract(this.withdrawAddress, this.withdrawAbi, signer);
  }

  // Get MVT Balance
  getMVTBalance(address: string): Observable<string> {
    return from(this.mvtContract.getMVTBalance(address).then(
      (balance: ethers.BigNumber) => ethers.utils.formatUnits(balance, 18)
    ));
  }

  // Deposit USDC
  depositUSDC(amount: number): Observable<any> {
    const parsedAmount = ethers.utils.parseUnits(amount.toString(), 6); // USDC has 6 decimals
    return from(this.withdrawContract.depositUSDC(parsedAmount));
  }

  // Request swap MVT for USDC
  requestSwap(amount: number): Observable<any> {
    const parsedAmount = ethers.utils.parseUnits(amount.toString(), 18); // MVT has 18 decimals
    return from(this.withdrawContract.requestSwap(parsedAmount));
  }

  // Get exchange rate
  getExchangeRate(): Observable<number> {
    return from(this.withdrawContract.getExchangeRate().then(
      (rate: ethers.BigNumber) => Number(ethers.utils.formatUnits(rate, 18))
    ));
  }

  // Admin: Approve swap
  approveSwap(requestId: number): Observable<any> {
    return from(this.withdrawContract.approveSwap(requestId));
  }
}
```

### Implementing in Components

Example MVT-Token component usage:

```typescript
import { Component, OnInit } from '@angular/core';
import { TokenService } from '../../shared/services/token.service';

@Component({
  selector: 'app-mvt-token',
  templateUrl: './mvt-token.component.html'
})
export class MvtTokenComponent implements OnInit {
  walletAddress: string = '';
  mvtBalance: number = 0;
  usdcAmount: number = 0;
  isDepositingUSDC: boolean = false;
  
  constructor(private tokenService: TokenService) {}
  
  ngOnInit() {
    // Get wallet address from MetaMask
    window.ethereum.request({ method: 'eth_requestAccounts' })
      .then(accounts => {
        this.walletAddress = accounts[0];
        this.fetchMVTBalance();
      });
  }
  
  fetchMVTBalance() {
    this.tokenService.getMVTBalance(this.walletAddress)
      .subscribe({
        next: (balance) => {
          this.mvtBalance = parseFloat(balance);
        },
        error: (error) => {
          console.error('Failed to fetch MVT balance:', error);
        }
      });
  }
  
  depositUSDC() {
    this.isDepositingUSDC = true;
    this.tokenService.depositUSDC(this.usdcAmount)
      .subscribe({
        next: (tx) => {
          console.log('USDC deposit transaction:', tx);
          // Reset after successful transaction
          this.usdcAmount = 0;
          this.isDepositingUSDC = false;
        },
        error: (error) => {
          console.error('Failed to deposit USDC:', error);
          this.isDepositingUSDC = false;
        }
      });
  }
}
```

## Admin Operations

### Contract Initialization

After deployment, the admin should:

1. Mint an initial supply of MVT tokens to the MVT Token contract:
```javascript
const mvtContract = new ethers.Contract(mvtAddress, mvtAbi, adminSigner);
const initialSupply = ethers.utils.parseUnits("1000000", 18); // 1 million tokens
await mvtContract.mint(initialSupply);
```

2. Transfer some USDC to the WithdrawMVTToken contract to provide liquidity for swaps.

### Regular Maintenance

The following operations should be performed regularly:

1. **Monitor Swap Requests**: Check for new swap requests and approve them
2. **Maintain Liquidity**: Ensure sufficient USDC and MVT balances in the contracts
3. **Update Exchange Rates**: If using a fixed exchange rate, update it as needed

## Monitoring and Management

### Key Metrics to Monitor

1. Total MVT supply
2. MVT and USDC contract balances
3. Number of pending swap requests
4. Transaction volume (deposits and withdrawals)

### Management Dashboard

Consider building an admin dashboard with the following features:

1. **Token Stats**: Display total supply, circulation, and contract balances
2. **Swap Request Management**: List and process swap requests
3. **User Balances**: View token balances for platform users
4. **Transaction History**: Display recent transactions

---

This quick-start guide provides an overview of key functionality and implementation details. For comprehensive deployment instructions, please refer to the DEPLOYMENT.md document. 