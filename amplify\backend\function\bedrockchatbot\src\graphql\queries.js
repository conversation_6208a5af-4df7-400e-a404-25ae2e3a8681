const { gql } = require("@apollo/client/core");

const GET_USER_FOR_COMMUNITY = gql`
  query GetUser($id: ID!) {
    getUser(id: $id) {
      id
      givenName
      familyName
      userAssociations(
        filter: { isDeleted: { eq: "false" }, status: { eq: true } }
      ) {
        items {
          type
          createdAt
          organization {
            id
            name
            imageUrl
            cityId
            membership {
              currentImpactScore
              MVPTokens
              fundTokens
              id
            }
          }
        }
      }
    }
  }
`;

const GET_USER_FOR_FAMILY = gql`
  query GetUser($id: ID!) {
    getUser(id: $id) {
      id
      givenName
      familyName
      associations(limit: 100000) {
        items {
          id
        }
      }
      userAssociations(filter: {type: {eq: "Person"}, status: {eq: true}}) {
        items {
          cityId
          id
          relationType
          type
          otherPerson {
            givenName
            familyName
            gender
            id
          }
        }
      }
    }
  }
`;

const GET_USER_FOR_KNOWLEDGE = gql`
  query SubmissionByDate($filter: ModelSubmissionFilterInput) {
    SubmissionByDate(isDeleted: "false", filter: $filter, limit: 100000) {
      items {
        id
        text
        submissionStatus  # Approved, In-review, Denied
        createdAt         # Last submitted knowledge
        projectType       # Type of knowledge submitted
        memberId
        organization {
          organizationUser {
            name          # Submission of organizations
          }
        }
        homework {
          microcredential {
            categoryData {
              name        # Category-wise count
            }
          }
        }
      }
    }
  }
`;

const GET_USER_DETAILS_FOR_KNOWLEDGE = gql`
  query GetUserDetailsForKnowledge($id: ID!) {
    getUser(id: $id) {
      personId: id
      associatedOrganizationIds: userAssociations(
        filter: {
          isDeleted: { eq: "false" },
          status: { eq: true },
          organizationID: { ne: null }
        }
      ) {
        items {
          organizationId: organizationID
        }
      }
      associatedPersonIds: userAssociations(
        filter: {
          isDeleted: { eq: "false" },
          status: { eq: true },
          otherPersonsID: { ne: null }
        }
      ) {
        items {
          otherPersonId: otherPersonsID
        }
      }
    }
  }
`;

const GET_KNOWLEDGE_REPOSITORY_STORE = gql`
  query ListKnowledgeRepositoryStores(
    $filter: ModelKnowledgeRepositoryStoreFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listKnowledgeRepositoryStores(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        entityType
        transcriptDetail {
          transcription
          summary
          metadata
        }
        fileType          # Type of knowledge whether it's video or audio
        durationInMinutes
        createdAt         # Last submitted knowledge
        userId
        organizationId
        isPublic
      }
    }
  }
`;

const GET_USER_FOR_UNIFY = gql`
  query GetUserForUnify($id: ID!) {
    getUser(id: $id) {
      id
      givenName
      familyName
      # Get all associations for both family and community use cases
      associations(limit: 100000) {
        items {
          id
        }
      }
      # Get all user associations with a filter that works for both use cases
      userAssociations(filter: {isDeleted: {eq: "false"}, status: {eq: true}}) {
        items {
          type
          relationType
          cityId
          id
          createdAt
          # Include organization data for community-related queries
          organization {
            id
            name
            imageUrl
            cityId
            membership {
              currentImpactScore
              MVPTokens
              fundTokens
              id
            }
          }
          # Include person data for family-related queries
          otherPerson {
            givenName
            familyName
            gender
            id
          }
        }
      }
    }
  }
`;

module.exports = {
  GET_USER_FOR_COMMUNITY,
  GET_USER_FOR_FAMILY,
  GET_USER_FOR_KNOWLEDGE,
  GET_USER_DETAILS_FOR_KNOWLEDGE,
  GET_KNOWLEDGE_REPOSITORY_STORE,
  GET_USER_FOR_UNIFY
};
