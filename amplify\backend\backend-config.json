{"api": {"getUserDetail": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "getUserDetailFun"}], "providerPlugin": "awscloudformation", "service": "API Gateway"}, "mobileGameAPI": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "mobileGameAPIFunction"}], "providerPlugin": "awscloudformation", "service": "API Gateway"}, "myvillageprojectadmi": {"dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}], "output": {"authConfig": {"additionalAuthenticationProviders": [{"authenticationType": "AWS_IAM"}], "defaultAuthentication": {"authenticationType": "AMAZON_COGNITO_USER_POOLS", "userPoolConfig": {"userPoolId": "authmyvillageprojectadmifeb4ea87"}}}, "securityType": "AMAZON_COGNITO_USER_POOLS"}, "providerPlugin": "awscloudformation", "service": "AppSync"}}, "auth": {"myvillageprojectadmifeb4ea87": {"customAuth": false, "dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": [], "passwordPolicyMinLength": 8}, "signupAttributes": ["EMAIL", "FAMILY_NAME", "GIVEN_NAME", "NAME", "PHONE_NUMBER"], "socialProviders": [], "usernameAttributes": ["EMAIL"], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito"}}, "function": {"bedrockchatbot": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "chat": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "customQueryFunction": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "getUserDetailFun": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "graphqlCustomFunction": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}, {"attributes": ["BucketName"], "category": "storage", "resourceName": "s3myvillageprojectadminportalorgprofilelogo"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "importDataFunction": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}, {"attributes": ["BucketName"], "category": "storage", "resourceName": "s3myvillageprojectadminportalorgprofilelogo"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "mobileGameAPIFunction": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "mvtToken": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "mvtWallet": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "notificationFunction": {"build": true, "dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}, {"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "sendEmail": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "usersManagementFunction": {"build": true, "dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillageprojectadmifeb4ea87"}, {"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "myvillageprojectadmi"}], "providerPlugin": "awscloudformation", "service": "Lambda"}}, "parameters": {"AMPLIFY_function_bedrockchatbot_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "bedrockchatbot"}]}, "AMPLIFY_function_bedrockchatbot_s3Key": {"usedBy": [{"category": "function", "resourceName": "bedrockchatbot"}]}, "AMPLIFY_function_chat_accessKeyId": {"usedBy": [{"category": "function", "resourceName": "chat"}]}, "AMPLIFY_function_chat_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "chat"}]}, "AMPLIFY_function_chat_s3Key": {"usedBy": [{"category": "function", "resourceName": "chat"}]}, "AMPLIFY_function_chat_secretAccessKey": {"usedBy": [{"category": "function", "resourceName": "chat"}]}, "AMPLIFY_function_customQueryFunction_accessKeyId": {"usedBy": [{"category": "function", "resourceName": "customQueryFunction"}]}, "AMPLIFY_function_customQueryFunction_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "customQueryFunction"}]}, "AMPLIFY_function_customQueryFunction_platformArn": {"usedBy": [{"category": "function", "resourceName": "customQueryFunction"}]}, "AMPLIFY_function_customQueryFunction_s3Key": {"usedBy": [{"category": "function", "resourceName": "customQueryFunction"}]}, "AMPLIFY_function_customQueryFunction_secretAccessKey": {"usedBy": [{"category": "function", "resourceName": "customQueryFunction"}]}, "AMPLIFY_function_getUserDetailFun_accessKeyId": {"usedBy": [{"category": "function", "resourceName": "getUserDetailFun"}]}, "AMPLIFY_function_getUserDetailFun_apiKey": {"usedBy": [{"category": "function", "resourceName": "getUserDetailFun"}]}, "AMPLIFY_function_getUserDetailFun_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "getUserDetailFun"}]}, "AMPLIFY_function_getUserDetailFun_s3Key": {"usedBy": [{"category": "function", "resourceName": "getUserDetailFun"}]}, "AMPLIFY_function_getUserDetailFun_secretAccessKey": {"usedBy": [{"category": "function", "resourceName": "getUserDetailFun"}]}, "AMPLIFY_function_getUserDetailFun_stripeWebhookSecret": {"usedBy": [{"category": "function", "resourceName": "getUserDetailFun"}]}, "AMPLIFY_function_getUserDetailFun_webClientId": {"usedBy": [{"category": "function", "resourceName": "getUserDetailFun"}]}, "AMPLIFY_function_graphqlCustomFunction_accessKeyId": {"usedBy": [{"category": "function", "resourceName": "graphqlCustomFunction"}]}, "AMPLIFY_function_graphqlCustomFunction_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "graphqlCustomFunction"}]}, "AMPLIFY_function_graphqlCustomFunction_s3Key": {"usedBy": [{"category": "function", "resourceName": "graphqlCustomFunction"}]}, "AMPLIFY_function_graphqlCustomFunction_secretAccessKey": {"usedBy": [{"category": "function", "resourceName": "graphqlCustomFunction"}]}, "AMPLIFY_function_importDataFunction_accessKeyId": {"usedBy": [{"category": "function", "resourceName": "importDataFunction"}]}, "AMPLIFY_function_importDataFunction_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "importDataFunction"}]}, "AMPLIFY_function_importDataFunction_s3Key": {"usedBy": [{"category": "function", "resourceName": "importDataFunction"}]}, "AMPLIFY_function_importDataFunction_secretAccessKey": {"usedBy": [{"category": "function", "resourceName": "importDataFunction"}]}, "AMPLIFY_function_mobileGameAPIFunction_accessKeyId": {"usedBy": [{"category": "function", "resourceName": "mobileGameAPIFunction"}]}, "AMPLIFY_function_mobileGameAPIFunction_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "mobileGameAPIFunction"}]}, "AMPLIFY_function_mobileGameAPIFunction_s3Key": {"usedBy": [{"category": "function", "resourceName": "mobileGameAPIFunction"}]}, "AMPLIFY_function_mobileGameAPIFunction_secretAccessKey": {"usedBy": [{"category": "function", "resourceName": "mobileGameAPIFunction"}]}, "AMPLIFY_function_mvtToken_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "mvtToken"}]}, "AMPLIFY_function_mvtToken_etherscanApiKey": {"usedBy": [{"category": "function", "resourceName": "mvtToken"}]}, "AMPLIFY_function_mvtToken_mvtContractAddress": {"usedBy": [{"category": "function", "resourceName": "mvtToken"}]}, "AMPLIFY_function_mvtToken_mvtUsdcContractAddress": {"usedBy": [{"category": "function", "resourceName": "mvtToken"}]}, "AMPLIFY_function_mvtToken_mvtWithdrawContractAddress": {"usedBy": [{"category": "function", "resourceName": "mvtToken"}]}, "AMPLIFY_function_mvtToken_privateKey": {"usedBy": [{"category": "function", "resourceName": "mvtToken"}]}, "AMPLIFY_function_mvtToken_rpcUrl": {"usedBy": [{"category": "function", "resourceName": "mvtToken"}]}, "AMPLIFY_function_mvtToken_s3Key": {"usedBy": [{"category": "function", "resourceName": "mvtToken"}]}, "AMPLIFY_function_mvtToken_stripeSecretKey": {"usedBy": [{"category": "function", "resourceName": "mvtToken"}]}, "AMPLIFY_function_mvtWallet_accessKeyId": {"usedBy": [{"category": "function", "resourceName": "mvtWallet"}]}, "AMPLIFY_function_mvtWallet_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "mvtWallet"}]}, "AMPLIFY_function_mvtWallet_ethereumRpcUrl": {"usedBy": [{"category": "function", "resourceName": "mvtWallet"}]}, "AMPLIFY_function_mvtWallet_mvtUsdcContractAddress": {"usedBy": [{"category": "function", "resourceName": "mvtWallet"}]}, "AMPLIFY_function_mvtWallet_mvtWithdrawContractAddress": {"usedBy": [{"category": "function", "resourceName": "mvtWallet"}]}, "AMPLIFY_function_mvtWallet_privateKey": {"usedBy": [{"category": "function", "resourceName": "mvtWallet"}]}, "AMPLIFY_function_mvtWallet_region": {"usedBy": [{"category": "function", "resourceName": "mvtWallet"}]}, "AMPLIFY_function_mvtWallet_s3Key": {"usedBy": [{"category": "function", "resourceName": "mvtWallet"}]}, "AMPLIFY_function_mvtWallet_secretAccessKey": {"usedBy": [{"category": "function", "resourceName": "mvtWallet"}]}, "AMPLIFY_function_mvtWallet_stripeSecretKey": {"usedBy": [{"category": "function", "resourceName": "mvtWallet"}]}, "AMPLIFY_function_notificationFunction_accessKeyId": {"usedBy": [{"category": "function", "resourceName": "notificationFunction"}]}, "AMPLIFY_function_notificationFunction_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "notificationFunction"}]}, "AMPLIFY_function_notificationFunction_s3Key": {"usedBy": [{"category": "function", "resourceName": "notificationFunction"}]}, "AMPLIFY_function_notificationFunction_secretAccessKey": {"usedBy": [{"category": "function", "resourceName": "notificationFunction"}]}, "AMPLIFY_function_sendEmail_accessKeyId": {"usedBy": [{"category": "function", "resourceName": "sendEmail"}]}, "AMPLIFY_function_sendEmail_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "sendEmail"}]}, "AMPLIFY_function_sendEmail_s3Key": {"usedBy": [{"category": "function", "resourceName": "sendEmail"}]}, "AMPLIFY_function_sendEmail_secretAccessKey": {"usedBy": [{"category": "function", "resourceName": "sendEmail"}]}, "AMPLIFY_function_usersManagementFunction_accessKeyId": {"usedBy": [{"category": "function", "resourceName": "usersManagementFunction"}]}, "AMPLIFY_function_usersManagementFunction_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "usersManagementFunction"}]}, "AMPLIFY_function_usersManagementFunction_s3Key": {"usedBy": [{"category": "function", "resourceName": "usersManagementFunction"}]}, "AMPLIFY_function_usersManagementFunction_secretAccessKey": {"usedBy": [{"category": "function", "resourceName": "usersManagementFunction"}]}}, "storage": {"s3myvillageprojectadminportalorgprofilelogo": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "S3"}}}