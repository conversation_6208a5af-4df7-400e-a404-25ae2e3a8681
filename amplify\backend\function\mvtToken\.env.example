API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT=https://<graphqlEndpoint>.appsync-api.<regionName>.amazonaws.com/graphql
AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID=<userPoolId>
API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT=<graphqlApiIdOutput>
STORAGE_S3MYVILLAGEPROJECTADMINPORTALORGPROFILELOGO_BUCKETNAME=<bucketName>
AWS_REGION=<regionName>
ACCESS_KEY_ID=<accessKeyId>
SECRET_ACCESS_KEY=<secretAccessKey>
ENV=<awsEnvironmentName>

# Blockchain Configuration
RPC_URL=<blockchainRpcUrl>
PRIVATE_KEY=<blockchainPrivateKey>
MVT_CONTRACT_ADDRESS=<blockchainMvtContractAddress>
MVT_WITHDRAW_CONTRACT_ADDRESS=<blockchainMvtWithdrawContractAddress>
MVT_USDC_CONTRACT_ADDRESS=<blockchainMvtUsdcContractAddress>
ETHERSCAN_API_KEY=<blockchainEtherscanApiKey>

# Stripe Configuration
STRIPE_SECRET_KEY=<stripeSecretKey>
STRIPE_WEBHOOK_SECRET=<stripeWebhookSecret>
FRONTEND_URL=<frontendUrl>

# Stripe Crypto Onramp Configuration
# This is required for the Crypto Onramp integration
STRIPE_ONRAMP_WEBHOOK_SECRET=<stripeOnrampWebhookSecret>
