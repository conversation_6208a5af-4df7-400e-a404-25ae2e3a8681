/**
 * Standardized Error Response Utilities for Bedrock Chatbot
 * Provides GraphQL-compliant error formatting across all operations
 */

/**
 * Error categories for standardized error classification
 */
const ERROR_CATEGORIES = {
  VALIDATION: 'Validation',
  AUTHORIZATION: 'Authorization',
  DYNAMODB: 'DynamoDB',
  BLOCKCHAIN: 'Blockchain',
  NETWORK: 'Network',
  BUSINESS_LOGIC: 'BusinessLogic',
  SYSTEM: 'System',
  EXTERNAL_SERVICE: 'ExternalService',
};

/**
 * Specific error types within categories
 */
const ERROR_TYPES = {
  INVALID_INPUT: 'InvalidInput',
  MISSING_REQUIRED_FIELD: 'MissingRequiredField',
  INVALID_FORMAT: 'InvalidFormat',
  OUT_OF_RANGE: 'OutOfRange',
  UNAUTHORIZED: 'Unauthorized',
  FORBIDDEN: 'Forbidden',
  INVALID_TOKEN: 'InvalidToken',
  EXPIRED_SESSION: 'ExpiredSession',
  DYNAMODB_EXCEPTION: 'DynamoDbException',
  RESOURCE_NOT_FOUND: 'ResourceNotFound',
  CONDITIONAL_CHECK_FAILED: 'ConditionalCheckFailed',
  THROTTLING: 'Throttling',
  TRANSACTION_FAILED: 'TransactionFailed',
  INSUFFICIENT_FUNDS: 'InsufficientFunds',
  CONTRACT_ERROR: 'ContractError',
  NETWORK_ERROR: 'NetworkError',
  INSUFFICIENT_BALANCE: 'InsufficientBalance',
  INVALID_OPERATION: 'InvalidOperation',
  RESOURCE_LOCKED: 'ResourceLocked',
  OPERATION_NOT_ALLOWED: 'OperationNotAllowed',
  TIMEOUT: 'Timeout',
  SERVICE_UNAVAILABLE: 'ServiceUnavailable',
  INTERNAL_ERROR: 'InternalError',
  CONFIGURATION_ERROR: 'ConfigurationError',
};

module.exports = {
  ERROR_CATEGORIES,
  ERROR_TYPES,
};
