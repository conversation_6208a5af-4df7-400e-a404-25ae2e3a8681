/* Amplify Params - DO NOT EDIT
  API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT
  API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT
  AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID
  ENV
  REGION
Amplify Params - DO NOT EDIT */

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

const { createHttpLink } = require("apollo-link-http");
const {
  ApolloClient,
  ApolloError,
  InMemoryCache,
} = require("@apollo/client/core");
const fetch = require("node-fetch");
const AWS = require("aws-sdk");

AWS.config.update({
  maxRetries: 3,
  httpOptions: { timeout: 30000, connectTimeout: 5000 },
  region: process.env.REGION,
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY,
});
const ddb = new AWS.DynamoDB();
const lambda = new AWS.Lambda();

/*Initializing CognitoIdentityServiceProvider from AWS SDK JS*/
const cognito = new AWS.CognitoIdentityServiceProvider({
  apiVersion: "2016-04-18",
});
const USERPOOLID = process.env.AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID;
const graphqlEndpoint =
  process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT;

console.log("graphqlEndpoint: ", graphqlEndpoint);

// Amazon Lex
const lexruntime = new AWS.LexRuntimeV2({ region: process.env.REGION });

const {
  GET_MEMBER_ASSIGNMENTS,
  GET_ASSOCIATED_DATA,
  UPDATE_USER,
  GET_USER,
  UPDATE_MEMBERSHIP,
  DELETE_MEMBERSHIP,
  DELETE_USER,
  GET_USER_ASSIGNMENTS,
  GET_USER_SUBMISSIONS,
  GET_MEMBERSHIP_LIST,
  GET_EVENTS_LIST,
  GET_HOMEWORK,
  CREATE_HOMEWORK_ORGANIZATIONS,
  CREATE_HOMEWORK_USERS,
  GET_MICROCREDENTIAL,
  GET_MEMBER,
  GET_LIST_HOMEWORK,
  UPDATE_KNOWLEDGE_REPOSITORY_STORE,
  UPDATE_SUBMISSION,
  CREATE_TRANSCRIPT_DETAIL,
} = require("./graphql.queries.js");
const { respondWithBedrockChatBot } = require("./services/bedrockchat.js");
const {
  storeChatbotPromptInDynamoDB,
  getChatbotAnswer,
  removeChatbotPromptFromDynamoDB,
} = require("./services/chatbot.js");
const { removeCityData } = require("./services/city-remove.js");
const { transferData } = require("./services/city-transfer.js");
const { transferCategoryData } = require("./services/category-transfer.js");
const { transcribeFile } = require("./services/transcribe.js");

exports.handler = async (event, context, callback) => {
  try {
    console.log("event.arguments?", event?.request?.headers?.authorization);
    let authorization = event?.request?.headers?.authorization;

    const link = createHttpLink({
      uri: graphqlEndpoint,
      fetch: fetch,
      headers: {
        Authorization: `${authorization}`,
      },
    });
    console.log("link", link.request);

    const apolloClient = new ApolloClient({
      link,
      cache: new InMemoryCache(),
    });

    switch (event.fieldName) {
      case "getMemberAssignments": {
        console.log("getMemberAssignments");
        const memberId = event.arguments?.memberId;
        const assignmentType = event.arguments?.assignmentType;
        const cityId = event.arguments?.cityId;
        const filterWithUser = { studentStakeholderId: { eq: memberId } };
        const filterWithOrg = { memberId: { eq: memberId } };
        const filterWithCity = { cityId: { eq: cityId } };

        const response = await apolloClient.query({
          query: GET_MEMBER_ASSIGNMENTS,
          variables: {
            filterUser: filterWithUser,
            filterOrg: filterWithOrg,
            filterWithCity: filterWithCity,
          },
        });

        const filterItems = (items) =>
          items.filter((ex) => {
            const homeworkExists = ex.homeworkData !== null;
            const memberExists =
              ex.studentStakeholderData !== null || ex.memberData !== null;

            if (assignmentType !== "all") {
              return (
                homeworkExists &&
                memberExists &&
                ex.homeworkData.assignmentType === assignmentType
              );
            } else if (
              ex.homeworkData &&
              ex.homeworkData.homeworkAutoGeneratedFrom !== "EVENT" &&
              ex.homeworkData.createdBy !== memberId
            ) {
              return homeworkExists && memberExists;
            }
          });

        const filteredHomeworkUsers = filterItems(
          response.data.listHomeworkUsers.items
        );
        const filteredHomeworkOrganizations = filterItems(
          response.data.listHomeworkOrganizations.items
        );

        const filteredResponse = {
          data: {
            listHomeworkUsers: { items: filteredHomeworkUsers },
            listHomeworkOrganizations: { items: filteredHomeworkOrganizations },
          },
        };

        console.log("GraphQL response:", filteredHomeworkUsers);
        callback(null, filteredResponse);
        break;
      }

      case "getAssignmentListMobile": {
        try {
          console.log("getAssignmentListMobile");
          let filteredHomeworkUsers;
          let filteredHomeworkOrganizations;
          const cityId = event.arguments?.cityId;
          let availableAssignment = [];
          const memberId = event.arguments?.memberId;
          const gender = event.arguments?.gender;
          const type =
            event.arguments?.memberType == "Organization"
              ? "member"
              : "stakeholder";
          const assignmentType = event.arguments?.assignmentType;
          await Promise.all(
            cityId.map(async (cityId) => {
              console.log("type", type);
              const filterWithUser = { studentStakeholderId: { eq: memberId } };
              const filterWithOrg = { memberId: { eq: memberId } };
              const filterWithCity = { cityId: { eq: cityId } };

              const response = await apolloClient.query({
                query: GET_MEMBER_ASSIGNMENTS,
                variables: {
                  filterUser: filterWithUser,
                  filterOrg: filterWithOrg,
                  filterWithCity: filterWithCity,
                },
              });

              const filterItems = (items) =>
                items.filter((ex) => {
                  const homeworkExists = ex.homeworkData !== null;
                  const memberExists =
                    ex.studentStakeholderData !== null ||
                    ex.memberData !== null;

                  if (assignmentType !== "all") {
                    if (
                      ex.homeworkData &&
                      ex.homeworkData.homeworkAutoGeneratedFrom !== "EVENT" &&
                      ex.homeworkData.createdBy !== memberId
                    ) {
                      return (
                        homeworkExists &&
                        memberExists &&
                        ex.homeworkData.assignmentType === assignmentType
                      );
                    }
                    return true;
                  } else {
                    if (
                      ex.homeworkData &&
                      ex.homeworkData.homeworkAutoGeneratedFrom !== "EVENT" &&
                      ex.homeworkData.createdBy !== memberId
                    ) {
                      return homeworkExists && memberExists;
                    }
                    return true;
                  }
                });

              filteredHomeworkUsers = filterItems(
                response.data.listHomeworkUsers.items
              );
              filteredHomeworkOrganizations = filterItems(
                response.data.listHomeworkOrganizations.items
              );

              const currentDate = new Date();
              const currentTime = new Date().toISOString();
              const pastDate = new Date();
              pastDate.setDate(currentDate.getDate() - 30);
              let filter = {
                and: [
                  { dueDate: { gt: currentTime } },
                  { cityId: { eq: cityId } },
                  { homeworkAutoGeneratedFrom: { ne: "EVENT" } },
                  { selfAssignment: { eq: true } },
                  { entityType: { eq: type } },
                ],
              };

              try {
                const { data } = await apolloClient.query({
                  query: GET_LIST_HOMEWORK,
                  variables: {
                    isDeleted: "false",
                    createdAt: { ge: pastDate.toISOString() },
                    sortDirection: "DESC",
                    filter: filter,
                  },
                });
                let homeworkData = data.homeworkByDate.items;

                if (gender) {
                  homeworkData = homeworkData.filter((item) => {
                    const itemGender = item.microcredential?.gender;

                    if (itemGender === "BOTH") {
                      return gender === "MALE" || gender === "FEMALE";
                    }

                    return itemGender === gender;
                  });
                }

                availableAssignment = homeworkData.map((homework) => {
                  let homeworkData = {
                    ...homework,
                    rating: 0,
                  };
                  return {
                    homeworkData,
                    homeworkStatus: null,
                  };
                });
              } catch (error) {
                console.error(`Failed to fetch homework for id: `, error);
              }
            })
          );

          const filteredResponseOfCurrentAssignments = {
            data: {
              listHomeworkUsers: { items: filteredHomeworkUsers },
              listHomeworkOrganizations: {
                items: filteredHomeworkOrganizations,
              },
              listAvailableAssignments: { items: availableAssignment },
            },
          };
          callback(null, filteredResponseOfCurrentAssignments ?? {});
        } catch (error) {
          console.error(error);
          return {
            statusCode: 500,
            body: JSON.stringify({ error: "An error occurred" }),
          };
        }
        break;
      }

      case "memberChatAssistant": {
        console.log("memberChatAssistant");
        const { userId, username, question } = event.arguments;

        const lexParams = {
          botAliasId: "TSTALIASID",
          botId: "SEOSRTVYBA", // Replace with your Lex V2 bot ID
          localeId: "en_US",
          sessionId: userId,
          text: question,
        };

        try {
          const lexResponse = await lexruntime
            .recognizeText(lexParams)
            .promise(); // Use recognizeText method to interact with the bot

          // Process Lex response (intent, slots, sessionState, etc.)
          const { messages, sessionState } = lexResponse;

          if (sessionState?.intent?.state === "ReadyForFulfillment") {
            // Handle fulfilled intent, e.g., access data
            switch (sessionState?.intent?.name) {
              case "WelcomeIntent": {
                return {
                  User: question,
                  Assistant:
                    messages[0].content || `Hello, how can I assist you?`,
                };
              }
              case "OrganizationIntent": {
                const associatedData = await getAssociatedData(
                  userId,
                  apolloClient,
                  "organizations"
                );
                return respondWithAssociatedData(
                  associatedData,
                  question,
                  "organizations"
                );
              }
              case "StudentAssociationIntent": {
                const associatedData = await getAssociatedData(
                  userId,
                  apolloClient,
                  "students"
                );
                return respondWithAssociatedData(
                  associatedData,
                  question,
                  "students"
                );
              }
              case "StakeholderAssociationIntent": {
                const associatedData = await getAssociatedData(
                  userId,
                  apolloClient,
                  "stakeholders"
                );
                return respondWithAssociatedData(
                  associatedData,
                  question,
                  "stakeholders"
                );
              }
              case "AddNewStudentIntent": {
                const loginUserData = await getUserById(userId);
                let userData = {
                  givenName:
                    sessionState?.intent?.slots?.firstName?.value
                      ?.resolvedValues[0] || "",
                  familyName:
                    sessionState?.intent?.slots?.lastName?.value
                      ?.resolvedValues[0] || "",
                  birthday:
                    sessionState?.intent?.slots?.birthday?.value
                      ?.resolvedValues[0] || "",
                  email:
                    sessionState?.intent?.slots?.email?.value
                      ?.resolvedValues[0] || "",
                  gender:
                    sessionState?.intent?.slots?.gender?.value
                      ?.resolvedValues[0] || "",
                  phoneNumber:
                    sessionState?.intent?.slots?.phoneNumber?.value
                      ?.resolvedValues[0] || "",
                  ethnicity:
                    sessionState?.intent?.slots?.ethnicity?.value
                      ?.resolvedValues[0] || "",
                  cityId:
                    loginUserData?.cityId ||
                    "8f6c4c07-dd62-480d-a376-ef221133f0e2",
                };
                if (userData) {
                  const userResponse = await createUserLambdaInvoke(
                    userData,
                    userId,
                    username
                  );
                  return {
                    User: question,
                    Assistant: `${userResponse}`,
                  };
                } else {
                  return {
                    User: question,
                    Assistant: `Some issue occured while creating this student.`,
                  };
                }
              }
              case "EditUserIntent": {
                let action =
                  sessionState?.intent?.slots?.action?.value
                    ?.resolvedValues[0] || "";
                let value =
                  sessionState?.intent?.slots?.updatedValue?.value
                    ?.resolvedValues[0] || "";

                const { statusCode, message } = await userDataValidation(
                  action,
                  value
                );
                if (statusCode === 200) {
                  let input = {
                    id: userId,
                    givenName: action === "first name" ? value : "",
                    familyName: action === "last name" ? value : "",
                    birthday: action === "birthday" ? value : "",
                    ethnicity: action === "ethnicity" ? value : "",
                    gender: action === "gender" ? value : "NONE",
                  };
                  const updatedUser = await updateUserFun(input, apolloClient);
                  if (updatedUser) {
                    return {
                      User: question,
                      Assistant: `Your detail updated successfully.`,
                    };
                  } else {
                    return {
                      User: question,
                      Assistant: `There are some issues while updating your information.`,
                    };
                  }
                } else {
                  return {
                    User: question,
                    Assistant: `${message}`,
                  };
                }
              }
              case "RetrieveUserInfoIntent": {
                let retrieve =
                  sessionState?.intent?.slots?.retrieve?.value
                    ?.resolvedValues[0] || "";
                const message = await getUserData(retrieve, userId);
                if (message && message !== "") {
                  return {
                    User: question,
                    Assistant: `${message}`,
                  };
                } else {
                  return {
                    User: question,
                    Assistant: `Unable to fetch your information.`,
                  };
                }
              }
              case "CategoryDataIntent": {
                const category =
                  sessionState?.intent?.slots?.category?.value
                    ?.resolvedValues[0] || "";
                const orgData = await eventEntityCategoryInfoFun(
                  apolloClient,
                  category,
                  "categoryData"
                );
                return respondWithEventEntityCategory(
                  orgData,
                  question,
                  category
                );
              }
              case "FallbackIntent": {
                return {
                  User: question,
                  Assistant: "I can only help with the association topics.",
                };
              }
              default:
                return {
                  User: question,
                  Assistant:
                    "I can't assist you with that yet, but I'm learning!",
                };
            }
          } else {
            // Handle ongoing conversation with Lex (e.g., elicit slots)
            if (
              sessionState?.intent?.state === "InProgress" &&
              sessionState?.intent?.name === "AddNewStudentIntent"
            ) {
              return {
                User: question,
                Assistant:
                  messages[0]?.content || "Invalid input for student create.",
              };
            } else if (
              sessionState?.intent?.state === "InProgress" &&
              sessionState?.intent?.name === "EditUserIntent"
            ) {
              return {
                User: question,
                Assistant:
                  messages[0]?.content || "Invalid input for update data.",
              };
            } else if (sessionState?.intent?.state === "InProgress") {
              return {
                User: question,
                Assistant:
                  messages[0]?.content || "Please enter valid information.",
              };
            } else {
              return {
                User: question,
                Assistant: "I can only help with the association topics.",
              };
            }
          }
        } catch (err) {
          console.error(err);
          return {
            statusCode: 500,
            body: JSON.stringify({ error: "An error occurred" }),
          };
        }
      }
      case "updateUserDetail": {
        console.log("updateUserDetail");
        let input = event?.arguments?.input;

        await updateUserFun(input, apolloClient);

        callback(null, {
          message: "User successfully updated.",
          statusCode: 200,
        });
        break;
      }
      case "deleteUserDetail": {
        console.log("deleteUserDetail");
        const { userId } = event?.arguments?.input;

        // Get user details
        const getUser = await apolloClient.query({
          query: GET_USER,
          variables: {
            id: userId,
          },
        });
        const userData = getUser.data.getUser;
        if (userData) {
          // Delete user relationship data
          let homeworkRelationIds = [];
          userData?.homeworks?.items?.map((homeworkData) => {
            homeworkRelationIds.push(homeworkData.id);
          });
          if (homeworkRelationIds.length > 0) {
            const HomeworkUsersTableName = `HomeworkUsers-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
            await Promise.all([
              deleteItems(HomeworkUsersTableName, homeworkRelationIds),
            ]);
          }

          let associationIds = [];
          if (userData?.associations?.items?.length > 0) {
            userData?.associations?.items.forEach((associationData) => {
              associationIds?.push(associationData?.id);
            });
          }
          if (userData?.userAssociations?.items?.length > 0) {
            userData?.userAssociations?.items.forEach((associationData) => {
              associationIds?.push(associationData?.id);
            });
          }
          if (associationIds.length > 0) {
            const associationTable = `Association-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
            await Promise.all([deleteItems(associationTable, associationIds)]);
          }

          // Delete cognito user
          const cognitoParams = {
            UserPoolId: USERPOOLID,
            Username: userId,
          };
          await cognito.adminDeleteUser(cognitoParams).promise();

          // Update Membership
          const membershipUpdate = {
            id: userData?.membership?.id,
            isDeleted: "true",
            _version: userData?.membership?._version,
          };
          const updateMembership = await apolloClient.mutate({
            mutation: UPDATE_MEMBERSHIP,
            variables: {
              input: membershipUpdate,
            },
          });
          const updatedMembership = updateMembership?.data?.updateMembership;

          let deleteMembership = {
            id: updatedMembership?.id,
            _version: updatedMembership?._version,
          };
          // Delete Membership
          await apolloClient.mutate({
            mutation: DELETE_MEMBERSHIP,
            variables: {
              input: deleteMembership,
            },
          });

          // Update user
          const updateUserData = {
            id: userData?.id,
            isDeleted: "true",
            _version: userData?._version,
          };
          const updateUser = await apolloClient.mutate({
            mutation: UPDATE_USER,
            variables: {
              input: updateUserData,
            },
          });
          const updatedUser = updateUser?.data?.updateUser;

          let deleteUser = {
            id: updatedUser?.id,
            _version: updatedUser?._version,
          };
          // Delete User
          await apolloClient.mutate({
            mutation: DELETE_USER,
            variables: {
              input: deleteUser,
            },
          });
        }
        callback(null, {
          message: "User successfully deleted.",
          statusCode: 200,
        });
        break;
      }
      case "userTokensChat": {
        console.log("userTokensChat");
        const { userId, question } = event.arguments;

        const lexParams = {
          botAliasId: "HEJTWO8PK3",
          botId: "MRKYFTDVQQ", // Replace with your Lex V2 bot ID
          localeId: "en_US",
          sessionId: userId,
          text: question,
        };

        try {
          const lexResponse = await lexruntime
            .recognizeText(lexParams)
            .promise(); // Use recognizeText method to interact with the bot

          // Process Lex response (intent, slots, sessionState, etc.)
          const { interpretations, messages, sessionState } = lexResponse;

          if (sessionState?.intent?.state === "ReadyForFulfillment") {
            // Handle fulfilled intent, e.g., access data
            switch (sessionState?.intent?.name) {
              case "WelcomeIntent": {
                return {
                  User: question,
                  Assistant:
                    messages[0].content || `Hello, how can I assist you?`,
                };
              }
              case "UserTokenDetailsIntent": {
                const userData = await tokensAndFundsData(
                  userId,
                  apolloClient,
                  "user"
                );
                let token = Number(userData?.currentImpactScore) || 0;
                return {
                  User: question,
                  Assistant: `Your current token balance is ${token}`,
                };
              }
              case "AssignmentDataIntent": {
                const assignmentData = await assignmentDataFun(
                  userId,
                  apolloClient,
                  "assignmentList"
                );
                return respondWithAssignmentData(assignmentData, question);
              }
              case "NextAssignmentDueDateIntent": {
                const nextAssignment = await assignmentDataFun(
                  userId,
                  apolloClient,
                  "nextAssignment"
                );
                return respondWithAssignmentDueDate(nextAssignment, question);
              }
              case "NextAssignmentDescriptionIntent": {
                const nextAssignment = await assignmentDataFun(
                  userId,
                  apolloClient,
                  "nextAssignment"
                );
                return respondWithAssignmentDescription(
                  nextAssignment,
                  question
                );
              }
              case "NextAssignmentTokenPointIntent": {
                const nextAssignment = await assignmentDataFun(
                  userId,
                  apolloClient,
                  "nextAssignment"
                );
                return respondWithAssignmentTokenPoint(
                  nextAssignment,
                  question
                );
              }
              case "EntityPointsIntent": {
                let entity =
                  sessionState?.intent?.slots?.entity?.value
                    ?.resolvedValues[0] || "";
                let type =
                  sessionState?.intent?.slots?.type?.value?.resolvedValues[0] ||
                  "";
                if (entity) {
                  const associatedData = await tokensAndFundsData(
                    userId,
                    apolloClient,
                    entity
                  );
                  let token = calculatePoints(associatedData, type);
                  return {
                    User: question,
                    Assistant: `Your associated ${entity}'s ${type} balance is ${token}`,
                  };
                } else {
                  return {
                    User: question,
                    Assistant: `I can only help with information associated to organization, student or stakeholder.`,
                  };
                }
              }
              case "LastSubmissionDataIntent": {
                const lastSubmission = await lastSubmissionData(
                  userId,
                  apolloClient
                );
                return respondWithLastSubmission(lastSubmission, question);
              }
              case "FallbackIntent": {
                return {
                  User: question,
                  Assistant:
                    "I can only help with information about your and your associate's funds, points, and tokens.",
                };
              }
              default:
                return {
                  User: question,
                  Assistant:
                    "I can't assist you with that yet, but I'm learning!",
                };
            }
          } else {
            // Handle ongoing conversation with Lex (e.g., elicit slots)
            if (sessionState?.intent?.state === "InProgress") {
              return {
                User: question,
                Assistant:
                  messages[0]?.content || "Unable to understand your request.",
              };
            }
            return {
              User: question,
              Assistant:
                "I can only help with information about your and your associate's funds, points, and tokens.",
            };
          }
        } catch (err) {
          console.error(err);
          return {
            statusCode: 500,
            body: JSON.stringify({ error: "An error occurred" }),
          };
        }
      }
      case "submissionsChat": {
        console.log("submissionsChat");
        const { userId, question } = event.arguments;

        const lexParams = {
          botAliasId: "3XONYMYWUF",
          botId: "CL2VOOMUFZ", // Replace with your Lex V2 bot ID
          localeId: "en_US",
          sessionId: userId,
          text: question,
        };

        try {
          const lexResponse = await lexruntime
            .recognizeText(lexParams)
            .promise(); // Use recognizeText method to interact with the bot

          // Process Lex response (intent, slots, sessionState, etc.)
          const { interpretations, messages, sessionState } = lexResponse;

          if (sessionState?.intent?.state === "ReadyForFulfillment") {
            // Handle fulfilled intent, e.g., access data
            switch (sessionState?.intent?.name) {
              case "WelcomeIntent": {
                return {
                  User: question,
                  Assistant:
                    messages[0]?.content || `Hello, how can I assist you?`,
                };
              }
              case "LastSubmissionDateIntent": {
                const lastSubmission = await previousSubmissionsFun(
                  userId,
                  apolloClient
                );
                return respondWithLastSubmissionDate(lastSubmission, question);
              }
              case "LastSubmissionStatusIntent": {
                const lastSubmission = await previousSubmissionsFun(
                  userId,
                  apolloClient
                );
                return respondWithLastSubmissionStatus(
                  lastSubmission,
                  question
                );
              }
              case "SubmissionListIntent": {
                const userSubmissionsData = await userSubmissionsFun(
                  userId,
                  apolloClient
                );
                return respondWithSubmissionList(userSubmissionsData, question);
              }
              case "OtherSubmissionsVillageIntent": {
                const villageSubmissions = await villageSubmissionsFun(
                  userId,
                  apolloClient,
                  5
                );
                return respondWithVillageSubmissions(
                  villageSubmissions,
                  question,
                  5
                );
              }
              case "LastSubmissionPointsIntent": {
                const lastSubmission = await previousSubmissionsFun(
                  userId,
                  apolloClient
                );
                const currency =
                  sessionState?.intent?.slots?.currency?.value
                    ?.resolvedValues[0];
                return respondWithLastSubmissionPoints(
                  lastSubmission,
                  question,
                  currency
                );
              }
              case "FallbackIntent": {
                return {
                  User: question,
                  Assistant:
                    "I can only help with your and your village's submissions.",
                };
              }
              default:
                return {
                  User: question,
                  Assistant:
                    "I can't assist you with that yet, but I'm learning!",
                };
            }
          } else {
            // Handle ongoing conversation with Lex (e.g., elicit slots)
            if (sessionState?.intent?.state === "InProgress") {
              return {
                User: question,
                Assistant:
                  messages[0]?.content || "Unable to understand your request.",
              };
            }
            return {
              User: question,
              Assistant:
                "I can only help with your and your village's submissions.",
            };
          }
        } catch (err) {
          console.error(err);
          return {
            statusCode: 500,
            body: JSON.stringify({ error: "An error occurred" }),
          };
        }
      }
      case "communityEventsChat": {
        console.log("communityEventsChat");
        const { userId, question } = event.arguments;

        const lexParams = {
          botAliasId: "TSTALIASID",
          botId: "0DMUTP4VKK", // Replace with your Lex V2 bot ID
          localeId: "en_US",
          sessionId: userId,
          text: question,
        };

        try {
          const lexResponse = await lexruntime
            .recognizeText(lexParams)
            .promise(); // Use recognizeText method to interact with the bot

          // Process Lex response (intent, slots, sessionState, etc.)
          const { messages, sessionState } = lexResponse;

          if (sessionState?.intent?.state === "ReadyForFulfillment") {
            // Handle fulfilled intent, e.g., access data
            const slots = sessionState?.intent?.slots;
            switch (sessionState?.intent?.name) {
              case "WelcomeIntent": {
                return {
                  User: question,
                  Assistant:
                    messages[0]?.content || `Hello, how can I assist you?`,
                };
              }
              case "CommunityEventInfoIntent": {
                // Are there any upcoming events happening?
                const data = await communityEventsFun(
                  apolloClient,
                  slots,
                  "communityEventInfo"
                );
                return respondWithCommunityEventInfo(data, question);
              }
              case "EventCategoryInfoIntent": {
                // What STEM activities are going on in the community?
                const { eventCategory, data } = await communityEventsFun(
                  apolloClient,
                  slots
                );
                return respondWithEventCategoryInfo(
                  data,
                  question,
                  eventCategory
                );
              }
              case "DateEventInfoIntent": {
                // Give me a list of all of the events happening next month.
                const data = await communityEventsFun(apolloClient, slots);
                return respondWithDateEventInfo(data, question);
              }
              case "EntityEventInfoIntent": {
                // What events does my organization have coming up?
                const data = await entityEventFun(apolloClient, userId);
                return respondWithEntityEvent(data, question);
              }
              case "EventEntityCategoryInfoIntent": {
                // What organizations have financial literacy events coming up?
                const category =
                  slots?.eventType?.value?.resolvedValues[0] || "";
                const orgData = await eventEntityCategoryInfoFun(
                  apolloClient,
                  category,
                  "eventEntityCategory"
                );
                return respondWithEventEntityCategory(
                  orgData,
                  question,
                  category
                );
              }
              case "FallbackIntent": {
                return {
                  User: question,
                  Assistant: "I can only help with information about events.",
                };
              }
              default:
                return {
                  User: question,
                  Assistant:
                    "I can't assist you with that yet, but I'm learning!",
                };
            }
          } else {
            // Handle ongoing conversation with Lex (e.g., elicit slots)
            if (sessionState?.intent?.state === "InProgress") {
              return {
                User: question,
                Assistant:
                  messages[0]?.content || "Invalid input for update data.",
              };
            } else {
              return {
                User: question,
                Assistant: "I can only help with information about events.",
              };
            }
          }
        } catch (err) {
          console.error(err);
          return {
            statusCode: 500,
            body: JSON.stringify({ error: "An error occurred" }),
          };
        }
      }

      case "membershipEventsChat": {
        console.log("communityEventsChat");
        const { userId, username, question } = event.arguments;

        const lexParams = {
          botAliasId: "VL9QDVO6AJ",
          botId: "BQTGI5CO2J", // Replace with your Lex V2 bot ID
          localeId: "en_US",
          sessionId: userId,
          text: question,
        };

        try {
          const lexResponse = await lexruntime
            .recognizeText(lexParams)
            .promise(); // Use recognizeText method to interact with the bot

          // Process Lex response (intent, slots, sessionState, etc.)
          const { messages, sessionState } = lexResponse;
          const userData = await getUserById(userId);
          if (sessionState?.intent?.state === "ReadyForFulfillment") {
            // Handle fulfilled intent, e.g., access data
            const slots = sessionState?.intent?.slots;
            switch (sessionState?.intent?.name) {
              case "WelcomeIntent": {
                return {
                  User: question,
                  Assistant:
                    messages[0]?.content || `Hello, how can I assist you?`,
                };
              }
              // event intents
              case "CommunityEventInfoIntent-Events": {
                // Are there any upcoming events happening?
                const data = await communityEventsFun(
                  apolloClient,
                  slots,
                  "communityEventInfo"
                );
                return respondWithCommunityEventInfo(data, question);
              }
              case "EventCategoryInfoIntent-Events": {
                // What STEM activities are going on in the community?
                const { eventCategory, data } = await communityEventsFun(
                  apolloClient,
                  slots
                );
                return respondWithEventCategoryInfo(
                  data,
                  question,
                  eventCategory
                );
              }
              case "DateEventInfoIntent-Events": {
                // Give me a list of all of the events happening next month.
                const data = await communityEventsFun(apolloClient, slots);
                return respondWithDateEventInfo(data, question);
              }
              case "EntityEventInfoIntent-Events": {
                // What events does my organization have coming up?
                const data = await entityEventFun(apolloClient, userId);
                return respondWithEntityEvent(data, question);
              }
              case "EventEntityCategoryInfoIntent-Events": {
                // What organizations have financial literacy events coming up?
                const category =
                  slots?.eventType?.value?.resolvedValues[0] || "";
                const orgData = await eventEntityCategoryInfoFun(
                  apolloClient,
                  category,
                  "eventEntityCategory"
                );
                return respondWithEventEntityCategory(
                  orgData,
                  question,
                  category
                );
              }
              case "EventsLastWeekendIntent": {
                //Could you list the events from the previous weekend?
                const data = await weekendEventsFunction(
                  apolloClient,
                  "EventsLastWeekendIntent",
                  userData
                );
                return respondWithWeekendEventInfo(
                  data,
                  question,
                  "EventsLastWeekendIntent"
                );
              }
              case "EventsThisWeekendIntent": {
                //What's happening in town this weekend?
                const data = await weekendEventsFunction(
                  apolloClient,
                  "EventsThisWeekendIntent",
                  userData
                );
                return respondWithWeekendEventInfo(
                  data,
                  question,
                  "EventsThisWeekendIntent"
                );
              }

              case "LastWeekendEventOrganizationsIntent": {
                //What's happening in town this weekend?
                const data = await weekendEventsFunction(
                  apolloClient,
                  "EventsLastWeekendIntent",
                  userData
                );
                return respondWithWeekendEventInfo(
                  data,
                  question,
                  "LastWeekendEventOrganizationsIntent"
                );
              }
              case "ThisWeekendEventOrganizationsIntent": {
                //What's happening in town this weekend?
                const data = await weekendEventsFunction(
                  apolloClient,
                  "EventsThisWeekendIntent",
                  userData
                );
                return respondWithWeekendEventInfo(
                  data,
                  question,
                  "ThisWeekendEventOrganizationsIntent"
                );
              }

              // membership intents

              case "OrganizationIntent-Membership": {
                const associatedData = await getAssociatedData(
                  userId,
                  apolloClient,
                  "organizations"
                );
                return respondWithAssociatedData(
                  associatedData,
                  question,
                  "organizations"
                );
              }
              case "StudentAssociationIntent-Membership": {
                const associatedData = await getAssociatedData(
                  userId,
                  apolloClient,
                  "students"
                );
                return respondWithAssociatedData(
                  associatedData,
                  question,
                  "students"
                );
              }
              case "StakeholderAssociationIntent-Membership": {
                const associatedData = await getAssociatedData(
                  userId,
                  apolloClient,
                  "stakeholders"
                );
                return respondWithAssociatedData(
                  associatedData,
                  question,
                  "stakeholders"
                );
              }
              case "AddNewStudentIntent-Membership": {
                const loginUserData = await getUserById(userId);
                let userData = {
                  givenName:
                    sessionState?.intent?.slots?.firstName?.value
                      ?.resolvedValues[0] || "",
                  familyName:
                    sessionState?.intent?.slots?.lastName?.value
                      ?.resolvedValues[0] || "",
                  birthday:
                    sessionState?.intent?.slots?.birthday?.value
                      ?.resolvedValues[0] || "",
                  email:
                    sessionState?.intent?.slots?.email?.value
                      ?.resolvedValues[0] || "",
                  gender:
                    sessionState?.intent?.slots?.gender?.value
                      ?.resolvedValues[0] || "",
                  phoneNumber:
                    sessionState?.intent?.slots?.phoneNumber?.value
                      ?.resolvedValues[0] || "",
                  ethnicity:
                    sessionState?.intent?.slots?.ethnicity?.value
                      ?.resolvedValues[0] || "",
                  cityId:
                    loginUserData?.cityId ||
                    "8f6c4c07-dd62-480d-a376-ef221133f0e2",
                };
                if (userData) {
                  const userResponse = await createUserLambdaInvoke(
                    userData,
                    userId,
                    username
                  );
                  return {
                    User: question,
                    Assistant: `${userResponse}`,
                  };
                } else {
                  return {
                    User: question,
                    Assistant: `Some issue occured while creating this student.`,
                  };
                }
              }
              case "EditUserIntent-Membership": {
                let action =
                  sessionState?.intent?.slots?.action?.value
                    ?.resolvedValues[0] || "";
                let value =
                  sessionState?.intent?.slots?.updatedValue?.value
                    ?.resolvedValues[0] || "";

                const { statusCode, message } = await userDataValidation(
                  action,
                  value
                );
                if (statusCode === 200) {
                  let input = {
                    id: userId,
                    givenName: action === "first name" ? value : "",
                    familyName: action === "last name" ? value : "",
                    birthday: action === "birthday" ? value : "",
                    ethnicity: action === "ethnicity" ? value : "",
                    gender: action === "gender" ? value : "",
                  };
                  const updatedUser = await updateUserFun(input, apolloClient);
                  if (updatedUser) {
                    return {
                      User: question,
                      Assistant: `Your detail updated successfully.`,
                    };
                  } else {
                    return {
                      User: question,
                      Assistant: `There are some issues while updating your information.`,
                    };
                  }
                } else {
                  return {
                    User: question,
                    Assistant: `${message}`,
                  };
                }
              }
              case "RetrieveUserInfoIntent-Membership": {
                let retrieve =
                  sessionState?.intent?.slots?.retrieve?.value
                    ?.resolvedValues[0] || "";
                const message = await getUserData(retrieve, userId);
                if (message && message !== "") {
                  return {
                    User: question,
                    Assistant: `${message}`,
                  };
                } else {
                  return {
                    User: question,
                    Assistant: `Unable to fetch your information.`,
                  };
                }
              }
              case "CategoryDataIntent-Membership": {
                const category =
                  sessionState?.intent?.slots?.category?.value
                    ?.resolvedValues[0] || "";
                const orgData = await eventEntityCategoryInfoFun(
                  apolloClient,
                  category,
                  "categoryData"
                );
                return respondWithEventEntityCategory(
                  orgData,
                  question,
                  category
                );
              }

              case "FallbackIntent": {
                return {
                  User: question,
                  Assistant: "I can only help with information about events.",
                };
              }
              default:
                return {
                  User: question,
                  Assistant:
                    "I can't assist you with that yet, but I'm learning!",
                };
            }
          } else {
            // Handle ongoing conversation with Lex (e.g., elicit slots)
            if (sessionState?.intent?.state === "InProgress") {
              return {
                User: question,
                Assistant:
                  messages[0]?.content || "Invalid input for update data.",
              };
            } else {
              return {
                User: question,
                Assistant: "I can only help with information about events.",
              };
            }
          }
        } catch (err) {
          console.error(err);
          return {
            statusCode: 500,
            body: JSON.stringify({ error: "An error occurred" }),
          };
        }
      }
      case "bedrockChatBot": {
        // @deprecated The 'bedrockChatBot' case is deprecated and and should be replaced with a new implementation in the bedrockchatbot Lambda function.
        const { question, userId, chatType } = event.arguments;
        try {
          let chatBotPrompt = await getChatbotAnswer(
            question,
            userId,
            chatType
          );
          let bedRockChatResponse = await respondWithBedrockChatBot(
            chatBotPrompt
          );

          return {
            User: question,
            Assistant: bedRockChatResponse,
          };
        } catch (error) {
          console.error(error);
          return {
            User: question,
            Assistant: "An error occurred while processing your request.",
          };
        }
      }
      case "fetchChatbotData": {
        try {
        // @deprecated The 'fetchChatbotData' case is deprecated and should be replaced with a new implementation in the bedrockchatbot Lambda function.
          await storeChatbotPromptInDynamoDB(
            event.arguments.userId,
            event.arguments.chatType,
            apolloClient
          );
          return {
            message: "Prompt data stored successfully",
          };
        } catch (error) {
          return {
            statusCode: 500,
            body: JSON.stringify({ error: "An error occurred" }),
          };
        }
      }
      case "removeChatbotData": {
      // @deprecated The 'removeChatbotData' case is deprecated and and should be replaced with a new implementation in the bedrockchatbot Lambda function.
        const { userId } = event.arguments;
        try {
          await removeChatbotPromptFromDynamoDB(userId);
          return {
            message: "Prompt data removed successfully",
          };
        } catch (error) {
          return {
            statusCode: 500,
            body: JSON.stringify({ error: "An error occurred" }),
          };
        }
      }
      case "transcribeVideoOrAudio": {
        const { s3URI, transcriptionModule, id } = event.arguments;

        try {
          // Construct the full S3 URI
          const finals3URI = `https://${process.env.STORAGE_S3MYVILLAGEPROJECTADMINPORTALORGPROFILELOGO_BUCKETNAME}.s3.us-east-1.amazonaws.com/public/${s3URI}`;
          console.log("Starting transcription for S3 URI:", finals3URI);

          // Call the transcribe service
          const result = await transcribeFile(finals3URI);
          console.log("Transcription completed:", result.status);

          // Extract the transcription text from the result
          const transcriptionText =
            result.data?.results?.transcripts?.[0]?.transcript || "";

          console.log("transcriptionText: ", transcriptionText);
          console.log("result.data: ", result.data);
          // Store the transcription in the appropriate module
          if (transcriptionModule.toLowerCase() === "knowledge") {
            console.log("Storing in knowledge repository:", id);
            await storeInKnowledgeRepository(
              id,
              {
                transcription: transcriptionText,
                transcriptDetail: result.data,
              },
              apolloClient
            );
          } else if (transcriptionModule.toLowerCase() === "submission") {
            console.log("Storing in submission:", id);
            await storeInSubmission(
              id,
              {
                transcription: transcriptionText,
                transcriptDetail: result.data,
              },
              apolloClient
            );
          } else {
            console.warn("Unknown transcription module:", transcriptionModule);
          }

          // Return the complete result
          return {
            status: "SUCCESS",
            data: result.data, // Return as object, not stringified
            message: "Transcription completed successfully",
            jobName: result.jobName || "",
            s3URI: result.s3URI || "",
          };
        } catch (error) {
          console.error("Error in transcribeVideoOrAudio:", error);
          return {
            status: "ERROR",
            data: null,
            message: error.message || "Failed to transcribe video/audio",
            jobName: "",
            s3URI: "",
          };
        }
      }
      case "academicChat": {
        console.log("academicChat");
        const { userId, question } = event.arguments;

        const lexParams = {
          botAliasId: "TSTALIASID",
          botId: "AK7CAB8WM6", // Replace with your Lex V2 bot ID
          localeId: "en_US",
          sessionId: userId,
          text: question,
        };

        try {
          const lexResponse = await lexruntime
            .recognizeText(lexParams)
            .promise(); // Use recognizeText method to interact with the bot
          console.log("lexResponse", lexResponse);

          // Process Lex response (intent, slots, sessionState, etc.)
          const { messages, sessionState } = lexResponse;

          if (sessionState?.intent?.state === "ReadyForFulfillment") {
            // Handle fulfilled intent, e.g., access data
            const slots = sessionState?.intent?.slots;
            switch (sessionState?.intent?.name) {
              case "WelcomeIntent": {
                return {
                  User: question,
                  Assistant:
                    messages[0]?.content || `Hello, how can I assist you?`,
                };
              }
              case "CommunityEventInfoIntent": {
                // Are there any upcoming events happening?
                const data = await communityEventsFun(
                  apolloClient,
                  slots,
                  "communityEventInfo"
                );
                return respondWithCommunityEventInfo(data, question);
              }
              case "EventCategoryInfoIntent": {
                // What STEM activities are going on in the community?
                const { eventCategory, data } = await communityEventsFun(
                  apolloClient,
                  slots
                );
                return respondWithEventCategoryInfo(
                  data,
                  question,
                  eventCategory
                );
              }
              case "DateEventInfoIntent": {
                // Give me a list of all of the events happening next month.
                const data = await communityEventsFun(apolloClient, slots);
                return respondWithDateEventInfo(data, question);
              }
              case "EntityEventInfoIntent": {
                // What events does my organization have coming up?
                const data = await entityEventFun(apolloClient, userId);
                return respondWithEntityEvent(data, question);
              }
              case "EventEntityCategoryInfoIntent": {
                // What organizations have financial literacy events coming up?
                const category =
                  slots?.eventType?.value?.resolvedValues[0] || "";
                const orgData = await eventEntityCategoryInfoFun(
                  apolloClient,
                  category,
                  "eventEntityCategory"
                );
                return respondWithEventEntityCategory(
                  orgData,
                  question,
                  category
                );
              }
              case "FallbackIntent": {
                return {
                  User: question,
                  Assistant: "I can only help with academic topics.",
                };
              }
              default:
                return {
                  User: question,
                  Assistant:
                    "I can't assist you with that yet, but I'm learning!",
                };
            }
          } else {
            // Handle ongoing conversation with Lex (e.g., elicit slots)
            if (sessionState?.intent?.state === "InProgress") {
              return {
                User: question,
                Assistant:
                  messages[0]?.content || "Invalid input for update data.",
              };
            } else {
              return {
                User: question,
                Assistant: "I can only help with academic topics.",
              };
            }
          }
        } catch (err) {
          console.error(err);
          return {
            statusCode: 500,
            body: JSON.stringify({ error: "An error occurred" }),
          };
        }
      }
      case "getAssignmentDetail": {
        console.log("getAssignmentDetail");
        try {
          const { homeworkId, userId, memberId } = event.arguments;

          const response = await apolloClient.query({
            query: GET_HOMEWORK,
            variables: {
              id: homeworkId,
            },
          });
          const homework = response?.data?.getHomework;
          console.log("homework", homework);
          let findMember;
          if (homework.entityType === "member") {
            findMember = await apolloClient.query({
              query: GET_MEMBER,
              variables: {
                id: memberId,
              },
            });
          } else {
            findMember = await apolloClient.query({
              query: GET_USER,
              variables: {
                id: memberId,
              },
            });
          }

          console.log("findMemberRes", findMember);

          if (
            homework.entityType === "member"
              ? findMember?.data?.getOrganizations &&
                findMember?.data?.getOrganizations !== undefined
              : findMember?.data?.getUser &&
                findMember?.data?.getUser !== undefined
          ) {
            console.log("in if");

            let microcredential = homework?.microcredential;

            const members = homework?.members;
            const studentsStakeholders = homework?.studentsStakeholders;
            console.log("micro", microcredential);
            if (homework?.microcredentialId && homework?.microcredential) {
              const memberData = await apolloClient.query({
                query: GET_MICROCREDENTIAL,
                variables: {
                  id: microcredential?.id,
                },
              });
              const memberRes = memberData?.data?.getPrograms;
              console.log("memberRes", memberRes);
              if (memberRes) {
                const homeworkOrganizations =
                  memberRes?.homeworkOrganizations?.items?.filter(
                    (ex) =>
                      ex.memberId === memberId &&
                      (ex.homeworkStatus === "completed" ||
                        ex.homeworkStatus === "in-review")
                  );
                const homeworkUsers = memberRes?.homeworkUsers?.items?.filter(
                  (ex) =>
                    ex.studentStakeholderId === memberId &&
                    (ex.homeworkStatus === "completed" ||
                      ex.homeworkStatus === "in-review")
                );

                const memberHomeworkData = [
                  ...homeworkOrganizations,
                  ...homeworkUsers,
                ];

                const completedAssignments = memberHomeworkData.filter(
                  (assignment) => assignment.homeworkStatus === "completed"
                );
                const receivedPoints = completedAssignments.reduce(
                  (sum, assignment) =>
                    sum + assignment?.homeworkData?.assignmentPoints || 0,
                  0
                );

                const inReviewAssignments = memberHomeworkData.filter(
                  (assignment) => assignment.homeworkStatus === "in-review"
                );
                const remainingPoints = inReviewAssignments.reduce(
                  (sum, assignment) =>
                    sum + assignment?.homeworkData?.assignmentPoints || 0,
                  0
                );

                microcredential = {
                  ...microcredential,
                  receivedPoints,
                  remainingPoints,
                };

                const memberDataArray =
                  members?.items
                    .map((item) => item?.memberData)
                    .filter(
                      ({ imageUrl }) =>
                        imageUrl !== null &&
                        imageUrl !== "" &&
                        imageUrl !== "null"
                    ) || [];
                const studentStakeholderDataArray =
                  studentsStakeholders?.items
                    .map((item) => item?.studentStakeholderData)
                    .filter(
                      ({ imageUrl }) =>
                        imageUrl !== null &&
                        imageUrl !== "" &&
                        imageUrl !== "null"
                    ) || [];

                const memberImage =
                  studentStakeholderDataArray[0]?.imageUrl ||
                  memberDataArray[0]?.imageUrl ||
                  "";
                console.log("memberImage", memberImage);

                const associatedOrganizations = await getAssociatedData(
                  userId,
                  apolloClient,
                  "organizations"
                );
                const associatedStakeholders = await getAssociatedData(
                  userId,
                  apolloClient,
                  "stakeholders"
                );
                const associatedStudents = await getAssociatedData(
                  userId,
                  apolloClient,
                  "students"
                );

                const allUserAssociationids = [
                  ...associatedOrganizations.map((org) => org.id),
                  ...associatedStakeholders.map(
                    (stakeholder) => stakeholder.id
                  ),
                  ...associatedStudents.map((student) => student.id),
                ];
                // console.log('allUserAssociationids', allUserAssociationids)

                // Merge member and student stakeholder IDs
                const allAssigendMemberIds = extractIds(
                  members,
                  studentsStakeholders
                );

                const isAssociated = hasMatchingValues(
                  allUserAssociationids,
                  allAssigendMemberIds
                );

                const matchingMembers = members.items.find(
                  (member) => member.memberId === memberId
                );
                const matchingStakeholders = studentsStakeholders.items.find(
                  (stakeholder) => stakeholder.studentStakeholderId === memberId
                );

                // Combine data from both matching objects (e.g., memberId, homeworkId, homeworkStatus)
                const memberHomework = {
                  ...matchingMembers,
                  ...matchingStakeholders,
                };
                console.log("memberHomework:", memberHomework);

                const isSubmitted = !![
                  "completed",
                  "in-review",
                  "denied",
                ].includes(memberHomework?.homeworkStatus);

                const isAssigned = !!memberHomework?.homeworkStatus;

                const homeworkDetail = {
                  id: homework?.id,
                  name: homework?.name,
                  shortDescription: homework?.shortDescription,
                  longDescription: homework?.longDescription,
                  dueDate: homework?.dueDate,
                  imageUrl: homework?.imageUrl,
                  coCreationType: homework?.coCreationType,
                  assignmentPoints: homework?.assignmentPoints,
                  primaryContact: homework?.primaryContact,
                  cityId: homework?.cityId,
                  createdAt: homework?.createdAt,
                  isAssociated: isAssociated,
                  isSubmitted: isSubmitted,
                  isAssigned: isAssigned,
                  memberImage: memberImage,
                  microcredential: microcredential,
                  message: "",
                };
                console.log("GraphQL response:", homeworkDetail);
                callback(null, homeworkDetail);
              } else {
                console.log("No microcredential else");
                callback(null, {
                  ...homework,
                  message: "The associated project does not exist.",
                });
              }
            } else {
              console.log("Empty microcredentialId else");
              callback(null, {
                ...homework,
                message: "The associated project does not exist.",
              });
            }
          } else {
            console.log("in else");
            callback(null, {
              ...homework,
              message: "The associated member does not exist.",
            });
          }
        } catch (err) {
          console.error(err);
          return {
            statusCode: 500,
            body: JSON.stringify({
              error: err?.message || "An error occurred",
            }),
          };
        }
        break;
      }
      case "selfAssignAssignment": {
        console.log("selfAssignAssignment");
        try {
          const { homeworkId, memberId, memberType } = event?.arguments?.input;
          //memberType = Person/Organization

          const response = await apolloClient.query({
            query: GET_HOMEWORK,
            variables: {
              id: homeworkId,
            },
          });
          const homework = response?.data?.getHomework;
          const microcredential = homework?.microcredential;

          if (memberType === "Organization") {
            const members = homework?.members;
            const matchingMembers = !!members?.items.find(
              (member) =>
                member.memberId === memberId && member.homeworkId === homeworkId
            );

            if (!matchingMembers) {
              const homeworkOrganizationData = {
                memberId: memberId,
                homeworkAssignDate: new Date().toISOString(),
                homeworkMembersId: homeworkId,
                homeworkId: homeworkId,
                homeworkStatus: "pending",
                organizationsHomeworksId: memberId,
                programsHomeworkOrganizationsId: microcredential?.id || "null",
              };

              await apolloClient.mutate({
                mutation: CREATE_HOMEWORK_ORGANIZATIONS,
                variables: {
                  input: homeworkOrganizationData,
                },
              });
              callback(null, {
                message: "Assignment assigned.",
                statusCode: 200,
              });
            }
            callback(null, {
              message: "Assignment already assigned.",
              statusCode: 200,
            });
          } else {
            const studentsStakeholders = homework?.studentsStakeholders;
            const matchingStakeholders = !!studentsStakeholders.items.find(
              (stakeholder) =>
                stakeholder.studentStakeholderId === memberId &&
                stakeholder.homeworkId === homeworkId
            );

            if (!matchingStakeholders) {
              const homeworkUserData = {
                studentStakeholderId: memberId,
                homeworkAssignDate: new Date().toISOString(),
                homeworkStudentsStakeholdersId: homeworkId,
                homeworkId: homeworkId,
                homeworkStatus: "pending",
                userHomeworksId: memberId,
                programsHomeworkUsersId: microcredential?.id || "null",
              };

              await apolloClient.mutate({
                mutation: CREATE_HOMEWORK_USERS,
                variables: {
                  input: homeworkUserData,
                },
              });
              callback(null, {
                message: "Assignment assigned.",
                statusCode: 200,
              });
            }
            callback(null, {
              message: "Assignment already assigned.",
              statusCode: 200,
            });
          }
        } catch (err) {
          console.error(err);
          return {
            statusCode: 500,
            body: JSON.stringify({
              error: err?.message || "An error occurred",
            }),
          };
        }
        break;
      }
      case "removeCityData": {
        console.log("removeCityData");
        const { cityId, transferCityId } = event?.arguments;

        try {
          if (transferCityId) {
            await transferData(cityId, transferCityId);
            console.log(
              `Data transferred from city ${cityId} to city ${transferCityId}`
            );
          } else {
            await removeCityData(cityId);
            console.log(
              `All data associated with city ${cityId} has been deleted`
            );
          }

          return {
            statusCode: 200,
            body: JSON.stringify({
              message: "City data removed successfully",
            }),
          };
        } catch (error) {
          console.error("inside removeCityData", error.message);
          return {
            statusCode: 500,
            body: JSON.stringify({
              error: error?.message || "An error occurred",
            }),
          };
        }
      }
      case "removeCategoryData": {
        const { categoryId, transferCategoryId } = event.arguments;
        console.log("transferCategoryId: ", transferCategoryId);
        console.log("categoryId: ", categoryId);

        try {
          await transferCategoryData(categoryId, transferCategoryId);

          return {
            statusCode: 200,
            message: "Category data transferred successfully",
          };
        } catch (error) {
          console.error("Error in removeCategoryData:", error);
          return {
            statusCode:
              error.message === "One or both categories not found" ? 400 : 500,
            message: "Error transferring category data: " + error.message,
          };
        }
      }
      default:
        callback("Unknown field, unable to resolve" + event.fieldName, null);
        break;
    }
  } catch (error) {
    console.error("Error executing GraphQL query:", error);
    throw new ApolloError("GraphQL Error", "GRAPHQL_ERROR", { error });
  }
};

function calculateCityPoints(data) {
  let points = 0;

  data.forEach((record) => {
    if (record.impactScore) points += parseFloat(record.impactScore);
  });

  return points || 0.0;
}

// Function to check if arrays have any matching values
function hasMatchingValues(arr1, arr2) {
  // Use some() to check if any element in arr1 exists in arr2
  return arr1.some((element) => arr2.includes(element));
}

// Function to extract member and student stakeholder IDs
function extractIds(membersObj, stakeholdersObj) {
  const memberIds = membersObj.items.map((member) => member.memberId);
  const studentStakeholderIds = stakeholdersObj.items.map(
    (stakeholder) => stakeholder.studentStakeholderId
  );

  return memberIds.concat(studentStakeholderIds);
}

const deleteItems = async (tableName, ids) => {
  const promises = ids.map(async (id) => {
    const deleteParams = {
      TableName: tableName,
      Key: {
        id: { S: id },
      },
    };
    try {
      await ddb.deleteItem(deleteParams).promise();
    } catch (error) {
      console.log(error, error.stack);
    }
  });
  await Promise.all(promises);
};

function replaceAll(str, find, replace) {
  return str.replace(new RegExp(find, "g"), replace);
}

async function getUserById(userId) {
  const params = {
    TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    Key: { id: { S: userId } },
  };
  const getUserResponse = await ddb.getItem(params).promise();
  const userItem = getUserResponse["Item"]
    ? AWS.DynamoDB.Converter.unmarshall(getUserResponse["Item"])
    : null;
  return userItem;
}

async function userDataValidation(action, value) {
  let message = "";
  let statusCode = 403;
  if (
    action === "ethnicity" &&
    [
      "African American",
      "Alaska Native",
      "American Indian",
      "Asian",
      "Black",
      "Native Hawaiian",
      "Pacific Islander",
      "White",
    ].indexOf(value) === -1
  ) {
    message =
      "Please select proper ethnicity ['African American', 'Alaska Native', 'American Indian', 'Asian', 'Black', 'Native Hawaiian', 'Pacific Islander', 'White'].";
  } else if (
    action === "gender" &&
    ["Male", "Female", "Other"].indexOf(value) === -1
  ) {
    message = "Please select proper gender ['Male', 'Female', 'Other'].";
  } else if (
    action == "birthday" &&
    !/^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/.test(value)
  ) {
    message = "Please enter valid birthday (YYYY-MM-DD).";
  } else {
    statusCode = 200;
  }
  return { statusCode: statusCode, message: message };
}

async function getUserData(retrieve, userId) {
  const userData = await getUserById(userId);
  let message = "";

  if (!userData) {
    return "User not found.";
  }

  switch (retrieve) {
    case "first name":
      message = `Your first name is ${userData.givenName}`;
      break;
    case "last name":
      message = `Your last name is ${userData.familyName}`;
      break;
    case "ethnicity":
      message = `Your ethnicity is ${userData.ethnicity}`;
      break;
    case "gender":
      message = `Your gender is ${userData.gender}`;
      break;
    case "phone number":
      message = `Your phone number is ${userData.phoneNumber}`;
      break;
    case "email":
      message = `Your email is ${userData.email}`;
      break;
    case "city":
      message = `Your city is ${userData.city}`;
      break;
    case "birthday":
      message = `Your birth date is ${userData.birthday}`;
      break;
    case "address":
      message = `Your address is ${userData.streetAddressOne} ${userData.streetAddressTwo}`;
      break;
    case "state":
      message = `Your state is ${userData.state}`;
      break;
    case "zipcode":
      message = `Your zipcode is ${userData.zipcode}`;
      break;
    case "name":
      message = `Your name is ${userData.name}`;
      break;
    default:
      message = "Invalid data to retrieve.";
  }

  return message;
}

async function updateUserFun(input, apolloClient) {
  if (input) {
    Object.keys(input).map((ex) => (input[ex] === "" ? delete input[ex] : ""));
  }
  console.log("input", input);
  // Get user details
  const getUser = await apolloClient.query({
    query: GET_USER,
    variables: {
      id: input?.id,
    },
  });
  const userData = getUser.data.getUser;
  console.log("userData", userData);
  input["_version"] = userData._version;
  input["name"] = `${input.givenName || userData.givenName} ${
    input.familyName || userData.familyName
  }`;

  // Update User
  const updateUser = await apolloClient.mutate({
    mutation: UPDATE_USER,
    variables: {
      input: input,
    },
  });
  const updatedUser = updateUser?.data?.updateUser;
  console.log("updatedUser", updatedUser);

  // Update cognito user data
  let UserAttributes = [];
  if (input?.familyName) {
    UserAttributes.push({ Name: "family_name", Value: input?.familyName });
  }
  if (input?.givenName) {
    UserAttributes.push({ Name: "given_name", Value: input?.givenName });
  }
  if (input?.name) {
    UserAttributes.push({ Name: "name", Value: input?.name });
  }
  const params = {
    UserAttributes,
    UserPoolId: USERPOOLID,
    Username: input?.id,
  };
  await cognito.adminUpdateUserAttributes(params).promise();

  // Update membership
  let membershipObj = {
    id: userData?.membership?.id,
    name: input.name,
    _version: userData?.membership?._version,
  };
  console.log("membershipObj", membershipObj);
  const updateMembership = await apolloClient.mutate({
    mutation: UPDATE_MEMBERSHIP,
    variables: {
      input: membershipObj,
    },
  });
  const updatedMembership = updateMembership?.data?.updateMembership;
  console.log("updatedMembership", updatedMembership);
  return updatedUser;
}

// Amazon lex
// Functions to handle specific intents (replace with your data access logic)
// memberChatAssistant API Functions
async function getAssociatedData(userId, apolloClient, type) {
  let filter = {
    or: [{ otherPersonsID: { eq: userId } }, { personsID: { eq: userId } }],
  };
  const response = await apolloClient.query({
    query: GET_ASSOCIATED_DATA,
    variables: {
      filter: filter,
    },
  });

  const associations = response.data.associationsByDate.items;

  if (type === "organizations") {
    let associatedOrgnizations = associations.flatMap((ex) => {
      if (ex?.organization?.id && ex?.type === "Organization") {
        return {
          id: ex?.organization?.id,
          name: ex?.organization?.name,
          relation: ex?.relationType,
        };
      } else {
        return [];
      }
    });
    return associatedOrgnizations;
  } else if (type === "stakeholders") {
    let associatedStakeholders = associations.flatMap((ex) => {
      if (
        ex?.person?.id &&
        ex?.person?.id !== userId &&
        ex?.person?.isStakeholder &&
        ex?.type === "Person"
      ) {
        return {
          id: ex?.person?.id,
          name: ex?.person?.name,
          relation: ex?.relationType,
        };
      } else if (
        ex?.otherPerson?.id &&
        ex?.otherPerson?.id !== userId &&
        ex?.otherPerson?.isStakeholder &&
        ex?.type === "Person"
      ) {
        return {
          id: ex?.otherPerson?.id,
          name: ex?.otherPerson?.name,
          relation: ex?.relationType,
        };
      } else {
        return [];
      }
    });
    return associatedStakeholders;
  } else if (type === "students") {
    let associatedStudents = associations.flatMap((ex) => {
      if (
        ex?.person?.id &&
        ex?.person?.id !== userId &&
        !ex?.person?.isStakeholder &&
        ex?.type === "Person"
      ) {
        return {
          id: ex?.person?.id,
          name: ex?.person?.name,
          relation: ex?.relationType,
        };
      } else if (
        ex?.otherPerson?.id &&
        ex?.otherPerson?.id !== userId &&
        !ex?.otherPerson?.isStakeholder &&
        ex?.type === "Person"
      ) {
        return {
          id: ex?.otherPerson?.id,
          name: ex?.otherPerson?.name,
          relation: ex?.relationType,
        };
      } else {
        return [];
      }
    });
    return associatedStudents;
  } else {
    return [];
  }
}

function respondWithAssociatedData(associatedData, question, type) {
  if (associatedData.length > 0) {
    let data = associatedData
      .map((ex) => `● ${ex.name} (${ex.relation}) \n`)
      .toString();
    return {
      User: question,
      Assistant: `You are associated with the following ${type}: \n\n${replaceAll(
        data,
        ",",
        ""
      )}`,
    };
  }
  return {
    User: question,
    Assistant: `It seems you aren\'t associated with any ${type} yet.`,
  };
}

const createUserLambdaInvoke = async (userData, userId, username) => {
  const customQueryFunctionName = `customQueryFunction-${process.env.ENV}`;
  const payload = {
    fieldName: "createStudent",
    arguments: {
      input: {
        items: {
          givenName: userData?.givenName ?? "",
          familyName: userData?.familyName ?? "",
          birthday: userData?.birthday ?? "",
          email: userData?.email ?? "",
          gender: userData?.gender ?? "",
          phoneNumber: userData?.phoneNumber ?? "",
          ethnicity: userData?.ethnicity ?? "",
          name: `${userData?.givenName} ${userData?.familyName}` ?? "",
          status: `enrolled`,
          registeredFrom: `MOBILE`,
          associationType: "Parent-to-Student",
          createdUserId: userId,
          createdUserName: username,
          imageUrl: "",
          cityId: userData?.cityId ?? "",
        },
      },
    },
  };
  const customQueryParams = {
    FunctionName: customQueryFunctionName,
    InvocationType: "RequestResponse",
    LogType: "None",
    Payload: JSON.stringify(payload),
  };

  let responseData = await lambda.invoke(customQueryParams).promise();
  console.log("responseData", responseData);
  let payloadResponse = JSON.parse(responseData?.Payload) || "";
  return (
    payloadResponse?.message ||
    "Some issue occured while creating this student."
  );
};

// userTokensChat API Functions
async function tokensAndFundsData(userId, apolloClient, type) {
  // Token, points and fund data
  let studentIds = [];
  let stakeholderIds = [];
  let organizationIds = [];

  let filter = {
    or: [{ otherPersonsID: { eq: userId } }, { personsID: { eq: userId } }],
  };
  const response = await apolloClient.query({
    query: GET_ASSOCIATED_DATA,
    variables: {
      filter: filter,
    },
  });
  const associations = response?.data?.associationsByDate?.items;
  associations.forEach((item) => {
    if (item.organization?.id) {
      uniquePush(organizationIds, item.organization.id);
    }
    processPerson(item?.person, stakeholderIds, studentIds);
    processPerson(item?.otherPerson, stakeholderIds, studentIds);
  });
  const filterMembership = {};
  const membershipResponse = await apolloClient.query({
    query: GET_MEMBERSHIP_LIST,
    variables: {
      filter: filterMembership,
    },
  });
  let memberships = membershipResponse?.data?.membershipByDate?.items;
  if (type === "organization") {
    let associatedOrg = memberships.filter((item) =>
      organizationIds.includes(item.organizationID)
    );
    return associatedOrg || 0;
  } else if (type === "stakholder") {
    let associatedStak = memberships.filter((item) =>
      stakeholderIds.includes(item.personsID)
    );
    return associatedStak || 0;
  } else if (type === "student") {
    let associatedStudent = memberships.filter((item) =>
      studentIds.includes(item.personsID)
    );
    return associatedStudent || 0;
  } else if (type === "user") {
    let userData = memberships.find((item) => item.personsID === userId);
    return userData;
  } else {
    return 0;
  }
}

const uniquePush = (array, value) => {
  if (!array.includes(value)) {
    array.push(value);
  }
};

const processPerson = (person, stakeholderIds, studentIds) => {
  if (person?.id) {
    const targetArray = person.isStakeholder ? stakeholderIds : studentIds;
    uniquePush(targetArray, person.id);
  }
};

async function assignmentDataFun(userId, apolloClient, type) {
  // Assignment list and next assignment data
  const filterAssignment = {
    studentStakeholderId: { eq: userId },
    homeworkStatus: { eq: "pending" },
  };
  const assignmentsResponse = await apolloClient.query({
    query: GET_USER_ASSIGNMENTS,
    variables: {
      filter: filterAssignment,
    },
  });
  let assignments = assignmentsResponse?.data?.listHomeworkUsers?.items;

  let assignmentData = [];
  assignments.map((ex) => {
    if (ex.homeworkStatus === "pending" && ex.homeworkData) {
      assignmentData.push({
        name: ex.homeworkData?.name,
        due_date: ex.homeworkData?.dueDate,
        points: ex.homeworkData?.assignmentPoints,
        tokens: ex.homeworkData?.assignmentPoints,
        description: ex.homeworkData?.shortDescription,
      });
    }
  });
  assignmentData.sort((a, b) => new Date(a.due_date) - new Date(b.due_date));
  if (type === "assignmentList") {
    return assignmentData;
  } else if (type === "nextAssignment") {
    let nextAssignment = assignmentData[0] || {};
    console.log("nextAssignment", nextAssignment);
    return nextAssignment;
  } else {
    return [];
  }
}

async function lastSubmissionData(userId, apolloClient) {
  // Last submission data
  const filterSubmission = { memberId: { eq: userId } };
  const submissionResponse = await apolloClient.query({
      
      
       query: GET_USER_SUBMISSIONS,
    variables: {
      filter: filterSubmission,
    },
  });
  let submissions = submissionResponse?.data?.SubmissionByDate?.items;
  let previousSubmissions = [];
  submissions.map((ex) => {
    previousSubmissions.push({
      name: ex?.text,
      description: ex?.description,
      status: ex?.isActive,
      submitted_at: ex?.createdAt,
    });
  });
  previousSubmissions.sort(
    (a, b) => new Date(b.submitted_at) - new Date(a.submitted_at)
  ) || [];
  let lastSubmission = {};
  if (previousSubmissions.length > 0) {
    lastSubmission = previousSubmissions[0];
  }
  return lastSubmission;
}

function calculatePoints(data, type) {
  let points = 0;

  if (type === "points") {
    data.forEach((record) => {
      if (record.currentImpactScore)
        points += parseFloat(record.currentImpactScore);
    });
  }

  if (type === "funds") {
    data.forEach((record) => {
      if (record.fundTokens) points += parseFloat(record.fundTokens);
    });
  }

  if (type === "tokens") {
    data.forEach((record) => {
      if (record.MVPTokens) points += parseFloat(record.MVPTokens);
    });
  }

  return points || 0.0;
}

function respondWithAssignmentData(assignmentData, question) {
  if (assignmentData.length > 0) {
    let data = assignmentData.map((ex) => `● ${ex.name} \n`).toString();
    return {
      User: question,
      Assistant: `You have following assignment: \n\n${replaceAll(
        data,
        ",",
        ""
      )}.`,
    };
  }
  return {
    User: question,
    Assistant: `It seems you don\'t have any assignment yet.`,
  };
}

function respondWithAssignmentDueDate(nextAssignment, question) {
  if (Object.keys(nextAssignment).length > 0) {
    return {
      User: question,
      Assistant: `Your next assignment ${
        nextAssignment?.name
      } is due on ${new Date(nextAssignment?.due_date).toDateString()}.`,
    };
  }
  return {
    User: question,
    Assistant: `You don\'t have any pending assignments.`,
  };
}

function respondWithAssignmentDescription(nextAssignment, question) {
  if (Object.keys(nextAssignment).length > 0) {
    return {
      User: question,
      Assistant: `Please read the following instructions for your next assignment: \n\n${nextAssignment?.description}.`,
    };
  }
  return {
    User: question,
    Assistant: `You don\'t have any pending assignments.`,
  };
}

function respondWithAssignmentTokenPoint(nextAssignment, question) {
  if (Object.keys(nextAssignment).length > 0) {
    return {
      User: question,
      Assistant: `You will earn ${nextAssignment?.tokens} tokens and ${nextAssignment?.points} points on completing your next assignment.`,
    };
  }
  return {
    User: question,
    Assistant: `You don\'t have any pending assignments.`,
  };
}

function respondWithLastSubmission(lastSubmission, question) {
  if (Object.keys(lastSubmission).length > 0) {
    return {
      User: question,
      Assistant: `Please find the details about your last submission below: \n\n● Name: ${
        lastSubmission?.name
      } \nYou posted this submission on ${new Date(
        lastSubmission?.submitted_at
      ).toDateString()}.`,
    };
  }
  return {
    User: question,
    Assistant: `It seems you don\'t have any submissions yet.`,
  };
}

// submissionsChat API functions
async function submissionData(userId, apolloClient, type) {
  const filterSubmission = {};
  const submissionResponse = await apolloClient.query({
    query: GET_USER_SUBMISSIONS,
    variables: {
      filter: filterSubmission,
    },
  });
  let submissions = submissionResponse?.data?.SubmissionByDate?.items;

  if (type === "allSubmission") {
    return submissions;
  } else if (type === "userSubmission") {
    let userSubmissions =
      submissions.filter((submission) => submission.memberId === userId) || [];
    return userSubmissions;
  } else {
    return [];
  }
}

async function previousSubmissionsFun(userId, apolloClient) {
  let userSubmissions = await submissionData(
    userId,
    apolloClient,
    "userSubmission"
  );
  // console.log('userSubmissions', userSubmissions)
  let previousSubmissions = [];
  userSubmissions.map((ex) => {
    previousSubmissions.push({
      name: ex?.text,
      description: ex?.description,
      status: ex?.isActive,
      submitted_at: ex?.createdAt,
      points: ex?.homework?.assignmentPoints,
      received: true,
    });
  });
  previousSubmissions.sort(
    (a, b) => new Date(b.submitted_at) - new Date(a.submitted_at)
  ) || [];
  let lastSubmission = {};
  if (previousSubmissions.length > 0) {
    lastSubmission = previousSubmissions[0];
  }
  return lastSubmission;
}

async function userSubmissionsFun(userId, apolloClient) {
  let userSubmissions = await submissionData(
    userId,
    apolloClient,
    "userSubmission"
  );
  let userSubmissionsData = [];
  userSubmissions.map((ex) => {
    userSubmissionsData.push({
      name: ex?.text,
      description: ex?.description,
      status: ex?.isActive,
      submitted_at: ex?.createdAt,
    });
  });
  return userSubmissionsData;
}

async function villageSubmissionsFun(userId, apolloClient, submissionCount) {
  let submissions = await submissionData(userId, apolloClient, "allSubmission");
  let villageSubmissions = [];
  submissions.slice(0, submissionCount).map((ex) => {
    villageSubmissions.push({
      name: ex?.text,
      description: ex?.description,
      status: ex?.isActive,
      submitted_at: ex?.createdAt,
    });
  });
  return villageSubmissions;
}

function respondWithLastSubmissionDate(lastSubmission, question) {
  if (Object.keys(lastSubmission).length > 0) {
    return {
      User: question,
      Assistant: `Your last submission was submit on ${new Date(
        lastSubmission?.submitted_at
      ).toDateString()}.`,
    };
  }
  return {
    User: question,
    Assistant: `It seems you don\'t have any submissions yet.`,
  };
}

function respondWithLastSubmissionStatus(lastSubmission, question) {
  if (Object.keys(lastSubmission).length > 0) {
    const status = lastSubmission?.status;
    return {
      User: question,
      Assistant: `Yes, we received your last submission ${lastSubmission?.name} and it's status is ${status}.`,
    };
  }
  return {
    User: question,
    Assistant: `It seems you don\'t have any submissions yet.`,
  };
}

function respondWithLastSubmissionPoints(lastSubmission, question, currency) {
  if (Object.keys(lastSubmission).length > 0) {
    const points = lastSubmission?.points;
    return {
      User: question,
      Assistant: `You earned ${points} ${currency} from your last submission ${lastSubmission?.name}.`,
    };
  }
  return {
    User: question,
    Assistant: `It seems you don\'t have any submissions yet.`,
  };
}

function respondWithSubmissionList(userSubmissionsData, question) {
  if (userSubmissionsData.length > 0) {
    let data = userSubmissionsData
      .map((ex) => `● ${ex.name} ( ${ex.description} ) \n`)
      .toString();
    return {
      User: question,
      Assistant: `You have following submissions: \n\n${replaceAll(
        data,
        ",",
        ""
      )}`,
    };
  }
  return {
    User: question,
    Assistant: `It seems you don\'t have any submission yet.`,
  };
}

function respondWithVillageSubmissions(
  villageSubmissions,
  question,
  submissionCount
) {
  if (villageSubmissions.length > 0) {
    let data = villageSubmissions
      .map((ex) => `● ${ex.name} ( ${ex.description} ) \n`)
      .toString();
    return {
      User: question,
      Assistant: `Please find information about ${submissionCount} recent submissions from our village: \n\n${replaceAll(
        data,
        ",",
        ""
      )}`,
    };
  }
  return {
    User: question,
    Assistant: `I can't find any other submissions in your village.`,
  };
}

// communityEventsChat API functions
async function eventsData(apolloClient, filter) {
  const filterEvents = filter || {};
  const eventResponse = await apolloClient.query({
    query: GET_EVENTS_LIST,
    variables: {
      filter: filterEvents,
    },
  });
  return eventResponse?.data?.eventsByDate?.items;
}

async function communityEventsFun(apolloClient, slots, intent) {
  let eventCategory = slots?.eventCategory?.value?.resolvedValues[0] || "";
  let timeRange = slots?.timeRange?.value?.resolvedValues[0] || "";
  let timeType = slots?.timeType?.value?.resolvedValues[0] || "";

  if (eventCategory) {
    let filter = {
      and: [
        { startDateTime: { le: new Date() } },
        { endDateTime: { ge: new Date() } },
      ],
    };
    let events = await eventsData(apolloClient, filter);
    let data = events.filter((event) => event.type === eventCategory);

    return { eventCategory, data };
  } else if (timeRange && timeType) {
    let events = await eventsData(apolloClient);
    let rangeDate = new Date();

    if (timeRange === "previous" || timeRange === "last") {
      switch (timeType) {
        case "day":
          rangeDate.setDate(rangeDate.getDate() - 1);
          break;
        case "week":
          rangeDate.setDate(rangeDate.getDate() - 7);
          break;
        case "month":
          rangeDate.setDate(rangeDate.getDate() - 30);
          break;
        case "year":
          rangeDate.setDate(rangeDate.getDate() - 365);
          break;
      }
      let filter = {
        and: [
          { startDateTime: { ge: rangeDate } },
          { startDateTime: { lt: new Date() } },
        ],
      };
      let events = await eventsData(apolloClient, filter);
      return events;
    } else if (timeRange === "coming" || timeRange === "next") {
      switch (timeType) {
        case "day":
          rangeDate.setDate(rangeDate.getDate() + 1);
          break;
        case "week":
          rangeDate.setDate(rangeDate.getDate() + 7);
          break;
        case "month":
          rangeDate.setDate(rangeDate.getDate() + 30);
          break;
        case "year":
          rangeDate.setDate(rangeDate.getDate() + 365);
          break;
      }
      let filter = {
        and: [
          { startDateTime: { le: rangeDate } },
          { startDateTime: { gt: new Date() } },
        ],
      };
      let events = await eventsData(apolloClient, filter);
      return events;
    } else {
      return events;
    }
  } else if (intent === "communityEventInfo") {
    let filter = { startDateTime: { ge: new Date() } };
    let events = await eventsData(apolloClient, filter);

    return events;
  } else {
    let events = await eventsData(apolloClient);
    return events;
  }
}

async function weekendEventsFunction(apolloClient, intent) {
  const today = new Date();
  const currentDay = today.getUTCDay();
  console.log("today", currentDay);
  if (intent === "EventsLastWeekendIntent") {
    const daysSinceLastSaturday = currentDay === 6 ? 0 : currentDay + 1;
    const daysUntilNextSunday = currentDay === 0 ? 0 : 7 - currentDay;

    const lastSaturday = new Date(
      today.getTime() - daysSinceLastSaturday * 24 * 60 * 60 * 1000
    );
    const lastSunday = new Date(
      today.getTime() - daysUntilNextSunday * 24 * 60 * 60 * 1000
    );

    lastSaturday.setHours(0, 0, 0, 0);

    lastSunday.setHours(23, 59, 59, 999);

    let filter = {
      and: [
        { startDateTime: { ge: lastSaturday } },
        { startDateTime: { le: lastSunday } },
      ],
    };
    console.log("comingSaturday", lastSaturday);
    console.log("comingSunday", lastSunday);
    console.log("filter if", filter);
    let events = await eventsData(apolloClient, filter);

    return events;
  } else if (intent === "EventsThisWeekendIntent") {
    const daysUntilNextSaturday =
      currentDay === 6 ? 0 : currentDay === 0 ? -1 : 6 - currentDay;
    const daysUntilNextSunday = currentDay === 0 ? 0 : 8 - currentDay;

    const comingSaturday = new Date(
      today.getTime() + daysUntilNextSaturday * 24 * 60 * 60 * 1000
    );
    const comingSunday = new Date(
      today.getTime() + daysUntilNextSunday * 24 * 60 * 60 * 1000
    );

    comingSaturday.setHours(0, 0, 0, 0);
    comingSunday.setHours(23, 59, 59, 999);
    let filter = {
      and: [
        { startDateTime: { ge: comingSaturday } },
        { startDateTime: { le: comingSunday } },
      ],
    };
    console.log("comingSaturday", comingSaturday);
    console.log("comingSunday", comingSunday);
    console.log("filter in else if", filter);

    let events = await eventsData(apolloClient, filter);
    return events;
  }
}

async function entityEventFun(apolloClient, userId) {
  let filter = { startDateTime: { ge: new Date() } };
  let events = await eventsData(apolloClient, filter);

  const associatedData = await getAssociatedData(
    userId,
    apolloClient,
    "organizations"
  );
  const organizationIds = [
    ...new Set(associatedData.map((organization) => organization.id)),
  ];

  const organizationIdsSet = new Set(organizationIds);
  const filteredEventData = events.filter((ex) =>
    ex?.EventsOrganizations?.items?.some((ey) =>
      organizationIdsSet.has(ey.organizationsID)
    )
  );
  console.log("filteredEventData", filteredEventData);
  return filteredEventData;
}

async function eventEntityCategoryInfoFun(apolloClient, category, intent) {
  if (category) {
    let filter =
      intent !== "categoryData" ? { startDateTime: { ge: new Date() } } : {};
    let events = await eventsData(apolloClient, filter);

    const data = events.reduce(
      (acc, ex) => {
        if (ex.type === category && ex?.EventsOrganizations?.items) {
          ex?.EventsOrganizations.items.forEach((ey) => {
            if (!acc.orgIds.includes(ey.organizations.id)) {
              acc.orgData.push(ey.organizations);
              acc.orgIds.push(ey.organizations.id);
            }
          });
        }
        return acc;
      },
      { orgData: [], orgIds: [] }
    );

    return data?.orgData;
  }
  return [];
}

function respondWithCommunityEventInfo(eventsData, question) {
  if (eventsData.length > 0) {
    let data = eventsData
      .map((ex) => `● ${ex.name} ( ${ex.shortDescription} ) \n`)
      .toString();
    return {
      User: question,
      Assistant: `Yes, please find the events listed below: \n\n${replaceAll(
        data,
        ",",
        ""
      )}`,
    };
  }
  return {
    User: question,
    Assistant: `There are no upcoming events.`,
  };
}

function respondWithWeekendEventInfo(eventsData, question, intent) {
  // console.log("eventsData", eventsData)
  switch (intent) {
    case "EventsLastWeekendIntent":
      if (eventsData.length > 0) {
        let eventOrgs = {};
        eventsData.map((ex) => {
          eventOrgs[ex.name] = ex.EventsOrganizations.items
            .map((e) => e.organizations)
            .flat();
        });
        let response = Object.keys(eventOrgs)
          .map((eventName) => {
            let hosts = eventOrgs[eventName].map((org) => org.name).join(", ");
            let eventDetails = eventsData.find(
              (event) => event.name === eventName
            );
            let description = eventDetails
              ? eventDetails.shortDescription
              : "Description not available";

            //converting date to UTC and then returning
            let startDT = new Date(eventDetails.startDateTime);

            eventDetails.startDateTime = startDT.setMinutes(
              startDT.getMinutes() + startDT.getTimezoneOffset()
            );
            const date = new Date(eventDetails.startDateTime);
            const startDateUtc = new Date(
              date.getTime() - date.getTimezoneOffset() * 60000
            );

            let startTime = eventDetails
              ? startDateUtc.toISOString().split("T")[1].split(".")[0]
              : "Time not available";
            let showDate = eventDetails
              ? startDateUtc.toISOString().split("T")[0]
              : "Date not available";
            ////

            let address =
              eventDetails.structure !== "virtual"
                ? eventDetails.streetAddress1
                : "Virtual";
            return `${eventName} - ${description}\n${hosts}\n${showDate} ${startTime}\n${address}\n`;
          })
          .join("\n\n"); // Added double newline to separate each event
        return {
          User: question,
          Assistant: `Yes, please find the events listed below: \n\n${response}`,
        };
      }
      break;
    case "EventsThisWeekendIntent":
      if (eventsData.length > 0) {
        let eventOrgs = {};
        eventsData.map((ex) => {
          eventOrgs[ex.name] = ex.EventsOrganizations.items
            .map((e) => e.organizations)
            .flat();
        });

        let response = Object.keys(eventOrgs)
          .map((eventName) => {
            let hosts = eventOrgs[eventName].map((org) => org.name).join(", ");
            let eventDetails = eventsData.find(
              (event) => event.name === eventName
            );
            let description = eventDetails
              ? eventDetails.shortDescription
              : "Description not available";

            //converting date to UTC and then returning
            let startDT = new Date(eventDetails.startDateTime);

            eventDetails.startDateTime = startDT.setMinutes(
              startDT.getMinutes() + startDT.getTimezoneOffset()
            );
            const date = new Date(eventDetails.startDateTime);
            const startDateUtc = new Date(
              date.getTime() - date.getTimezoneOffset() * 60000
            );

            let startTime = eventDetails
              ? startDateUtc.toISOString().split("T")[1].split(".")[0]
              : "Time not available";
            let showDate = eventDetails
              ? startDateUtc.toISOString().split("T")[0]
              : "Date not available";
            ////

            let address =
              eventDetails.structure !== "virtual"
                ? eventDetails.streetAddress1
                : "Virtual";
            return `${eventName} - ${description}\n${hosts}\n${showDate} ${startTime}\n${address}\n`;
          })
          .join("\n\n"); // Added double newline to separate each event
        return {
          User: question,
          Assistant: `Yes, please find the events listed below: \n\n${response}`,
        };
      }
      break;
    case "LastWeekendEventOrganizationsIntent":
      if (eventsData.length > 0) {
        let eventData = {};
        eventsData.map((ex) => {
          eventData[ex.name] = ex.EventsOrganizations.items
            .map((e) => e.organizations)
            .flat();
        });

        let response = Object.keys(eventData)
          .map((eventName) => {
            let hosts = eventData[eventName].map((org) => org.name).join(", ");
            let eventDetails = eventsData.find(
              (event) => event.name === eventName
            );
            let description = eventDetails
              ? eventDetails.shortDescription
              : "Description not available";

            //converting date to UTC and then returning
            let startDT = new Date(eventDetails.startDateTime);

            eventDetails.startDateTime = startDT.setMinutes(
              startDT.getMinutes() + startDT.getTimezoneOffset()
            );
            const date = new Date(eventDetails.startDateTime);
            const startDateUtc = new Date(
              date.getTime() - date.getTimezoneOffset() * 60000
            );

            let startTime = eventDetails
              ? startDateUtc.toISOString().split("T")[1].split(".")[0]
              : "Time not available";
            let showDate = eventDetails
              ? startDateUtc.toISOString().split("T")[0]
              : "Date not available";
            ////

            let address =
              eventDetails.structure !== "virtual"
                ? eventDetails.streetAddress1
                : "Virtual";
            return `${eventName} - ${description}\n${hosts}\n${showDate} ${startTime}\n${address}\n`;
          })
          .join("\n\n"); // Added double newline to separate each event

        return {
          User: question,
          Assistant: `Yes, please find the details listed below: \n\n${response}`,
        };
      }
      break;
    case "ThisWeekendEventOrganizationsIntent":
      if (eventsData.length > 0) {
        let eventData = {};
        eventsData.map((ex) => {
          eventData[ex.name] = ex.EventsOrganizations.items
            .map((e) => e.organizations)
            .flat();
        });

        let response = Object.keys(eventData)
          .map((eventName) => {
            let hosts = eventData[eventName].map((org) => org.name).join(", ");
            let eventDetails = eventsData.find(
              (event) => event.name === eventName
            );
            let description = eventDetails
              ? eventDetails.shortDescription
              : "Description not available";

            //converting date to UTC and then returning
            let startDT = new Date(eventDetails.startDateTime);

            eventDetails.startDateTime = startDT.setMinutes(
              startDT.getMinutes() + startDT.getTimezoneOffset()
            );
            const date = new Date(eventDetails.startDateTime);
            const startDateUtc = new Date(
              date.getTime() - date.getTimezoneOffset() * 60000
            );

            let startTime = eventDetails
              ? startDateUtc.toISOString().split("T")[1].split(".")[0]
              : "Time not available";
            let showDate = eventDetails
              ? startDateUtc.toISOString().split("T")[0]
              : "Date not available";
            ////

            let address =
              eventDetails.structure !== "virtual"
                ? eventDetails.streetAddress1
                : "Virtual";
            return `${eventName} - ${description}\n${hosts}\n${showDate} ${startTime}\n${address}\n`;
          })
          .join("\n\n"); // Added double newline to separate each event

        return {
          User: question,
          Assistant: `Yes, please find the details listed below: \n\n${response}`,
        };
      }
      break;
    default:
      break;
  }

  return {
    User: question,
    Assistant:
      intent === "EventsThisWeekendIntent" ||
      intent === "ThisWeekendEventOrganizationsIntent"
        ? `There are no events on this weekend.`
        : `No events were held last weekend.`,
  };
}

function respondWithEventCategoryInfo(eventsData, question, eventCategory) {
  if (eventsData.length > 0) {
    let data = eventsData
      .map((ex) => `● ${ex.name} ( ${ex.shortDescription} ) \n`)
      .toString();
    return {
      User: question,
      Assistant: `Please find the ongoing ${eventCategory} events listed below: \n\n${replaceAll(
        data,
        ",",
        ""
      )}`,
    };
  }
  return {
    User: question,
    Assistant: `There are no upcoming events.`,
  };
}

function respondWithDateEventInfo(eventsData, question) {
  if (eventsData.length > 0) {
    let data = eventsData
      .map((ex) => `● ${ex.name} ( ${ex.shortDescription} ) \n`)
      .toString();
    return {
      User: question,
      Assistant: `Sure, please find the events listed below: \n\n${replaceAll(
        data,
        ",",
        ""
      )}`,
    };
  }
  return {
    User: question,
    Assistant: `There are no such events found.`,
  };
}

function respondWithEntityEvent(eventsData, question) {
  if (eventsData.length > 0) {
    let data = eventsData
      .map((ex) => `● ${ex.name} ( ${ex.shortDescription} ) \n`)
      .toString();
    return {
      User: question,
      Assistant: `Please find the list of upcoming events of your organization below: \n\n${replaceAll(
        data,
        ",",
        ""
      )}`,
    };
  }
  return {
    User: question,
    Assistant: `There are no such events found.`,
  };
}

function respondWithEventEntityCategory(orgData, question, eventCategory) {
  if (orgData.length > 0) {
    let data = orgData.map((ex) => `● ${ex.name} \n`).toString();
    return {
      User: question,
      Assistant: `The organizations having events of ${eventCategory} category are listed below: \n\n${replaceAll(
        data,
        ",",
        ""
      )}`,
    };
  }
  return {
    User: question,
    Assistant: `There are no organizations having events of category ${eventCategory}.`,
  };
}

async function createTranscriptDetail(transcription, apolloClient, summaryAndMetadata) {
  let transcriptDetail = transcription.transcriptDetail || {};
  if (typeof transcriptDetail === "string") {
    try {
      transcriptDetail = JSON.parse(transcriptDetail);
    } catch (error) {
      console.error(error);
      transcriptDetail = {};
    }
  }
  let summary = "";
  let metadata = {};
  if (summaryAndMetadata) {
    summary = summaryAndMetadata.summary;
    metadata = summaryAndMetadata.metadata;
  } else {
    const userMetadata = {};
    const result = await summarizeAndExtractMetadata(transcription.transcription, userMetadata);
    summary = result.summary;
    metadata = result.metadata;
  }
  transcriptDetail.metadata = metadata;

  try {
    const transcriptDetailInput = {
      jobName: transcriptDetail.jobName,
      accountId: transcriptDetail.accountId,
      status: transcriptDetail.status,
      results: transcriptDetail.results ? JSON.stringify(transcriptDetail.results) : null,
      metadata: transcriptDetail.metadata ? JSON.stringify(transcriptDetail.metadata) : null,
      transcription: transcription.transcription,
      summary,
      createdAt: new Date().toISOString(),
    };
    const transcriptDetailResult = await apolloClient.mutate({
      mutation: CREATE_TRANSCRIPT_DETAIL,
      variables: { input: transcriptDetailInput },
    });
    return transcriptDetailResult?.data?.createTranscriptDetail?.id || null;
  } catch (error) {
    console.error("Error creating TranscriptDetail: ", error);
    return null;
  }
}

async function storeInKnowledgeRepository(id, transcription, apolloClient) {
  console.log("transcription: ", transcription);
  console.log("id: ", id);

  // Assume userMetadata is available or fetched elsewhere; for now, use empty object
  const userMetadata = {};
  // Call Bedrock for summary and metadata
  const { summary, metadata } = await summarizeAndExtractMetadata(
    transcription.transcription,
    userMetadata
  );

  // Prepare transcriptDetail fields
  let transcriptDetail = transcription.transcriptDetail || {};
  if (typeof transcriptDetail === "string") {
    try {
      transcriptDetail = JSON.parse(transcriptDetail);
    } catch (error) {
      console.error(error);
      transcriptDetail = {};
    }
  }
  transcriptDetail.metadata = metadata;

  // 1. Create TranscriptDetail record via GraphQL mutation
  let transcriptDetailId = await createTranscriptDetail(transcription, apolloClient, { summary, metadata });
  if (!transcriptDetailId) {
    return;
  }

  // 2. Update KnowledgeRepositoryStore with reference to TranscriptDetail
  const variables = {
    input: {
      id,
      transcriptDetailId,
    },
  };
  console.log("variables: ", variables);

  try {
    const result = await apolloClient.mutate({
      mutation: UPDATE_KNOWLEDGE_REPOSITORY_STORE,
      variables,
    });
    console.log({ result });
  } catch (error) {
    console.error("Error updating knowledge repository store: ", error);
  }
}

async function storeInSubmission(id, transcription, apolloClient) {
  console.log("transcription: ", transcription);
  console.log("id: ", id);

  // Assume userMetadata is available or fetched elsewhere; for now, use empty object
  const userMetadata = {};
  // Call Bedrock for summary and metadata
  const { summary, metadata } = await summarizeAndExtractMetadata(
    transcription.transcription,
    userMetadata
  );

  // Prepare transcriptDetail fields
  let transcriptDetail = transcription.transcriptDetail || {};
  if (typeof transcriptDetail === "string") {
    try {
      transcriptDetail = JSON.parse(transcriptDetail);
    } catch (error) {
      console.error(error);
      transcriptDetail = {};
    }
  }
  transcriptDetail.metadata = metadata;

  // 1. Create TranscriptDetail record via GraphQL mutation
  let transcriptDetailId = await createTranscriptDetail(transcription, apolloClient, { summary, metadata });
  if (!transcriptDetailId) {
    return;
  }

  // 2. Update Submission with reference to TranscriptDetail
  const variables = {
    input: {
      id,
      transcriptDetailId,
    },
  };
  console.log("variables: ", variables);

  try {
    const result = await apolloClient.mutate({
      mutation: UPDATE_SUBMISSION,
      variables,
    });
    console.log({ result });
  } catch (error) {
    console.error("Error updating submission: ", error);
  }
}

/**
 * Calls Bedrock to summarize transcript and extract metadata.
 * @param {string} transcriptText
 * @param {object} userMetadata
 * @returns {object} { summary, metadata }
 */
async function summarizeAndExtractMetadata(transcriptText, userMetadata) {
  // Prompt for Bedrock summarization and metadata extraction
  const prompt = `### Summarize the transcript in 4-5 key points. Then extract structured metadata as JSON with fields: region, country, theme, time_period, tone, submitted_by, submitted_village, submitted_timestamp, shared_by, source_type, linked_files, mvp_domains, skills_tags, reviewed, permissions (public_use, api_access, ai_training).\n\nTranscript: ${transcriptText}\n\nUser Metadata: ${JSON.stringify(
    userMetadata
  )}`;
  const response = await respondWithBedrockChatBot(prompt);
  // Try to parse JSON from response (if present)
  let summary = "",
    metadata = {};
  try {
    const match = response.match(/\{[\s\S]*\}/);
    if (match) {
      metadata = JSON.parse(match[0]);
      summary = response.replace(match[0], "").trim();
    } else {
      summary = response;
    }
  } catch (error) {
    console.error(error);
    summary = response;
  }
  return { summary, metadata };
}
