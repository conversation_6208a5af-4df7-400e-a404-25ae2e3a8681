const { getChatHistoryPrompt } = require('../../shared/utils/chatHistory');
const { createLogger } = require('../../shared/utils/logger');
const { formatPromptData } = require('./formatPromptData');

const logger = createLogger('knowledgeHandler');

/**
 * Get the knowledge chatbot prompt
 * @param {string} question - The user's question
 * @param {Object} promptData - The data to use for generating the prompt
 * @param {string} userId - The ID of the user
 * @param {Object} apolloClient - The Apollo Client instance
 * @param {string} [chatType] - Optional chat type for filtering chat history
 * @returns {Promise<string>} - The generated prompt
 */
async function getKnowledgePrompt(
  question,
  promptData,
  userId,
  apolloClient,
  chatType = 'Knowledge'
) {
  // Log the raw prompt data for debugging
  console.log('=== RAW KNOWLEDGE PROMPT DATA ===');
  console.log('Type of promptData:', typeof promptData);
  console.log(
    'Keys in promptData:',
    promptData ? Object.keys(promptData) : 'promptData is null/undefined'
  );
  console.log('Full promptData:', JSON.stringify(promptData, null, 2));
  console.log('=== END RAW KNOWLEDGE PROMPT DATA ===');

  // Extract chat history and knowledge data
  const { chatHistory: chatHistoryData, ...knowledgeData } = promptData || {};

  // Log the extracted data
  console.log('=== EXTRACTED KNOWLEDGE DATA ===');
  console.log(
    'chatHistoryData length:',
    chatHistoryData ? chatHistoryData.length : 'No chat history'
  );
  console.log('knowledgeData keys:', Object.keys(knowledgeData));
  console.log('knowledgeData content:', JSON.stringify(knowledgeData, null, 2));
  console.log('=== END EXTRACTED KNOWLEDGE DATA ===');

  // Format the knowledge data using the shared formatter
  const formattedData = formatPromptData('knowledge', knowledgeData, userId);

  // Get chat history if apolloClient is available
  const chatHistory = apolloClient
    ? await getChatHistoryPrompt(apolloClient, userId, chatType)
    : '';

  logger.debug('Retrieved chat history for knowledge chat', {
    chatType,
    hasChatHistory: !!chatHistory,
    chatHistoryLength: chatHistory?.length || 0,
  });

  return `${
    chatHistory
      ? `### CRITICAL CONTEXT INSTRUCTION - READ THIS FIRST:
If the user's current question is "${question}" and it appears ambiguous or contains references like "which one?", "tell me more about it", "what about that?", "the submission", "the knowledge item", etc., you MUST:
1. IGNORE the generic greeting response rule completely for this question
2. Look at the conversation history below to find what specific items (knowledge items, submissions) were mentioned
3. Provide detailed information about those specific items
4. DO NOT ask for clarification when the context is available in the conversation history

${chatHistory}

`
      : ''
  }
  Hello, 
  You are a strict, rule-based assistant for the MyVillage knowledge app. Follow every instruction carefully. Never guess or assume 
  Below is the knowledge base information for the MyVillage app for the user with id ${userId}:

  ### Knowledge Base Information:
  
  **1. General Information**  
  - General knowledge and information about the MyVillage platform.
  - Platform features and how to use them.
  
  **2. Help Articles**  
  - Frequently asked questions and their answers.
  - Step-by-step guides for common tasks.
  
  **3. Tutorials**  
  - Video and text tutorials for different features.
  - Best practices and tips.
  
  **4. Policies**  
  - Community guidelines and policies.
  - Terms of service and privacy policy highlights.
  
  ### Important Rules:
  1. **DO NOT fabricate answers.** Use only the provided data to respond.
  2. For greetings, respond with: "Hello! How can I assist you with MyVillage today?"
  3. If you don't know the answer, direct the user to the appropriate help section.
  4. Keep responses informative and helpful.
  5. For technical issues, provide clear, step-by-step guidance.
  
  **User Question:**  
  **${question}**  

  Here is the knowledge base data to use for generating the answer:

  ${formattedData}`;
}

module.exports = {
  getKnowledgePrompt,
};
