{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"Windows\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.0.0\",\"stackType\":\"function-Lambda\",\"metadata\":{}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "rpcUrl": {"Type": "String"}, "privateKey": {"Type": "String"}, "mvtContractAddress": {"Type": "String"}, "mvtWithdrawContractAddress": {"Type": "String"}, "mvtUsdcContractAddress": {"Type": "String"}, "etherscanApiKey": {"Type": "String"}, "stripeSecretKey": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "mvtToken", {"Fn::Join": ["", ["mvtToken", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "RPC_URL": {"Ref": "rpcUrl"}, "PRIVATE_KEY": {"Ref": "privateKey"}, "MVT_CONTRACT_ADDRESS": {"Ref": "mvtContractAddress"}, "MVT_WITHDRAW_CONTRACT_ADDRESS": {"Ref": "mvtWithdrawContractAddress"}, "MVT_USDC_CONTRACT_ADDRESS": {"Ref": "mvtUsdcContractAddress"}, "ETHERSCAN_API_KEY": {"Ref": "etherscanApiKey"}, "STRIPE_SECRET_KEY": {"Ref": "stripeSecretKey"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "Layers": [], "Timeout": 25}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "myvillageprojectadmiLambdaRole8fde9ff0", {"Fn::Join": ["", ["myvillageprojectadmiLambdaRole8fde9ff0", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}